{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\student\\\\ReviewSteps.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport Navbar from './Navbar';\nimport Sidebar from './Sidebar';\nimport PatientNav from './PatientNav';\nimport Loader from '../components/Loader';\nimport { FaCheck, FaUser, FaCheckCircle, FaTimesCircle, FaSave, FaSpinner } from 'react-icons/fa';\n\n// CSS for spinner animation\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst spinnerAnimation = `\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n  .animate-spin {\n    animation: spin 1s linear infinite;\n  }\n`;\nconst ReviewSteps = () => {\n  _s();\n  const {\n    nationalId\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [patientData, setPatientData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(false);\n  const [reviewData, setReviewData] = useState({\n    patientId: {\n      nationalId: '',\n      fullName: ''\n    },\n    procedureType: '',\n    chartId: '',\n    note: '',\n    reviewSteps: [],\n    subType: '' // For Removable Prosthodontics, Fixed Prosthodontics, or Operative subtypes\n  });\n\n  // Get the selected sheet type from localStorage (set by PatientNav)\n  useEffect(() => {\n    const savedSheetType = localStorage.getItem('selectedSheetType');\n    if (savedSheetType) {\n      setReviewData(prev => ({\n        ...prev,\n        procedureType: savedSheetType\n      }));\n\n      // Fetch review steps for this procedure type\n      fetchReviewSteps(savedSheetType);\n    }\n  }, []);\n\n  // Listen for changes to the selected sheet type in localStorage\n  useEffect(() => {\n    const handleStorageChange = () => {\n      const savedSheetType = localStorage.getItem('selectedSheetType');\n      if (savedSheetType && savedSheetType !== reviewData.procedureType) {\n        setReviewData(prev => ({\n          ...prev,\n          procedureType: savedSheetType,\n          reviewSteps: [] // Clear previous steps\n        }));\n\n        // Fetch review steps for this procedure type\n        fetchReviewSteps(savedSheetType);\n      }\n    };\n\n    // Add event listener for storage changes\n    window.addEventListener('storage', handleStorageChange);\n\n    // Clean up\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n    };\n  }, [reviewData.procedureType]);\n  const fetchReviewSteps = async (procedureType, subType = '') => {\n    // Validate procedure type\n    if (!procedureType || procedureType.trim() === '') {\n      setError('Please select a valid procedure type');\n      return;\n    }\n    try {\n      // Show loading state\n      setLoading(true);\n\n      // Add subType as a query parameter if it's provided\n      const url = `http://localhost:5000/api/reviews/steps/${procedureType}${subType ? `?subType=${encodeURIComponent(subType)}` : ''}`;\n      const response = await axios.get(url, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n\n      // Check if we got valid data back\n      if (!response.data || !Array.isArray(response.data) || response.data.length === 0) {\n        setError(`No review steps found for ${procedureType}${subType ? ` (${subType})` : ''}. Please try another procedure type.`);\n        return;\n      }\n\n      // Update state with the fetched steps and subType\n      setReviewData(prev => ({\n        ...prev,\n        reviewSteps: response.data,\n        subType: subType || prev.subType\n      }));\n\n      // Clear any previous errors\n      setError(null);\n    } catch (err) {\n      console.error('Error fetching review steps:', err);\n\n      // Provide more specific error messages based on the error\n      if (err.response) {\n        // The request was made and the server responded with a status code\n        // that falls out of the range of 2xx\n        if (err.response.status === 401) {\n          setError('Your session has expired. Please log in again.');\n        } else if (err.response.status === 404) {\n          setError(`No review steps found for ${procedureType}. Please try another procedure type.`);\n        } else {\n          setError(`Server error: ${err.response.data.message || 'Failed to load review steps'}`);\n        }\n      } else if (err.request) {\n        // The request was made but no response was received\n        setError('Network error. Please check your connection and try again.');\n      } else {\n        // Something happened in setting up the request that triggered an Error\n        setError('Failed to load review steps. Please try again later.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Check if user is logged in and is a student\n        if (!token || !user) {\n          console.log('No token or user found');\n          setError('Please log in to submit a review');\n          navigate('/login');\n          return;\n        }\n        if (user.role !== 'student') {\n          console.log('User is not a student:', user.role);\n          setError('Only students can submit reviews');\n          navigate('/dashboard');\n          return;\n        }\n\n        // Fetch patient data\n        const patientResponse = await axios.get(`http://localhost:5000/api/patients/public/${nationalId}`);\n        setPatientData(patientResponse.data);\n        setReviewData(prev => ({\n          ...prev,\n          patientId: {\n            nationalId: patientResponse.data.nationalId,\n            fullName: patientResponse.data.fullName\n          }\n        }));\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5;\n        console.error('API Error:', {\n          message: err.message,\n          status: (_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status,\n          data: (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data\n        });\n\n        // Handle different error scenarios\n        if (((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 404) {\n          setError(`Patient with ID ${nationalId} not found. Please check the patient ID.`);\n        } else if (((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 401) {\n          setError('Session expired or invalid. Please log in again.');\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          navigate('/login');\n        } else if (((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : _err$response5.status) === 403) {\n          setError('You do not have permission to access this resource.');\n          navigate('/dashboard');\n        } else {\n          var _err$response6, _err$response6$data;\n          setError(((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.message) || 'Failed to load patient data');\n        }\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [nationalId, navigate, user, token]);\n  const handleStepToggle = index => {\n    const updatedSteps = [...reviewData.reviewSteps];\n    updatedSteps[index].completed = !updatedSteps[index].completed;\n    setReviewData(prev => ({\n      ...prev,\n      reviewSteps: updatedSteps\n    }));\n  };\n  const handleNoteChange = e => {\n    setReviewData(prev => ({\n      ...prev,\n      note: e.target.value\n    }));\n  };\n  const handleSubmitReview = async () => {\n    // Validate required fields\n    if (!reviewData.procedureType || reviewData.procedureType.trim() === '') {\n      setError('Please select a procedure type before submitting');\n      return;\n    }\n\n    // Validate subType for Removable Prosthodontics\n    if (reviewData.procedureType === 'Removable Prosthodontics' && !reviewData.subType) {\n      setError('Please select either Complete Denture or Partial Denture before submitting');\n      return;\n    }\n\n    // Validate subType for Fixed Prosthodontics\n    if (reviewData.procedureType === 'Fixed Prosthodontics' && !reviewData.subType) {\n      setError('Please select either Crown Procedure or Bridge Procedure before submitting');\n      return;\n    }\n\n    // Validate subType for Operative\n    if (reviewData.procedureType === 'Operative' && !reviewData.subType) {\n      setError('Please select either Amalgam Restoration or Composite Restoration before submitting');\n      return;\n    }\n    if (!reviewData.reviewSteps || reviewData.reviewSteps.length === 0) {\n      setError('No review steps available. Please select a different procedure type');\n      return;\n    }\n    if (!reviewData.patientId || !reviewData.patientId.nationalId) {\n      setError('Patient information is missing. Please refresh the page and try again');\n      return;\n    }\n\n    // Check if any steps are completed\n    const completedSteps = reviewData.reviewSteps.filter(step => step.completed);\n    if (completedSteps.length === 0) {\n      setError('Please complete at least one review step before submitting');\n      return;\n    }\n    try {\n      // Show loading state\n      setLoading(true);\n\n      // Create a payload with all required fields\n      const payload = {\n        patientId: reviewData.patientId,\n        procedureType: reviewData.procedureType,\n        note: reviewData.note || '',\n        reviewSteps: reviewData.reviewSteps || [],\n        comment: '' // Add empty comment field\n      };\n\n      // Add subType to the payload if it's a Removable Prosthodontics, Fixed Prosthodontics, or Operative review\n      if ((reviewData.procedureType === 'Removable Prosthodontics' || reviewData.procedureType === 'Fixed Prosthodontics' || reviewData.procedureType === 'Operative') && reviewData.subType) {\n        payload.procedureType = `${reviewData.procedureType} - ${reviewData.subType}`;\n      }\n      console.log('Submitting review:', payload);\n      const response = await axios.post('http://localhost:5000/api/reviews', payload, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      console.log('Review submitted:', response.data);\n\n      // Clear any errors and show success message\n      setError(null);\n      setSuccess(true);\n    } catch (err) {\n      console.error('Submit Review Error:', err);\n\n      // More detailed error logging and user-friendly messages\n      if (err.response) {\n        console.error('Response data:', err.response.data);\n        console.error('Response status:', err.response.status);\n        console.error('Response headers:', err.response.headers);\n        if (err.response.status === 401) {\n          setError('Your session has expired. Please log in again.');\n        } else if (err.response.status === 400) {\n          setError(`Validation error: ${err.response.data.message || 'Please check your submission'}`);\n        } else if (err.response.status === 404) {\n          setError('Student information not found. Please log in again.');\n        } else {\n          setError(`Server error: ${err.response.data.message || 'Failed to submit review'}`);\n        }\n      } else if (err.request) {\n        console.error('Request made but no response received:', err.request);\n        setError('Network error. Please check your connection and try again.');\n      } else {\n        console.error('Error setting up request:', err.message);\n        setError('Failed to submit review. Please try again later.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Animation variants\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Only show full-page error for critical errors that prevent the component from functioning\n  if (error && (error.includes('session') || error.includes('log in') || error.includes('permission'))) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n        isOpen: sidebarOpen,\n        setIsOpen: setSidebarOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {\n          toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              scale: 0.9,\n              opacity: 0\n            },\n            animate: {\n              scale: 1,\n              opacity: 1\n            },\n            className: \"text-center max-w-md bg-white p-8 rounded-xl shadow-sm border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-[#0077B6] mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-12 w-12 mx-auto\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-bold text-gray-900 mb-2\",\n              children: \"Authentication Error\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-6\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => navigate('/login'),\n              className: \"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium shadow-md\",\n              children: \"Go to Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: spinnerAnimation\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Sidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PatientNav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            variants: container,\n            initial: \"hidden\",\n            animate: \"show\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-3xl font-bold text-[#0077B6]\",\n                children: \"Review Steps\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [new Date().toLocaleDateString(), \" \", new Date().toLocaleTimeString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this), error && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: -10\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              className: \"bg-red-50 border-l-4 border-red-400 p-4 mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"h-5 w-5 text-red-400\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    viewBox: \"0 0 20 20\",\n                    fill: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 405,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-[#333333]\",\n                    children: error\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-auto pl-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"-mx-1.5 -my-1.5\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setError(null),\n                      className: \"inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"h-4 w-4\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 20 20\",\n                        fill: \"currentColor\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          fillRule: \"evenodd\",\n                          d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                          clipRule: \"evenodd\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 422,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 421,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 17\n            }, this), success ? /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              className: \"bg-[#28A745]/10 p-6 rounded-xl shadow-sm mb-8 border border-[#28A745]/20 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                className: \"h-12 w-12 text-[#28A745] mx-auto mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-2\",\n                children: \"Review Submitted Successfully\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-2\",\n                children: [\"Your \", reviewData.procedureType, \" review has been saved successfully.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-6\",\n                children: \"It will be available in your history and sent for supervisor approval.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-center gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => {\n                    // Reset form and fetch new steps\n                    setReviewData({\n                      patientId: {\n                        nationalId: patientData.nationalId,\n                        fullName: patientData.fullName\n                      },\n                      procedureType: reviewData.procedureType,\n                      chartId: '',\n                      note: '',\n                      reviewSteps: []\n                    });\n                    fetchReviewSteps(reviewData.procedureType);\n                    setSuccess(false);\n                  },\n                  className: \"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90\",\n                  children: \"Submit Another Review\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => navigate(`/patientprofile/${nationalId}`),\n                  className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50\",\n                  children: \"Back to Patient Profile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                className: \"bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Patient Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-semibold text-gray-900\",\n                      children: (patientData === null || patientData === void 0 ? void 0 : patientData.fullName) || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"National ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-semibold text-gray-900\",\n                      children: (patientData === null || patientData === void 0 ? void 0 : patientData.nationalId) || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 493,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Student Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 496,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-semibold text-gray-900\",\n                      children: (user === null || user === void 0 ? void 0 : user.name) || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 497,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Student ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 500,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-semibold text-gray-900\",\n                      children: (user === null || user === void 0 ? void 0 : user.studentId) || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 501,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                className: \"bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-[#0077B6] mb-4\",\n                  children: \"Select Procedure Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                    value: reviewData.procedureType || '',\n                    onChange: e => {\n                      const newProcedureType = e.target.value;\n                      if (!newProcedureType) {\n                        // Handle empty selection\n                        setError('Please select a valid procedure type');\n                        return;\n                      }\n\n                      // Clear any previous errors\n                      setError(null);\n\n                      // Update localStorage\n                      localStorage.setItem('selectedSheetType', newProcedureType);\n\n                      // Update state\n                      setReviewData(prev => ({\n                        ...prev,\n                        procedureType: newProcedureType,\n                        reviewSteps: [] // Clear previous steps\n                      }));\n\n                      // Fetch new steps\n                      fetchReviewSteps(newProcedureType);\n                    },\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] appearance-none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select a procedure type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 545,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Operative\",\n                      children: \"Operative\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 546,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Fixed Prosthodontics\",\n                      children: \"Fixed Prosthodontics\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 547,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Removable Prosthodontics\",\n                      children: \"Removable Prosthodontics\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 548,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Endodontics\",\n                      children: \"Endodontics\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 549,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Periodontics\",\n                      children: \"Periodontics\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 550,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"fill-current h-4 w-4\",\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 554,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 553,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 19\n              }, this), reviewData.procedureType === 'Removable Prosthodontics' && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                className: \"bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-[#0077B6] mb-4\",\n                  children: \"Select Denture Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      const subType = 'Complete Denture';\n                      setReviewData(prev => ({\n                        ...prev,\n                        subType\n                      }));\n                      fetchReviewSteps(reviewData.procedureType, subType);\n                    },\n                    className: `p-4 border rounded-lg flex flex-col items-center transition-all ${reviewData.subType === 'Complete Denture' ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md' : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg font-medium mb-2\",\n                      children: \"Complete Denture\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 583,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-600 text-center\",\n                      children: \"Full denture evaluation for edentulous patients\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 584,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      const subType = 'Partial Denture';\n                      setReviewData(prev => ({\n                        ...prev,\n                        subType\n                      }));\n                      fetchReviewSteps(reviewData.procedureType, subType);\n                    },\n                    className: `p-4 border rounded-lg flex flex-col items-center transition-all ${reviewData.subType === 'Partial Denture' ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md' : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg font-medium mb-2\",\n                      children: \"Partial Denture\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 600,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-600 text-center\",\n                      children: \"Partial denture evaluation for partially edentulous patients\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 601,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 588,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 21\n              }, this), reviewData.procedureType === 'Fixed Prosthodontics' && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                className: \"bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-[#0077B6] mb-4\",\n                  children: \"Select Procedure Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      const subType = 'Crown Procedure';\n                      setReviewData(prev => ({\n                        ...prev,\n                        subType\n                      }));\n                      fetchReviewSteps(reviewData.procedureType, subType);\n                    },\n                    className: `p-4 border rounded-lg flex flex-col items-center transition-all ${reviewData.subType === 'Crown Procedure' ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md' : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg font-medium mb-2\",\n                      children: \"Crown Procedure\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 632,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-600 text-center\",\n                      children: \"Single crown preparation and placement\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 633,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 620,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      const subType = 'Bridge Procedure';\n                      setReviewData(prev => ({\n                        ...prev,\n                        subType\n                      }));\n                      fetchReviewSteps(reviewData.procedureType, subType);\n                    },\n                    className: `p-4 border rounded-lg flex flex-col items-center transition-all ${reviewData.subType === 'Bridge Procedure' ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md' : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg font-medium mb-2\",\n                      children: \"Bridge Procedure\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 649,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-600 text-center\",\n                      children: \"Bridge preparation and placement for multiple teeth\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 650,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 637,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 21\n              }, this), reviewData.procedureType === 'Operative' && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                className: \"bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-[#0077B6] mb-4\",\n                  children: \"Select Restoration Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      const subType = 'Amalgam Restoration';\n                      setReviewData(prev => ({\n                        ...prev,\n                        subType\n                      }));\n                      fetchReviewSteps(reviewData.procedureType, subType);\n                    },\n                    className: `p-4 border rounded-lg flex flex-col items-center transition-all ${reviewData.subType === 'Amalgam Restoration' ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md' : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg font-medium mb-2\",\n                      children: \"Amalgam Restoration\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 681,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-600 text-center\",\n                      children: \"Metallic restoration for posterior teeth\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 682,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 669,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      const subType = 'Composite Restoration';\n                      setReviewData(prev => ({\n                        ...prev,\n                        subType\n                      }));\n                      fetchReviewSteps(reviewData.procedureType, subType);\n                    },\n                    className: `p-4 border rounded-lg flex flex-col items-center transition-all ${reviewData.subType === 'Composite Restoration' ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md' : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg font-medium mb-2\",\n                      children: \"Composite Restoration\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 698,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-600 text-center\",\n                      children: \"Tooth-colored restoration for anterior or posterior teeth\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 699,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 686,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                className: \"bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold text-[#0077B6]\",\n                    children: (reviewData.procedureType === 'Removable Prosthodontics' || reviewData.procedureType === 'Fixed Prosthodontics' || reviewData.procedureType === 'Operative') && reviewData.subType ? `${reviewData.procedureType}: ${reviewData.subType} Evaluation` : `${reviewData.procedureType || 'Procedure'} Review Steps`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 714,\n                    columnNumber: 23\n                  }, this), reviewData.reviewSteps && reviewData.reviewSteps.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gray-200 rounded-full h-2.5 w-32 mr-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-[#0077B6] h-2.5 rounded-full\",\n                        style: {\n                          width: `${reviewData.reviewSteps.filter(step => step.completed).length / reviewData.reviewSteps.length * 100}%`\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 725,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 724,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-600\",\n                      children: [reviewData.reviewSteps.filter(step => step.completed).length, \"/\", reviewData.reviewSteps.length, \" completed\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 732,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 723,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 713,\n                  columnNumber: 21\n                }, this), !reviewData.procedureType ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-yellow-50 border-l-4 border-yellow-400 p-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"h-5 w-5 text-yellow-400\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 20 20\",\n                        fill: \"currentColor\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          fillRule: \"evenodd\",\n                          d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                          clipRule: \"evenodd\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 749,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 743,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 742,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"ml-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-yellow-700\",\n                        children: \"Please select a procedure type from the dropdown above.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 757,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 756,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 741,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 23\n                }, this) : reviewData.procedureType === 'Removable Prosthodontics' && !reviewData.subType ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-yellow-50 border-l-4 border-yellow-400 p-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"h-5 w-5 text-yellow-400\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 20 20\",\n                        fill: \"currentColor\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          fillRule: \"evenodd\",\n                          d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                          clipRule: \"evenodd\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 773,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 767,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 766,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"ml-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-yellow-700\",\n                        children: \"Please select either Complete Denture or Partial Denture from the options above.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 781,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 780,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 765,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 764,\n                  columnNumber: 23\n                }, this) : reviewData.procedureType === 'Fixed Prosthodontics' && !reviewData.subType ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-yellow-50 border-l-4 border-yellow-400 p-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"h-5 w-5 text-yellow-400\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 20 20\",\n                        fill: \"currentColor\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          fillRule: \"evenodd\",\n                          d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                          clipRule: \"evenodd\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 797,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 791,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 790,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"ml-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-yellow-700\",\n                        children: \"Please select either Crown Procedure or Bridge Procedure from the options above.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 805,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 804,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 789,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 788,\n                  columnNumber: 23\n                }, this) : reviewData.procedureType === 'Operative' && !reviewData.subType ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-yellow-50 border-l-4 border-yellow-400 p-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"h-5 w-5 text-yellow-400\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 20 20\",\n                        fill: \"currentColor\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          fillRule: \"evenodd\",\n                          d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                          clipRule: \"evenodd\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 821,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 815,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 814,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"ml-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-yellow-700\",\n                        children: \"Please select either Amalgam Restoration or Composite Restoration from the options above.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 829,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 828,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 813,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 812,\n                  columnNumber: 23\n                }, this) : reviewData.reviewSteps && reviewData.reviewSteps.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-lg border border-gray-200 overflow-hidden\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"divide-y divide-gray-200\",\n                    children: reviewData.reviewSteps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `p-4 flex items-center justify-between transition-colors ${step.completed ? 'bg-orange-50' : 'hover:bg-gray-50'}`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-medium text-gray-900\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"inline-block w-6 text-center mr-2 text-blue-600 font-bold\",\n                            children: [index + 1, \".\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 847,\n                            columnNumber: 35\n                          }, this), step.description]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 846,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 845,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleStepToggle(index),\n                        className: `flex items-center justify-center w-20 h-8 rounded-full transition-colors ${step.completed ? 'bg-orange-500 text-white' : 'bg-gray-200 text-gray-500'}`,\n                        children: step.completed ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                            className: \"h-4 w-4 mr-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 861,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-xs font-medium\",\n                            children: \"Pending\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 862,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n                            className: \"h-4 w-4 mr-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 866,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-xs font-medium\",\n                            children: \"Not Done\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 867,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 851,\n                        columnNumber: 31\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 839,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 837,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-yellow-50 border-l-4 border-yellow-400 p-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"h-5 w-5 text-yellow-400\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 20 20\",\n                        fill: \"currentColor\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          fillRule: \"evenodd\",\n                          d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                          clipRule: \"evenodd\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 885,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 879,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 878,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"ml-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-yellow-700\",\n                        children: \"No review steps available for this procedure type.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 893,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 892,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 877,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 876,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 708,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                className: \"bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-[#0077B6] mb-4\",\n                  children: \"Additional Notes (Optional)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 908,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  placeholder: \"Add any additional notes about the review steps\",\n                  value: reviewData.note,\n                  onChange: handleNoteChange,\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6]\",\n                  rows: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 909,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 903,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                className: \"flex justify-end\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: loading ? {} : {\n                    scale: 1.05\n                  },\n                  whileTap: loading ? {} : {\n                    scale: 0.95\n                  },\n                  onClick: handleSubmitReview,\n                  disabled: loading || !reviewData.procedureType || reviewData.reviewSteps.length === 0 || reviewData.procedureType === 'Removable Prosthodontics' && !reviewData.subType || reviewData.procedureType === 'Fixed Prosthodontics' && !reviewData.subType || reviewData.procedureType === 'Operative' && !reviewData.subType,\n                  className: `px-6 py-3 rounded-lg flex items-center ${loading ? 'bg-[#0077B6]/60 text-white cursor-wait' : !reviewData.procedureType || reviewData.reviewSteps.length === 0 || reviewData.procedureType === 'Removable Prosthodontics' && !reviewData.subType || reviewData.procedureType === 'Fixed Prosthodontics' && !reviewData.subType || reviewData.procedureType === 'Operative' && !reviewData.subType ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white shadow-md hover:shadow-lg'}`,\n                  children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(FaSpinner, {\n                      className: \"h-5 w-5 mr-2 animate-spin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 950,\n                      columnNumber: 27\n                    }, this), \"Submitting...\"]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(FaSave, {\n                      className: \"h-5 w-5 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 955,\n                      columnNumber: 27\n                    }, this), \"Submit Review\"]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 924,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 919,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 373,\n    columnNumber: 5\n  }, this);\n};\n_s(ReviewSteps, \"CukU32MPdyRZnfRr0VDm8zO4TU0=\", false, function () {\n  return [useParams, useNavigate, useAuth];\n});\n_c = ReviewSteps;\nexport default ReviewSteps;\nvar _c;\n$RefreshReg$(_c, \"ReviewSteps\");", "map": {"version": 3, "names": ["useState", "useEffect", "useParams", "useNavigate", "motion", "axios", "useAuth", "<PERSON><PERSON><PERSON>", "Sidebar", "PatientNav", "Loader", "FaCheck", "FaUser", "FaCheckCircle", "FaTimesCircle", "FaSave", "FaSpinner", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "spinnerAnimation", "ReviewSteps", "_s", "nationalId", "navigate", "user", "token", "sidebarOpen", "setSidebarOpen", "patientData", "setPatientData", "loading", "setLoading", "error", "setError", "success", "setSuccess", "reviewData", "setReviewData", "patientId", "fullName", "procedureType", "chartId", "note", "reviewSteps", "subType", "savedSheetType", "localStorage", "getItem", "prev", "fetchReviewSteps", "handleStorageChange", "window", "addEventListener", "removeEventListener", "trim", "url", "encodeURIComponent", "response", "get", "headers", "Authorization", "data", "Array", "isArray", "length", "err", "console", "status", "message", "request", "fetchData", "log", "role", "patientResponse", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "removeItem", "_err$response6", "_err$response6$data", "handleStepToggle", "index", "updatedSteps", "completed", "handleNoteChange", "e", "target", "value", "handleSubmitReview", "completedSteps", "filter", "step", "payload", "comment", "post", "container", "hidden", "opacity", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "includes", "className", "children", "isOpen", "setIsOpen", "toggleSidebar", "div", "initial", "scale", "animate", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "button", "whileHover", "whileTap", "onClick", "variants", "Date", "toLocaleDateString", "toLocaleTimeString", "y", "fillRule", "clipRule", "name", "studentId", "onChange", "newProcedureType", "setItem", "style", "width", "map", "description", "placeholder", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/student/ReviewSteps.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport Navbar from './Navbar';\nimport Sidebar from './Sidebar';\nimport PatientNav from './PatientNav';\nimport Loader from '../components/Loader';\nimport { FaCheck, FaUser, FaCheckCircle, FaTimesCircle, FaSave, FaSpinner } from 'react-icons/fa';\n\n// CSS for spinner animation\nconst spinnerAnimation = `\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n  .animate-spin {\n    animation: spin 1s linear infinite;\n  }\n`;\n\nconst ReviewSteps = () => {\n  const { nationalId } = useParams();\n  const navigate = useNavigate();\n  const { user, token } = useAuth();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [patientData, setPatientData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(false);\n  const [reviewData, setReviewData] = useState({\n    patientId: { nationalId: '', fullName: '' },\n    procedureType: '',\n    chartId: '',\n    note: '',\n    reviewSteps: [],\n    subType: '' // For Removable Prosthodontics, Fixed Prosthodontics, or Operative subtypes\n  });\n\n  // Get the selected sheet type from localStorage (set by PatientNav)\n  useEffect(() => {\n    const savedSheetType = localStorage.getItem('selectedSheetType');\n    if (savedSheetType) {\n      setReviewData(prev => ({\n        ...prev,\n        procedureType: savedSheetType\n      }));\n\n      // Fetch review steps for this procedure type\n      fetchReviewSteps(savedSheetType);\n    }\n  }, []);\n\n  // Listen for changes to the selected sheet type in localStorage\n  useEffect(() => {\n    const handleStorageChange = () => {\n      const savedSheetType = localStorage.getItem('selectedSheetType');\n      if (savedSheetType && savedSheetType !== reviewData.procedureType) {\n        setReviewData(prev => ({\n          ...prev,\n          procedureType: savedSheetType,\n          reviewSteps: [] // Clear previous steps\n        }));\n\n        // Fetch review steps for this procedure type\n        fetchReviewSteps(savedSheetType);\n      }\n    };\n\n    // Add event listener for storage changes\n    window.addEventListener('storage', handleStorageChange);\n\n    // Clean up\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n    };\n  }, [reviewData.procedureType]);\n\n  const fetchReviewSteps = async (procedureType, subType = '') => {\n    // Validate procedure type\n    if (!procedureType || procedureType.trim() === '') {\n      setError('Please select a valid procedure type');\n      return;\n    }\n\n    try {\n      // Show loading state\n      setLoading(true);\n\n      // Add subType as a query parameter if it's provided\n      const url = `http://localhost:5000/api/reviews/steps/${procedureType}${subType ? `?subType=${encodeURIComponent(subType)}` : ''}`;\n\n      const response = await axios.get(url, {\n        headers: { Authorization: `Bearer ${token}` },\n      });\n\n      // Check if we got valid data back\n      if (!response.data || !Array.isArray(response.data) || response.data.length === 0) {\n        setError(`No review steps found for ${procedureType}${subType ? ` (${subType})` : ''}. Please try another procedure type.`);\n        return;\n      }\n\n      // Update state with the fetched steps and subType\n      setReviewData(prev => ({\n        ...prev,\n        reviewSteps: response.data,\n        subType: subType || prev.subType\n      }));\n\n      // Clear any previous errors\n      setError(null);\n    } catch (err) {\n      console.error('Error fetching review steps:', err);\n\n      // Provide more specific error messages based on the error\n      if (err.response) {\n        // The request was made and the server responded with a status code\n        // that falls out of the range of 2xx\n        if (err.response.status === 401) {\n          setError('Your session has expired. Please log in again.');\n        } else if (err.response.status === 404) {\n          setError(`No review steps found for ${procedureType}. Please try another procedure type.`);\n        } else {\n          setError(`Server error: ${err.response.data.message || 'Failed to load review steps'}`);\n        }\n      } else if (err.request) {\n        // The request was made but no response was received\n        setError('Network error. Please check your connection and try again.');\n      } else {\n        // Something happened in setting up the request that triggered an Error\n        setError('Failed to load review steps. Please try again later.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Check if user is logged in and is a student\n        if (!token || !user) {\n          console.log('No token or user found');\n          setError('Please log in to submit a review');\n          navigate('/login');\n          return;\n        }\n\n        if (user.role !== 'student') {\n          console.log('User is not a student:', user.role);\n          setError('Only students can submit reviews');\n          navigate('/dashboard');\n          return;\n        }\n\n        // Fetch patient data\n        const patientResponse = await axios.get(`http://localhost:5000/api/patients/public/${nationalId}`);\n        setPatientData(patientResponse.data);\n        setReviewData((prev) => ({\n          ...prev,\n          patientId: { nationalId: patientResponse.data.nationalId, fullName: patientResponse.data.fullName },\n        }));\n\n        setLoading(false);\n      } catch (err) {\n        console.error('API Error:', {\n          message: err.message,\n          status: err.response?.status,\n          data: err.response?.data,\n        });\n\n        // Handle different error scenarios\n        if (err.response?.status === 404) {\n          setError(`Patient with ID ${nationalId} not found. Please check the patient ID.`);\n        } else if (err.response?.status === 401) {\n          setError('Session expired or invalid. Please log in again.');\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          navigate('/login');\n        } else if (err.response?.status === 403) {\n          setError('You do not have permission to access this resource.');\n          navigate('/dashboard');\n        } else {\n          setError(err.response?.data?.message || 'Failed to load patient data');\n        }\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [nationalId, navigate, user, token]);\n\n  const handleStepToggle = (index) => {\n    const updatedSteps = [...reviewData.reviewSteps];\n    updatedSteps[index].completed = !updatedSteps[index].completed;\n    setReviewData(prev => ({\n      ...prev,\n      reviewSteps: updatedSteps\n    }));\n  };\n\n  const handleNoteChange = (e) => {\n    setReviewData(prev => ({\n      ...prev,\n      note: e.target.value\n    }));\n  };\n\n  const handleSubmitReview = async () => {\n    // Validate required fields\n    if (!reviewData.procedureType || reviewData.procedureType.trim() === '') {\n      setError('Please select a procedure type before submitting');\n      return;\n    }\n\n    // Validate subType for Removable Prosthodontics\n    if (reviewData.procedureType === 'Removable Prosthodontics' && !reviewData.subType) {\n      setError('Please select either Complete Denture or Partial Denture before submitting');\n      return;\n    }\n\n    // Validate subType for Fixed Prosthodontics\n    if (reviewData.procedureType === 'Fixed Prosthodontics' && !reviewData.subType) {\n      setError('Please select either Crown Procedure or Bridge Procedure before submitting');\n      return;\n    }\n\n    // Validate subType for Operative\n    if (reviewData.procedureType === 'Operative' && !reviewData.subType) {\n      setError('Please select either Amalgam Restoration or Composite Restoration before submitting');\n      return;\n    }\n\n    if (!reviewData.reviewSteps || reviewData.reviewSteps.length === 0) {\n      setError('No review steps available. Please select a different procedure type');\n      return;\n    }\n\n    if (!reviewData.patientId || !reviewData.patientId.nationalId) {\n      setError('Patient information is missing. Please refresh the page and try again');\n      return;\n    }\n\n    // Check if any steps are completed\n    const completedSteps = reviewData.reviewSteps.filter(step => step.completed);\n    if (completedSteps.length === 0) {\n      setError('Please complete at least one review step before submitting');\n      return;\n    }\n\n    try {\n      // Show loading state\n      setLoading(true);\n\n      // Create a payload with all required fields\n      const payload = {\n        patientId: reviewData.patientId,\n        procedureType: reviewData.procedureType,\n        note: reviewData.note || '',\n        reviewSteps: reviewData.reviewSteps || [],\n        comment: '', // Add empty comment field\n      };\n\n      // Add subType to the payload if it's a Removable Prosthodontics, Fixed Prosthodontics, or Operative review\n      if ((reviewData.procedureType === 'Removable Prosthodontics' ||\n           reviewData.procedureType === 'Fixed Prosthodontics' ||\n           reviewData.procedureType === 'Operative') && reviewData.subType) {\n        payload.procedureType = `${reviewData.procedureType} - ${reviewData.subType}`;\n      }\n\n      console.log('Submitting review:', payload);\n      const response = await axios.post('http://localhost:5000/api/reviews', payload, {\n        headers: { Authorization: `Bearer ${token}` },\n      });\n\n      console.log('Review submitted:', response.data);\n\n      // Clear any errors and show success message\n      setError(null);\n      setSuccess(true);\n    } catch (err) {\n      console.error('Submit Review Error:', err);\n\n      // More detailed error logging and user-friendly messages\n      if (err.response) {\n        console.error('Response data:', err.response.data);\n        console.error('Response status:', err.response.status);\n        console.error('Response headers:', err.response.headers);\n\n        if (err.response.status === 401) {\n          setError('Your session has expired. Please log in again.');\n        } else if (err.response.status === 400) {\n          setError(`Validation error: ${err.response.data.message || 'Please check your submission'}`);\n        } else if (err.response.status === 404) {\n          setError('Student information not found. Please log in again.');\n        } else {\n          setError(`Server error: ${err.response.data.message || 'Failed to submit review'}`);\n        }\n      } else if (err.request) {\n        console.error('Request made but no response received:', err.request);\n        setError('Network error. Please check your connection and try again.');\n      } else {\n        console.error('Error setting up request:', err.message);\n        setError('Failed to submit review. Please try again later.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Animation variants\n  const container = {\n    hidden: { opacity: 0 },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  if (loading) {\n    return <Loader />;\n  }\n\n  // Only show full-page error for critical errors that prevent the component from functioning\n  if (error && (error.includes('session') || error.includes('log in') || error.includes('permission'))) {\n    return (\n      <div className=\"flex h-screen bg-gray-50\">\n        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\n          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\n          <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white flex items-center justify-center\">\n            <motion.div\n              initial={{ scale: 0.9, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              className=\"text-center max-w-md bg-white p-8 rounded-xl shadow-sm border border-gray-100\"\n            >\n              <div className=\"text-[#0077B6] mb-4\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  className=\"h-12 w-12 mx-auto\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n                  />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-bold text-gray-900 mb-2\">Authentication Error</h3>\n              <p className=\"text-gray-600 mb-6\">{error}</p>\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => navigate('/login')}\n                className=\"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium shadow-md\"\n              >\n                Go to Login\n              </motion.button>\n            </motion.div>\n          </main>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      {/* Add style tag for animations */}\n      <style>{spinnerAnimation}</style>\n      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\n        <PatientNav />\n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\">\n          <div className=\"max-w-4xl mx-auto\">\n            <motion.div variants={container} initial=\"hidden\" animate=\"show\">\n              <div className=\"flex justify-between items-center mb-6\">\n                <h1 className=\"text-3xl font-bold text-[#0077B6]\">Review Steps</h1>\n                <div className=\"text-sm text-gray-600\">\n                  {new Date().toLocaleDateString()} {new Date().toLocaleTimeString()}\n                </div>\n              </div>\n\n              {/* Inline Error Message */}\n              {error && (\n                <motion.div\n                  initial={{ opacity: 0, y: -10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  className=\"bg-red-50 border-l-4 border-red-400 p-4 mb-6\"\n                >\n                  <div className=\"flex\">\n                    <div className=\"flex-shrink-0\">\n                      <svg\n                        className=\"h-5 w-5 text-red-400\"\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        viewBox=\"0 0 20 20\"\n                        fill=\"currentColor\"\n                      >\n                        <path\n                          fillRule=\"evenodd\"\n                          d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\"\n                          clipRule=\"evenodd\"\n                        />\n                      </svg>\n                    </div>\n                    <div className=\"ml-3\">\n                      <p className=\"text-sm text-[#333333]\">{error}</p>\n                    </div>\n                    <div className=\"ml-auto pl-3\">\n                      <div className=\"-mx-1.5 -my-1.5\">\n                        <button\n                          onClick={() => setError(null)}\n                          className=\"inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none\"\n                        >\n                          <svg className=\"h-4 w-4\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path\n                              fillRule=\"evenodd\"\n                              d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\"\n                              clipRule=\"evenodd\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n\n              {success ? (\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  className=\"bg-[#28A745]/10 p-6 rounded-xl shadow-sm mb-8 border border-[#28A745]/20 text-center\"\n                >\n                  <FaCheck className=\"h-12 w-12 text-[#28A745] mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Review Submitted Successfully</h3>\n                  <p className=\"text-gray-600 mb-2\">Your {reviewData.procedureType} review has been saved successfully.</p>\n                  <p className=\"text-gray-600 mb-6\">It will be available in your history and sent for supervisor approval.</p>\n                  <div className=\"flex justify-center gap-4\">\n                    <motion.button\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      onClick={() => {\n                        // Reset form and fetch new steps\n                        setReviewData({\n                          patientId: {\n                            nationalId: patientData.nationalId,\n                            fullName: patientData.fullName,\n                          },\n                          procedureType: reviewData.procedureType,\n                          chartId: '',\n                          note: '',\n                          reviewSteps: []\n                        });\n                        fetchReviewSteps(reviewData.procedureType);\n                        setSuccess(false);\n                      }}\n                      className=\"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90\"\n                    >\n                      Submit Another Review\n                    </motion.button>\n                    <motion.button\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      onClick={() => navigate(`/patientprofile/${nationalId}`)}\n                      className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50\"\n                    >\n                      Back to Patient Profile\n                    </motion.button>\n                  </div>\n                </motion.div>\n              ) : (\n                <>\n                  {/* Basic Info */}\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10\"\n                  >\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <div>\n                        <h4 className=\"text-sm font-medium text-gray-500\">Patient Name</h4>\n                        <p className=\"text-sm font-semibold text-gray-900\">{patientData?.fullName || 'N/A'}</p>\n                      </div>\n                      <div>\n                        <h4 className=\"text-sm font-medium text-gray-500\">National ID</h4>\n                        <p className=\"text-sm font-semibold text-gray-900\">{patientData?.nationalId || 'N/A'}</p>\n                      </div>\n                      <div>\n                        <h4 className=\"text-sm font-medium text-gray-500\">Student Name</h4>\n                        <p className=\"text-sm font-semibold text-gray-900\">{user?.name || 'N/A'}</p>\n                      </div>\n                      <div>\n                        <h4 className=\"text-sm font-medium text-gray-500\">Student ID</h4>\n                        <p className=\"text-sm font-semibold text-gray-900\">{user?.studentId || 'N/A'}</p>\n                      </div>\n                    </div>\n                  </motion.div>\n\n                  {/* Procedure Type Selector */}\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10\"\n                  >\n                    <h3 className=\"text-lg font-semibold text-[#0077B6] mb-4\">\n                      Select Procedure Type\n                    </h3>\n                    <div className=\"relative\">\n                      <select\n                        value={reviewData.procedureType || ''}\n                        onChange={(e) => {\n                          const newProcedureType = e.target.value;\n\n                          if (!newProcedureType) {\n                            // Handle empty selection\n                            setError('Please select a valid procedure type');\n                            return;\n                          }\n\n                          // Clear any previous errors\n                          setError(null);\n\n                          // Update localStorage\n                          localStorage.setItem('selectedSheetType', newProcedureType);\n\n                          // Update state\n                          setReviewData(prev => ({\n                            ...prev,\n                            procedureType: newProcedureType,\n                            reviewSteps: [] // Clear previous steps\n                          }));\n\n                          // Fetch new steps\n                          fetchReviewSteps(newProcedureType);\n                        }}\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] appearance-none\"\n                      >\n                        <option value=\"\">Select a procedure type</option>\n                        <option value=\"Operative\">Operative</option>\n                        <option value=\"Fixed Prosthodontics\">Fixed Prosthodontics</option>\n                        <option value=\"Removable Prosthodontics\">Removable Prosthodontics</option>\n                        <option value=\"Endodontics\">Endodontics</option>\n                        <option value=\"Periodontics\">Periodontics</option>\n                      </select>\n                      <div className=\"pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700\">\n                        <svg className=\"fill-current h-4 w-4\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\">\n                          <path d=\"M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z\" />\n                        </svg>\n                      </div>\n                    </div>\n                  </motion.div>\n\n                  {/* Removable Prosthodontics Type Selector */}\n                  {reviewData.procedureType === 'Removable Prosthodontics' && (\n                    <motion.div\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      className=\"bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10\"\n                    >\n                      <h3 className=\"text-lg font-semibold text-[#0077B6] mb-4\">\n                        Select Denture Type\n                      </h3>\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <button\n                          onClick={() => {\n                            const subType = 'Complete Denture';\n                            setReviewData(prev => ({ ...prev, subType }));\n                            fetchReviewSteps(reviewData.procedureType, subType);\n                          }}\n                          className={`p-4 border rounded-lg flex flex-col items-center transition-all ${\n                            reviewData.subType === 'Complete Denture'\n                              ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md'\n                              : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'\n                          }`}\n                        >\n                          <span className=\"text-lg font-medium mb-2\">Complete Denture</span>\n                          <span className=\"text-sm text-gray-600 text-center\">\n                            Full denture evaluation for edentulous patients\n                          </span>\n                        </button>\n                        <button\n                          onClick={() => {\n                            const subType = 'Partial Denture';\n                            setReviewData(prev => ({ ...prev, subType }));\n                            fetchReviewSteps(reviewData.procedureType, subType);\n                          }}\n                          className={`p-4 border rounded-lg flex flex-col items-center transition-all ${\n                            reviewData.subType === 'Partial Denture'\n                              ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md'\n                              : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'\n                          }`}\n                        >\n                          <span className=\"text-lg font-medium mb-2\">Partial Denture</span>\n                          <span className=\"text-sm text-gray-600 text-center\">\n                            Partial denture evaluation for partially edentulous patients\n                          </span>\n                        </button>\n                      </div>\n                    </motion.div>\n                  )}\n\n                  {/* Fixed Prosthodontics Type Selector */}\n                  {reviewData.procedureType === 'Fixed Prosthodontics' && (\n                    <motion.div\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      className=\"bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10\"\n                    >\n                      <h3 className=\"text-lg font-semibold text-[#0077B6] mb-4\">\n                        Select Procedure Type\n                      </h3>\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <button\n                          onClick={() => {\n                            const subType = 'Crown Procedure';\n                            setReviewData(prev => ({ ...prev, subType }));\n                            fetchReviewSteps(reviewData.procedureType, subType);\n                          }}\n                          className={`p-4 border rounded-lg flex flex-col items-center transition-all ${\n                            reviewData.subType === 'Crown Procedure'\n                              ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md'\n                              : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'\n                          }`}\n                        >\n                          <span className=\"text-lg font-medium mb-2\">Crown Procedure</span>\n                          <span className=\"text-sm text-gray-600 text-center\">\n                            Single crown preparation and placement\n                          </span>\n                        </button>\n                        <button\n                          onClick={() => {\n                            const subType = 'Bridge Procedure';\n                            setReviewData(prev => ({ ...prev, subType }));\n                            fetchReviewSteps(reviewData.procedureType, subType);\n                          }}\n                          className={`p-4 border rounded-lg flex flex-col items-center transition-all ${\n                            reviewData.subType === 'Bridge Procedure'\n                              ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md'\n                              : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'\n                          }`}\n                        >\n                          <span className=\"text-lg font-medium mb-2\">Bridge Procedure</span>\n                          <span className=\"text-sm text-gray-600 text-center\">\n                            Bridge preparation and placement for multiple teeth\n                          </span>\n                        </button>\n                      </div>\n                    </motion.div>\n                  )}\n\n                  {/* Operative Type Selector */}\n                  {reviewData.procedureType === 'Operative' && (\n                    <motion.div\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      className=\"bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10\"\n                    >\n                      <h3 className=\"text-lg font-semibold text-[#0077B6] mb-4\">\n                        Select Restoration Type\n                      </h3>\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <button\n                          onClick={() => {\n                            const subType = 'Amalgam Restoration';\n                            setReviewData(prev => ({ ...prev, subType }));\n                            fetchReviewSteps(reviewData.procedureType, subType);\n                          }}\n                          className={`p-4 border rounded-lg flex flex-col items-center transition-all ${\n                            reviewData.subType === 'Amalgam Restoration'\n                              ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md'\n                              : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'\n                          }`}\n                        >\n                          <span className=\"text-lg font-medium mb-2\">Amalgam Restoration</span>\n                          <span className=\"text-sm text-gray-600 text-center\">\n                            Metallic restoration for posterior teeth\n                          </span>\n                        </button>\n                        <button\n                          onClick={() => {\n                            const subType = 'Composite Restoration';\n                            setReviewData(prev => ({ ...prev, subType }));\n                            fetchReviewSteps(reviewData.procedureType, subType);\n                          }}\n                          className={`p-4 border rounded-lg flex flex-col items-center transition-all ${\n                            reviewData.subType === 'Composite Restoration'\n                              ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md'\n                              : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'\n                          }`}\n                        >\n                          <span className=\"text-lg font-medium mb-2\">Composite Restoration</span>\n                          <span className=\"text-sm text-gray-600 text-center\">\n                            Tooth-colored restoration for anterior or posterior teeth\n                          </span>\n                        </button>\n                      </div>\n                    </motion.div>\n                  )}\n\n                  {/* Review Steps */}\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10\"\n                  >\n                    <div className=\"flex justify-between items-center mb-4\">\n                      <h3 className=\"text-lg font-semibold text-[#0077B6]\">\n                        {(reviewData.procedureType === 'Removable Prosthodontics' ||\n                          reviewData.procedureType === 'Fixed Prosthodontics' ||\n                          reviewData.procedureType === 'Operative') && reviewData.subType\n                          ? `${reviewData.procedureType}: ${reviewData.subType} Evaluation`\n                          : `${reviewData.procedureType || 'Procedure'} Review Steps`}\n                      </h3>\n\n                      {reviewData.reviewSteps && reviewData.reviewSteps.length > 0 && (\n                        <div className=\"flex items-center\">\n                          <div className=\"bg-gray-200 rounded-full h-2.5 w-32 mr-2\">\n                            <div\n                              className=\"bg-[#0077B6] h-2.5 rounded-full\"\n                              style={{\n                                width: `${(reviewData.reviewSteps.filter(step => step.completed).length / reviewData.reviewSteps.length) * 100}%`\n                              }}\n                            ></div>\n                          </div>\n                          <span className=\"text-sm text-gray-600\">\n                            {reviewData.reviewSteps.filter(step => step.completed).length}/{reviewData.reviewSteps.length} completed\n                          </span>\n                        </div>\n                      )}\n                    </div>\n\n                    {!reviewData.procedureType ? (\n                      <div className=\"bg-yellow-50 border-l-4 border-yellow-400 p-4\">\n                        <div className=\"flex\">\n                          <div className=\"flex-shrink-0\">\n                            <svg\n                              className=\"h-5 w-5 text-yellow-400\"\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              viewBox=\"0 0 20 20\"\n                              fill=\"currentColor\"\n                            >\n                              <path\n                                fillRule=\"evenodd\"\n                                d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\"\n                                clipRule=\"evenodd\"\n                              />\n                            </svg>\n                          </div>\n                          <div className=\"ml-3\">\n                            <p className=\"text-sm text-yellow-700\">\n                              Please select a procedure type from the dropdown above.\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                    ) : reviewData.procedureType === 'Removable Prosthodontics' && !reviewData.subType ? (\n                      <div className=\"bg-yellow-50 border-l-4 border-yellow-400 p-4\">\n                        <div className=\"flex\">\n                          <div className=\"flex-shrink-0\">\n                            <svg\n                              className=\"h-5 w-5 text-yellow-400\"\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              viewBox=\"0 0 20 20\"\n                              fill=\"currentColor\"\n                            >\n                              <path\n                                fillRule=\"evenodd\"\n                                d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\"\n                                clipRule=\"evenodd\"\n                              />\n                            </svg>\n                          </div>\n                          <div className=\"ml-3\">\n                            <p className=\"text-sm text-yellow-700\">\n                              Please select either Complete Denture or Partial Denture from the options above.\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                    ) : reviewData.procedureType === 'Fixed Prosthodontics' && !reviewData.subType ? (\n                      <div className=\"bg-yellow-50 border-l-4 border-yellow-400 p-4\">\n                        <div className=\"flex\">\n                          <div className=\"flex-shrink-0\">\n                            <svg\n                              className=\"h-5 w-5 text-yellow-400\"\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              viewBox=\"0 0 20 20\"\n                              fill=\"currentColor\"\n                            >\n                              <path\n                                fillRule=\"evenodd\"\n                                d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\"\n                                clipRule=\"evenodd\"\n                              />\n                            </svg>\n                          </div>\n                          <div className=\"ml-3\">\n                            <p className=\"text-sm text-yellow-700\">\n                              Please select either Crown Procedure or Bridge Procedure from the options above.\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                    ) : reviewData.procedureType === 'Operative' && !reviewData.subType ? (\n                      <div className=\"bg-yellow-50 border-l-4 border-yellow-400 p-4\">\n                        <div className=\"flex\">\n                          <div className=\"flex-shrink-0\">\n                            <svg\n                              className=\"h-5 w-5 text-yellow-400\"\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              viewBox=\"0 0 20 20\"\n                              fill=\"currentColor\"\n                            >\n                              <path\n                                fillRule=\"evenodd\"\n                                d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\"\n                                clipRule=\"evenodd\"\n                              />\n                            </svg>\n                          </div>\n                          <div className=\"ml-3\">\n                            <p className=\"text-sm text-yellow-700\">\n                              Please select either Amalgam Restoration or Composite Restoration from the options above.\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                    ) : reviewData.reviewSteps && reviewData.reviewSteps.length > 0 ? (\n                      <div className=\"bg-white rounded-lg border border-gray-200 overflow-hidden\">\n                        <div className=\"divide-y divide-gray-200\">\n                          {reviewData.reviewSteps.map((step, index) => (\n                            <div\n                              key={index}\n                              className={`p-4 flex items-center justify-between transition-colors ${\n                                step.completed ? 'bg-orange-50' : 'hover:bg-gray-50'\n                              }`}\n                            >\n                              <div className=\"flex items-center\">\n                                <span className=\"text-sm font-medium text-gray-900\">\n                                  <span className=\"inline-block w-6 text-center mr-2 text-blue-600 font-bold\">{index + 1}.</span>\n                                  {step.description}\n                                </span>\n                              </div>\n                              <button\n                                onClick={() => handleStepToggle(index)}\n                                className={`flex items-center justify-center w-20 h-8 rounded-full transition-colors ${\n                                  step.completed\n                                    ? 'bg-orange-500 text-white'\n                                    : 'bg-gray-200 text-gray-500'\n                                }`}\n                              >\n                                {step.completed ? (\n                                  <>\n                                    <FaCheckCircle className=\"h-4 w-4 mr-1\" />\n                                    <span className=\"text-xs font-medium\">Pending</span>\n                                  </>\n                                ) : (\n                                  <>\n                                    <FaTimesCircle className=\"h-4 w-4 mr-1\" />\n                                    <span className=\"text-xs font-medium\">Not Done</span>\n                                  </>\n                                )}\n                              </button>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    ) : (\n                      <div className=\"bg-yellow-50 border-l-4 border-yellow-400 p-4\">\n                        <div className=\"flex\">\n                          <div className=\"flex-shrink-0\">\n                            <svg\n                              className=\"h-5 w-5 text-yellow-400\"\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              viewBox=\"0 0 20 20\"\n                              fill=\"currentColor\"\n                            >\n                              <path\n                                fillRule=\"evenodd\"\n                                d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\"\n                                clipRule=\"evenodd\"\n                              />\n                            </svg>\n                          </div>\n                          <div className=\"ml-3\">\n                            <p className=\"text-sm text-yellow-700\">\n                              No review steps available for this procedure type.\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                  </motion.div>\n\n                  {/* Notes */}\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10\"\n                  >\n                    <h3 className=\"text-lg font-semibold text-[#0077B6] mb-4\">Additional Notes (Optional)</h3>\n                    <textarea\n                      placeholder=\"Add any additional notes about the review steps\"\n                      value={reviewData.note}\n                      onChange={handleNoteChange}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6]\"\n                      rows=\"4\"\n                    />\n                  </motion.div>\n\n                  {/* Submit Button */}\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"flex justify-end\"\n                  >\n                    <motion.button\n                      whileHover={loading ? {} : { scale: 1.05 }}\n                      whileTap={loading ? {} : { scale: 0.95 }}\n                      onClick={handleSubmitReview}\n                      disabled={\n                        loading ||\n                        !reviewData.procedureType ||\n                        reviewData.reviewSteps.length === 0 ||\n                        (reviewData.procedureType === 'Removable Prosthodontics' && !reviewData.subType) ||\n                        (reviewData.procedureType === 'Fixed Prosthodontics' && !reviewData.subType) ||\n                        (reviewData.procedureType === 'Operative' && !reviewData.subType)\n                      }\n                      className={`px-6 py-3 rounded-lg flex items-center ${\n                        loading\n                          ? 'bg-[#0077B6]/60 text-white cursor-wait'\n                          : !reviewData.procedureType ||\n                            reviewData.reviewSteps.length === 0 ||\n                            (reviewData.procedureType === 'Removable Prosthodontics' && !reviewData.subType) ||\n                            (reviewData.procedureType === 'Fixed Prosthodontics' && !reviewData.subType) ||\n                            (reviewData.procedureType === 'Operative' && !reviewData.subType)\n                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                            : 'bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white shadow-md hover:shadow-lg'\n                      }`}\n                    >\n                      {loading ? (\n                        <>\n                          <FaSpinner className=\"h-5 w-5 mr-2 animate-spin\" />\n                          Submitting...\n                        </>\n                      ) : (\n                        <>\n                          <FaSave className=\"h-5 w-5 mr-2\" />\n                          Submit Review\n                        </>\n                      )}\n                    </motion.button>\n                  </motion.div>\n                </>\n              )}\n            </motion.div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default ReviewSteps;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,OAAO,EAAEC,MAAM,EAAEC,aAAa,EAAEC,aAAa,EAAEC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;;AAEjG;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,gBAAgB,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAW,CAAC,GAAGtB,SAAS,CAAC,CAAC;EAClC,MAAMuB,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEuB,IAAI;IAAEC;EAAM,CAAC,GAAGrB,OAAO,CAAC,CAAC;EACjC,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC;IAC3CwC,SAAS,EAAE;MAAEhB,UAAU,EAAE,EAAE;MAAEiB,QAAQ,EAAE;IAAG,CAAC;IAC3CC,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE,CAAC;EACd,CAAC,CAAC;;EAEF;EACA7C,SAAS,CAAC,MAAM;IACd,MAAM8C,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC;IAChE,IAAIF,cAAc,EAAE;MAClBR,aAAa,CAACW,IAAI,KAAK;QACrB,GAAGA,IAAI;QACPR,aAAa,EAAEK;MACjB,CAAC,CAAC,CAAC;;MAEH;MACAI,gBAAgB,CAACJ,cAAc,CAAC;IAClC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA9C,SAAS,CAAC,MAAM;IACd,MAAMmD,mBAAmB,GAAGA,CAAA,KAAM;MAChC,MAAML,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC;MAChE,IAAIF,cAAc,IAAIA,cAAc,KAAKT,UAAU,CAACI,aAAa,EAAE;QACjEH,aAAa,CAACW,IAAI,KAAK;UACrB,GAAGA,IAAI;UACPR,aAAa,EAAEK,cAAc;UAC7BF,WAAW,EAAE,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC;;QAEH;QACAM,gBAAgB,CAACJ,cAAc,CAAC;MAClC;IACF,CAAC;;IAED;IACAM,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEF,mBAAmB,CAAC;;IAEvD;IACA,OAAO,MAAM;MACXC,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEH,mBAAmB,CAAC;IAC5D,CAAC;EACH,CAAC,EAAE,CAACd,UAAU,CAACI,aAAa,CAAC,CAAC;EAE9B,MAAMS,gBAAgB,GAAG,MAAAA,CAAOT,aAAa,EAAEI,OAAO,GAAG,EAAE,KAAK;IAC9D;IACA,IAAI,CAACJ,aAAa,IAAIA,aAAa,CAACc,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjDrB,QAAQ,CAAC,sCAAsC,CAAC;MAChD;IACF;IAEA,IAAI;MACF;MACAF,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMwB,GAAG,GAAG,2CAA2Cf,aAAa,GAAGI,OAAO,GAAG,YAAYY,kBAAkB,CAACZ,OAAO,CAAC,EAAE,GAAG,EAAE,EAAE;MAEjI,MAAMa,QAAQ,GAAG,MAAMtD,KAAK,CAACuD,GAAG,CAACH,GAAG,EAAE;QACpCI,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUnC,KAAK;QAAG;MAC9C,CAAC,CAAC;;MAEF;MACA,IAAI,CAACgC,QAAQ,CAACI,IAAI,IAAI,CAACC,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACI,IAAI,CAAC,IAAIJ,QAAQ,CAACI,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;QACjF/B,QAAQ,CAAC,6BAA6BO,aAAa,GAAGI,OAAO,GAAG,KAAKA,OAAO,GAAG,GAAG,EAAE,sCAAsC,CAAC;QAC3H;MACF;;MAEA;MACAP,aAAa,CAACW,IAAI,KAAK;QACrB,GAAGA,IAAI;QACPL,WAAW,EAAEc,QAAQ,CAACI,IAAI;QAC1BjB,OAAO,EAAEA,OAAO,IAAII,IAAI,CAACJ;MAC3B,CAAC,CAAC,CAAC;;MAEH;MACAX,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOgC,GAAG,EAAE;MACZC,OAAO,CAAClC,KAAK,CAAC,8BAA8B,EAAEiC,GAAG,CAAC;;MAElD;MACA,IAAIA,GAAG,CAACR,QAAQ,EAAE;QAChB;QACA;QACA,IAAIQ,GAAG,CAACR,QAAQ,CAACU,MAAM,KAAK,GAAG,EAAE;UAC/BlC,QAAQ,CAAC,gDAAgD,CAAC;QAC5D,CAAC,MAAM,IAAIgC,GAAG,CAACR,QAAQ,CAACU,MAAM,KAAK,GAAG,EAAE;UACtClC,QAAQ,CAAC,6BAA6BO,aAAa,sCAAsC,CAAC;QAC5F,CAAC,MAAM;UACLP,QAAQ,CAAC,iBAAiBgC,GAAG,CAACR,QAAQ,CAACI,IAAI,CAACO,OAAO,IAAI,6BAA6B,EAAE,CAAC;QACzF;MACF,CAAC,MAAM,IAAIH,GAAG,CAACI,OAAO,EAAE;QACtB;QACApC,QAAQ,CAAC,4DAA4D,CAAC;MACxE,CAAC,MAAM;QACL;QACAA,QAAQ,CAAC,sDAAsD,CAAC;MAClE;IACF,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDhC,SAAS,CAAC,MAAM;IACd,MAAMuE,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF;QACA,IAAI,CAAC7C,KAAK,IAAI,CAACD,IAAI,EAAE;UACnB0C,OAAO,CAACK,GAAG,CAAC,wBAAwB,CAAC;UACrCtC,QAAQ,CAAC,kCAAkC,CAAC;UAC5CV,QAAQ,CAAC,QAAQ,CAAC;UAClB;QACF;QAEA,IAAIC,IAAI,CAACgD,IAAI,KAAK,SAAS,EAAE;UAC3BN,OAAO,CAACK,GAAG,CAAC,wBAAwB,EAAE/C,IAAI,CAACgD,IAAI,CAAC;UAChDvC,QAAQ,CAAC,kCAAkC,CAAC;UAC5CV,QAAQ,CAAC,YAAY,CAAC;UACtB;QACF;;QAEA;QACA,MAAMkD,eAAe,GAAG,MAAMtE,KAAK,CAACuD,GAAG,CAAC,6CAA6CpC,UAAU,EAAE,CAAC;QAClGO,cAAc,CAAC4C,eAAe,CAACZ,IAAI,CAAC;QACpCxB,aAAa,CAAEW,IAAI,KAAM;UACvB,GAAGA,IAAI;UACPV,SAAS,EAAE;YAAEhB,UAAU,EAAEmD,eAAe,CAACZ,IAAI,CAACvC,UAAU;YAAEiB,QAAQ,EAAEkC,eAAe,CAACZ,IAAI,CAACtB;UAAS;QACpG,CAAC,CAAC,CAAC;QAEHR,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOkC,GAAG,EAAE;QAAA,IAAAS,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;QACZZ,OAAO,CAAClC,KAAK,CAAC,YAAY,EAAE;UAC1BoC,OAAO,EAAEH,GAAG,CAACG,OAAO;UACpBD,MAAM,GAAAO,aAAA,GAAET,GAAG,CAACR,QAAQ,cAAAiB,aAAA,uBAAZA,aAAA,CAAcP,MAAM;UAC5BN,IAAI,GAAAc,cAAA,GAAEV,GAAG,CAACR,QAAQ,cAAAkB,cAAA,uBAAZA,cAAA,CAAcd;QACtB,CAAC,CAAC;;QAEF;QACA,IAAI,EAAAe,cAAA,GAAAX,GAAG,CAACR,QAAQ,cAAAmB,cAAA,uBAAZA,cAAA,CAAcT,MAAM,MAAK,GAAG,EAAE;UAChClC,QAAQ,CAAC,mBAAmBX,UAAU,0CAA0C,CAAC;QACnF,CAAC,MAAM,IAAI,EAAAuD,cAAA,GAAAZ,GAAG,CAACR,QAAQ,cAAAoB,cAAA,uBAAZA,cAAA,CAAcV,MAAM,MAAK,GAAG,EAAE;UACvClC,QAAQ,CAAC,kDAAkD,CAAC;UAC5Da,YAAY,CAACiC,UAAU,CAAC,OAAO,CAAC;UAChCjC,YAAY,CAACiC,UAAU,CAAC,MAAM,CAAC;UAC/BxD,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,MAAM,IAAI,EAAAuD,cAAA,GAAAb,GAAG,CAACR,QAAQ,cAAAqB,cAAA,uBAAZA,cAAA,CAAcX,MAAM,MAAK,GAAG,EAAE;UACvClC,QAAQ,CAAC,qDAAqD,CAAC;UAC/DV,QAAQ,CAAC,YAAY,CAAC;QACxB,CAAC,MAAM;UAAA,IAAAyD,cAAA,EAAAC,mBAAA;UACLhD,QAAQ,CAAC,EAAA+C,cAAA,GAAAf,GAAG,CAACR,QAAQ,cAAAuB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcnB,IAAI,cAAAoB,mBAAA,uBAAlBA,mBAAA,CAAoBb,OAAO,KAAI,6BAA6B,CAAC;QACxE;QACArC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDuC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAChD,UAAU,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,KAAK,CAAC,CAAC;EAEvC,MAAMyD,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,YAAY,GAAG,CAAC,GAAGhD,UAAU,CAACO,WAAW,CAAC;IAChDyC,YAAY,CAACD,KAAK,CAAC,CAACE,SAAS,GAAG,CAACD,YAAY,CAACD,KAAK,CAAC,CAACE,SAAS;IAC9DhD,aAAa,CAACW,IAAI,KAAK;MACrB,GAAGA,IAAI;MACPL,WAAW,EAAEyC;IACf,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,gBAAgB,GAAIC,CAAC,IAAK;IAC9BlD,aAAa,CAACW,IAAI,KAAK;MACrB,GAAGA,IAAI;MACPN,IAAI,EAAE6C,CAAC,CAACC,MAAM,CAACC;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC;IACA,IAAI,CAACtD,UAAU,CAACI,aAAa,IAAIJ,UAAU,CAACI,aAAa,CAACc,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACvErB,QAAQ,CAAC,kDAAkD,CAAC;MAC5D;IACF;;IAEA;IACA,IAAIG,UAAU,CAACI,aAAa,KAAK,0BAA0B,IAAI,CAACJ,UAAU,CAACQ,OAAO,EAAE;MAClFX,QAAQ,CAAC,4EAA4E,CAAC;MACtF;IACF;;IAEA;IACA,IAAIG,UAAU,CAACI,aAAa,KAAK,sBAAsB,IAAI,CAACJ,UAAU,CAACQ,OAAO,EAAE;MAC9EX,QAAQ,CAAC,4EAA4E,CAAC;MACtF;IACF;;IAEA;IACA,IAAIG,UAAU,CAACI,aAAa,KAAK,WAAW,IAAI,CAACJ,UAAU,CAACQ,OAAO,EAAE;MACnEX,QAAQ,CAAC,qFAAqF,CAAC;MAC/F;IACF;IAEA,IAAI,CAACG,UAAU,CAACO,WAAW,IAAIP,UAAU,CAACO,WAAW,CAACqB,MAAM,KAAK,CAAC,EAAE;MAClE/B,QAAQ,CAAC,qEAAqE,CAAC;MAC/E;IACF;IAEA,IAAI,CAACG,UAAU,CAACE,SAAS,IAAI,CAACF,UAAU,CAACE,SAAS,CAAChB,UAAU,EAAE;MAC7DW,QAAQ,CAAC,uEAAuE,CAAC;MACjF;IACF;;IAEA;IACA,MAAM0D,cAAc,GAAGvD,UAAU,CAACO,WAAW,CAACiD,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACR,SAAS,CAAC;IAC5E,IAAIM,cAAc,CAAC3B,MAAM,KAAK,CAAC,EAAE;MAC/B/B,QAAQ,CAAC,4DAA4D,CAAC;MACtE;IACF;IAEA,IAAI;MACF;MACAF,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM+D,OAAO,GAAG;QACdxD,SAAS,EAAEF,UAAU,CAACE,SAAS;QAC/BE,aAAa,EAAEJ,UAAU,CAACI,aAAa;QACvCE,IAAI,EAAEN,UAAU,CAACM,IAAI,IAAI,EAAE;QAC3BC,WAAW,EAAEP,UAAU,CAACO,WAAW,IAAI,EAAE;QACzCoD,OAAO,EAAE,EAAE,CAAE;MACf,CAAC;;MAED;MACA,IAAI,CAAC3D,UAAU,CAACI,aAAa,KAAK,0BAA0B,IACvDJ,UAAU,CAACI,aAAa,KAAK,sBAAsB,IACnDJ,UAAU,CAACI,aAAa,KAAK,WAAW,KAAKJ,UAAU,CAACQ,OAAO,EAAE;QACpEkD,OAAO,CAACtD,aAAa,GAAG,GAAGJ,UAAU,CAACI,aAAa,MAAMJ,UAAU,CAACQ,OAAO,EAAE;MAC/E;MAEAsB,OAAO,CAACK,GAAG,CAAC,oBAAoB,EAAEuB,OAAO,CAAC;MAC1C,MAAMrC,QAAQ,GAAG,MAAMtD,KAAK,CAAC6F,IAAI,CAAC,mCAAmC,EAAEF,OAAO,EAAE;QAC9EnC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUnC,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEFyC,OAAO,CAACK,GAAG,CAAC,mBAAmB,EAAEd,QAAQ,CAACI,IAAI,CAAC;;MAE/C;MACA5B,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;IAClB,CAAC,CAAC,OAAO8B,GAAG,EAAE;MACZC,OAAO,CAAClC,KAAK,CAAC,sBAAsB,EAAEiC,GAAG,CAAC;;MAE1C;MACA,IAAIA,GAAG,CAACR,QAAQ,EAAE;QAChBS,OAAO,CAAClC,KAAK,CAAC,gBAAgB,EAAEiC,GAAG,CAACR,QAAQ,CAACI,IAAI,CAAC;QAClDK,OAAO,CAAClC,KAAK,CAAC,kBAAkB,EAAEiC,GAAG,CAACR,QAAQ,CAACU,MAAM,CAAC;QACtDD,OAAO,CAAClC,KAAK,CAAC,mBAAmB,EAAEiC,GAAG,CAACR,QAAQ,CAACE,OAAO,CAAC;QAExD,IAAIM,GAAG,CAACR,QAAQ,CAACU,MAAM,KAAK,GAAG,EAAE;UAC/BlC,QAAQ,CAAC,gDAAgD,CAAC;QAC5D,CAAC,MAAM,IAAIgC,GAAG,CAACR,QAAQ,CAACU,MAAM,KAAK,GAAG,EAAE;UACtClC,QAAQ,CAAC,qBAAqBgC,GAAG,CAACR,QAAQ,CAACI,IAAI,CAACO,OAAO,IAAI,8BAA8B,EAAE,CAAC;QAC9F,CAAC,MAAM,IAAIH,GAAG,CAACR,QAAQ,CAACU,MAAM,KAAK,GAAG,EAAE;UACtClC,QAAQ,CAAC,qDAAqD,CAAC;QACjE,CAAC,MAAM;UACLA,QAAQ,CAAC,iBAAiBgC,GAAG,CAACR,QAAQ,CAACI,IAAI,CAACO,OAAO,IAAI,yBAAyB,EAAE,CAAC;QACrF;MACF,CAAC,MAAM,IAAIH,GAAG,CAACI,OAAO,EAAE;QACtBH,OAAO,CAAClC,KAAK,CAAC,wCAAwC,EAAEiC,GAAG,CAACI,OAAO,CAAC;QACpEpC,QAAQ,CAAC,4DAA4D,CAAC;MACxE,CAAC,MAAM;QACLiC,OAAO,CAAClC,KAAK,CAAC,2BAA2B,EAAEiC,GAAG,CAACG,OAAO,CAAC;QACvDnC,QAAQ,CAAC,kDAAkD,CAAC;MAC9D;IACF,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkE,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MACJD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACVC,eAAe,EAAE;MACnB;IACF;EACF,CAAC;EAED,IAAIxE,OAAO,EAAE;IACX,oBAAOd,OAAA,CAACR,MAAM;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;;EAEA;EACA,IAAI1E,KAAK,KAAKA,KAAK,CAAC2E,QAAQ,CAAC,SAAS,CAAC,IAAI3E,KAAK,CAAC2E,QAAQ,CAAC,QAAQ,CAAC,IAAI3E,KAAK,CAAC2E,QAAQ,CAAC,YAAY,CAAC,CAAC,EAAE;IACpG,oBACE3F,OAAA;MAAK4F,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvC7F,OAAA,CAACV,OAAO;QAACwG,MAAM,EAAEpF,WAAY;QAACqF,SAAS,EAAEpF;MAAe;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3D1F,OAAA;QAAK4F,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnD7F,OAAA,CAACX,MAAM;UAAC2G,aAAa,EAAEA,CAAA,KAAMrF,cAAc,CAAC,CAACD,WAAW;QAAE;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7D1F,OAAA;UAAM4F,SAAS,EAAC,gHAAgH;UAAAC,QAAA,eAC9H7F,OAAA,CAACd,MAAM,CAAC+G,GAAG;YACTC,OAAO,EAAE;cAAEC,KAAK,EAAE,GAAG;cAAEhB,OAAO,EAAE;YAAE,CAAE;YACpCiB,OAAO,EAAE;cAAED,KAAK,EAAE,CAAC;cAAEhB,OAAO,EAAE;YAAE,CAAE;YAClCS,SAAS,EAAC,+EAA+E;YAAAC,QAAA,gBAEzF7F,OAAA;cAAK4F,SAAS,EAAC,qBAAqB;cAAAC,QAAA,eAClC7F,OAAA;gBACEqG,KAAK,EAAC,4BAA4B;gBAClCT,SAAS,EAAC,mBAAmB;gBAC7BU,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnBC,MAAM,EAAC,cAAc;gBAAAX,QAAA,eAErB7F,OAAA;kBACEyG,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,WAAW,EAAE,CAAE;kBACfC,CAAC,EAAC;gBAAsI;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1F,OAAA;cAAI4F,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAAoB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9E1F,OAAA;cAAG4F,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAE7E;YAAK;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7C1F,OAAA,CAACd,MAAM,CAAC2H,MAAM;cACZC,UAAU,EAAE;gBAAEX,KAAK,EAAE;cAAK,CAAE;cAC5BY,QAAQ,EAAE;gBAAEZ,KAAK,EAAE;cAAK,CAAE;cAC1Ba,OAAO,EAAEA,CAAA,KAAMzG,QAAQ,CAAC,QAAQ,CAAE;cAClCqF,SAAS,EAAC,oJAAoJ;cAAAC,QAAA,EAC/J;YAED;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE1F,OAAA;IAAK4F,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBAEvC7F,OAAA;MAAA6F,QAAA,EAAQ1F;IAAgB;MAAAoF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACjC1F,OAAA,CAACV,OAAO;MAACwG,MAAM,EAAEpF,WAAY;MAACqF,SAAS,EAAEpF;IAAe;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC3D1F,OAAA;MAAK4F,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnD7F,OAAA,CAACX,MAAM;QAAC2G,aAAa,EAAEA,CAAA,KAAMrF,cAAc,CAAC,CAACD,WAAW;MAAE;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7D1F,OAAA,CAACT,UAAU;QAAAgG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACd1F,OAAA;QAAM4F,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAC7F7F,OAAA;UAAK4F,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChC7F,OAAA,CAACd,MAAM,CAAC+G,GAAG;YAACgB,QAAQ,EAAEhC,SAAU;YAACiB,OAAO,EAAC,QAAQ;YAACE,OAAO,EAAC,MAAM;YAAAP,QAAA,gBAC9D7F,OAAA;cAAK4F,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD7F,OAAA;gBAAI4F,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAY;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnE1F,OAAA;gBAAK4F,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACnC,IAAIqB,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAC,GAAC,EAAC,IAAID,IAAI,CAAC,CAAC,CAACE,kBAAkB,CAAC,CAAC;cAAA;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL1E,KAAK,iBACJhB,OAAA,CAACd,MAAM,CAAC+G,GAAG;cACTC,OAAO,EAAE;gBAAEf,OAAO,EAAE,CAAC;gBAAEkC,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCjB,OAAO,EAAE;gBAAEjB,OAAO,EAAE,CAAC;gBAAEkC,CAAC,EAAE;cAAE,CAAE;cAC9BzB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,eAExD7F,OAAA;gBAAK4F,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB7F,OAAA;kBAAK4F,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5B7F,OAAA;oBACE4F,SAAS,EAAC,sBAAsB;oBAChCS,KAAK,EAAC,4BAA4B;oBAClCE,OAAO,EAAC,WAAW;oBACnBD,IAAI,EAAC,cAAc;oBAAAT,QAAA,eAEnB7F,OAAA;sBACEsH,QAAQ,EAAC,SAAS;sBAClBV,CAAC,EAAC,mNAAmN;sBACrNW,QAAQ,EAAC;oBAAS;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN1F,OAAA;kBAAK4F,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnB7F,OAAA;oBAAG4F,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAE7E;kBAAK;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACN1F,OAAA;kBAAK4F,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3B7F,OAAA;oBAAK4F,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,eAC9B7F,OAAA;sBACEgH,OAAO,EAAEA,CAAA,KAAM/F,QAAQ,CAAC,IAAI,CAAE;sBAC9B2E,SAAS,EAAC,yFAAyF;sBAAAC,QAAA,eAEnG7F,OAAA;wBAAK4F,SAAS,EAAC,SAAS;wBAACS,KAAK,EAAC,4BAA4B;wBAACE,OAAO,EAAC,WAAW;wBAACD,IAAI,EAAC,cAAc;wBAAAT,QAAA,eACjG7F,OAAA;0BACEsH,QAAQ,EAAC,SAAS;0BAClBV,CAAC,EAAC,oMAAoM;0BACtMW,QAAQ,EAAC;wBAAS;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAEAxE,OAAO,gBACNlB,OAAA,CAACd,MAAM,CAAC+G,GAAG;cACTC,OAAO,EAAE;gBAAEf,OAAO,EAAE,CAAC;gBAAEkC,CAAC,EAAE;cAAG,CAAE;cAC/BjB,OAAO,EAAE;gBAAEjB,OAAO,EAAE,CAAC;gBAAEkC,CAAC,EAAE;cAAE,CAAE;cAC9BzB,SAAS,EAAC,sFAAsF;cAAAC,QAAA,gBAEhG7F,OAAA,CAACP,OAAO;gBAACmG,SAAS,EAAC;cAAuC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7D1F,OAAA;gBAAI4F,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAA6B;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzF1F,OAAA;gBAAG4F,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAAC,OAAK,EAACzE,UAAU,CAACI,aAAa,EAAC,sCAAoC;cAAA;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACzG1F,OAAA;gBAAG4F,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAsE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5G1F,OAAA;gBAAK4F,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxC7F,OAAA,CAACd,MAAM,CAAC2H,MAAM;kBACZC,UAAU,EAAE;oBAAEX,KAAK,EAAE;kBAAK,CAAE;kBAC5BY,QAAQ,EAAE;oBAAEZ,KAAK,EAAE;kBAAK,CAAE;kBAC1Ba,OAAO,EAAEA,CAAA,KAAM;oBACb;oBACA3F,aAAa,CAAC;sBACZC,SAAS,EAAE;wBACThB,UAAU,EAAEM,WAAW,CAACN,UAAU;wBAClCiB,QAAQ,EAAEX,WAAW,CAACW;sBACxB,CAAC;sBACDC,aAAa,EAAEJ,UAAU,CAACI,aAAa;sBACvCC,OAAO,EAAE,EAAE;sBACXC,IAAI,EAAE,EAAE;sBACRC,WAAW,EAAE;oBACf,CAAC,CAAC;oBACFM,gBAAgB,CAACb,UAAU,CAACI,aAAa,CAAC;oBAC1CL,UAAU,CAAC,KAAK,CAAC;kBACnB,CAAE;kBACFyE,SAAS,EAAC,8HAA8H;kBAAAC,QAAA,EACzI;gBAED;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAChB1F,OAAA,CAACd,MAAM,CAAC2H,MAAM;kBACZC,UAAU,EAAE;oBAAEX,KAAK,EAAE;kBAAK,CAAE;kBAC5BY,QAAQ,EAAE;oBAAEZ,KAAK,EAAE;kBAAK,CAAE;kBAC1Ba,OAAO,EAAEA,CAAA,KAAMzG,QAAQ,CAAC,mBAAmBD,UAAU,EAAE,CAAE;kBACzDsF,SAAS,EAAC,8EAA8E;kBAAAC,QAAA,EACzF;gBAED;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,gBAEb1F,OAAA,CAAAE,SAAA;cAAA2F,QAAA,gBAEE7F,OAAA,CAACd,MAAM,CAAC+G,GAAG;gBACTC,OAAO,EAAE;kBAAEf,OAAO,EAAE,CAAC;kBAAEkC,CAAC,EAAE;gBAAG,CAAE;gBAC/BjB,OAAO,EAAE;kBAAEjB,OAAO,EAAE,CAAC;kBAAEkC,CAAC,EAAE;gBAAE,CAAE;gBAC9BzB,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,eAE7E7F,OAAA;kBAAK4F,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpD7F,OAAA;oBAAA6F,QAAA,gBACE7F,OAAA;sBAAI4F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAY;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnE1F,OAAA;sBAAG4F,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAAE,CAAAjF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEW,QAAQ,KAAI;oBAAK;sBAAAgE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC,eACN1F,OAAA;oBAAA6F,QAAA,gBACE7F,OAAA;sBAAI4F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAW;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClE1F,OAAA;sBAAG4F,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAAE,CAAAjF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEN,UAAU,KAAI;oBAAK;sBAAAiF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF,CAAC,eACN1F,OAAA;oBAAA6F,QAAA,gBACE7F,OAAA;sBAAI4F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAY;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnE1F,OAAA;sBAAG4F,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAAE,CAAArF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgH,IAAI,KAAI;oBAAK;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eACN1F,OAAA;oBAAA6F,QAAA,gBACE7F,OAAA;sBAAI4F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAU;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjE1F,OAAA;sBAAG4F,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAAE,CAAArF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiH,SAAS,KAAI;oBAAK;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAGb1F,OAAA,CAACd,MAAM,CAAC+G,GAAG;gBACTC,OAAO,EAAE;kBAAEf,OAAO,EAAE,CAAC;kBAAEkC,CAAC,EAAE;gBAAG,CAAE;gBAC/BjB,OAAO,EAAE;kBAAEjB,OAAO,EAAE,CAAC;kBAAEkC,CAAC,EAAE;gBAAE,CAAE;gBAC9BzB,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,gBAE7E7F,OAAA;kBAAI4F,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAE1D;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1F,OAAA;kBAAK4F,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvB7F,OAAA;oBACEyE,KAAK,EAAErD,UAAU,CAACI,aAAa,IAAI,EAAG;oBACtCkG,QAAQ,EAAGnD,CAAC,IAAK;sBACf,MAAMoD,gBAAgB,GAAGpD,CAAC,CAACC,MAAM,CAACC,KAAK;sBAEvC,IAAI,CAACkD,gBAAgB,EAAE;wBACrB;wBACA1G,QAAQ,CAAC,sCAAsC,CAAC;wBAChD;sBACF;;sBAEA;sBACAA,QAAQ,CAAC,IAAI,CAAC;;sBAEd;sBACAa,YAAY,CAAC8F,OAAO,CAAC,mBAAmB,EAAED,gBAAgB,CAAC;;sBAE3D;sBACAtG,aAAa,CAACW,IAAI,KAAK;wBACrB,GAAGA,IAAI;wBACPR,aAAa,EAAEmG,gBAAgB;wBAC/BhG,WAAW,EAAE,EAAE,CAAC;sBAClB,CAAC,CAAC,CAAC;;sBAEH;sBACAM,gBAAgB,CAAC0F,gBAAgB,CAAC;oBACpC,CAAE;oBACF/B,SAAS,EAAC,sGAAsG;oBAAAC,QAAA,gBAEhH7F,OAAA;sBAAQyE,KAAK,EAAC,EAAE;sBAAAoB,QAAA,EAAC;oBAAuB;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACjD1F,OAAA;sBAAQyE,KAAK,EAAC,WAAW;sBAAAoB,QAAA,EAAC;oBAAS;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5C1F,OAAA;sBAAQyE,KAAK,EAAC,sBAAsB;sBAAAoB,QAAA,EAAC;oBAAoB;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAClE1F,OAAA;sBAAQyE,KAAK,EAAC,0BAA0B;sBAAAoB,QAAA,EAAC;oBAAwB;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC1E1F,OAAA;sBAAQyE,KAAK,EAAC,aAAa;sBAAAoB,QAAA,EAAC;oBAAW;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAChD1F,OAAA;sBAAQyE,KAAK,EAAC,cAAc;sBAAAoB,QAAA,EAAC;oBAAY;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACT1F,OAAA;oBAAK4F,SAAS,EAAC,qFAAqF;oBAAAC,QAAA,eAClG7F,OAAA;sBAAK4F,SAAS,EAAC,sBAAsB;sBAACS,KAAK,EAAC,4BAA4B;sBAACE,OAAO,EAAC,WAAW;sBAAAV,QAAA,eAC1F7F,OAAA;wBAAM4G,CAAC,EAAC;sBAA4E;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EAGZtE,UAAU,CAACI,aAAa,KAAK,0BAA0B,iBACtDxB,OAAA,CAACd,MAAM,CAAC+G,GAAG;gBACTC,OAAO,EAAE;kBAAEf,OAAO,EAAE,CAAC;kBAAEkC,CAAC,EAAE;gBAAG,CAAE;gBAC/BjB,OAAO,EAAE;kBAAEjB,OAAO,EAAE,CAAC;kBAAEkC,CAAC,EAAE;gBAAE,CAAE;gBAC9BzB,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,gBAE7E7F,OAAA;kBAAI4F,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAE1D;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1F,OAAA;kBAAK4F,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpD7F,OAAA;oBACEgH,OAAO,EAAEA,CAAA,KAAM;sBACb,MAAMpF,OAAO,GAAG,kBAAkB;sBAClCP,aAAa,CAACW,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEJ;sBAAQ,CAAC,CAAC,CAAC;sBAC7CK,gBAAgB,CAACb,UAAU,CAACI,aAAa,EAAEI,OAAO,CAAC;oBACrD,CAAE;oBACFgE,SAAS,EAAE,mEACTxE,UAAU,CAACQ,OAAO,KAAK,kBAAkB,GACrC,4CAA4C,GAC5C,gEAAgE,EACnE;oBAAAiE,QAAA,gBAEH7F,OAAA;sBAAM4F,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAClE1F,OAAA;sBAAM4F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAEpD;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACT1F,OAAA;oBACEgH,OAAO,EAAEA,CAAA,KAAM;sBACb,MAAMpF,OAAO,GAAG,iBAAiB;sBACjCP,aAAa,CAACW,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEJ;sBAAQ,CAAC,CAAC,CAAC;sBAC7CK,gBAAgB,CAACb,UAAU,CAACI,aAAa,EAAEI,OAAO,CAAC;oBACrD,CAAE;oBACFgE,SAAS,EAAE,mEACTxE,UAAU,CAACQ,OAAO,KAAK,iBAAiB,GACpC,4CAA4C,GAC5C,gEAAgE,EACnE;oBAAAiE,QAAA,gBAEH7F,OAAA;sBAAM4F,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EAAC;oBAAe;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjE1F,OAAA;sBAAM4F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAEpD;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACb,EAGAtE,UAAU,CAACI,aAAa,KAAK,sBAAsB,iBAClDxB,OAAA,CAACd,MAAM,CAAC+G,GAAG;gBACTC,OAAO,EAAE;kBAAEf,OAAO,EAAE,CAAC;kBAAEkC,CAAC,EAAE;gBAAG,CAAE;gBAC/BjB,OAAO,EAAE;kBAAEjB,OAAO,EAAE,CAAC;kBAAEkC,CAAC,EAAE;gBAAE,CAAE;gBAC9BzB,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,gBAE7E7F,OAAA;kBAAI4F,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAE1D;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1F,OAAA;kBAAK4F,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpD7F,OAAA;oBACEgH,OAAO,EAAEA,CAAA,KAAM;sBACb,MAAMpF,OAAO,GAAG,iBAAiB;sBACjCP,aAAa,CAACW,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEJ;sBAAQ,CAAC,CAAC,CAAC;sBAC7CK,gBAAgB,CAACb,UAAU,CAACI,aAAa,EAAEI,OAAO,CAAC;oBACrD,CAAE;oBACFgE,SAAS,EAAE,mEACTxE,UAAU,CAACQ,OAAO,KAAK,iBAAiB,GACpC,4CAA4C,GAC5C,gEAAgE,EACnE;oBAAAiE,QAAA,gBAEH7F,OAAA;sBAAM4F,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EAAC;oBAAe;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjE1F,OAAA;sBAAM4F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAEpD;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACT1F,OAAA;oBACEgH,OAAO,EAAEA,CAAA,KAAM;sBACb,MAAMpF,OAAO,GAAG,kBAAkB;sBAClCP,aAAa,CAACW,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEJ;sBAAQ,CAAC,CAAC,CAAC;sBAC7CK,gBAAgB,CAACb,UAAU,CAACI,aAAa,EAAEI,OAAO,CAAC;oBACrD,CAAE;oBACFgE,SAAS,EAAE,mEACTxE,UAAU,CAACQ,OAAO,KAAK,kBAAkB,GACrC,4CAA4C,GAC5C,gEAAgE,EACnE;oBAAAiE,QAAA,gBAEH7F,OAAA;sBAAM4F,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAClE1F,OAAA;sBAAM4F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAEpD;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACb,EAGAtE,UAAU,CAACI,aAAa,KAAK,WAAW,iBACvCxB,OAAA,CAACd,MAAM,CAAC+G,GAAG;gBACTC,OAAO,EAAE;kBAAEf,OAAO,EAAE,CAAC;kBAAEkC,CAAC,EAAE;gBAAG,CAAE;gBAC/BjB,OAAO,EAAE;kBAAEjB,OAAO,EAAE,CAAC;kBAAEkC,CAAC,EAAE;gBAAE,CAAE;gBAC9BzB,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,gBAE7E7F,OAAA;kBAAI4F,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAE1D;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1F,OAAA;kBAAK4F,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpD7F,OAAA;oBACEgH,OAAO,EAAEA,CAAA,KAAM;sBACb,MAAMpF,OAAO,GAAG,qBAAqB;sBACrCP,aAAa,CAACW,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEJ;sBAAQ,CAAC,CAAC,CAAC;sBAC7CK,gBAAgB,CAACb,UAAU,CAACI,aAAa,EAAEI,OAAO,CAAC;oBACrD,CAAE;oBACFgE,SAAS,EAAE,mEACTxE,UAAU,CAACQ,OAAO,KAAK,qBAAqB,GACxC,4CAA4C,GAC5C,gEAAgE,EACnE;oBAAAiE,QAAA,gBAEH7F,OAAA;sBAAM4F,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACrE1F,OAAA;sBAAM4F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAEpD;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACT1F,OAAA;oBACEgH,OAAO,EAAEA,CAAA,KAAM;sBACb,MAAMpF,OAAO,GAAG,uBAAuB;sBACvCP,aAAa,CAACW,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEJ;sBAAQ,CAAC,CAAC,CAAC;sBAC7CK,gBAAgB,CAACb,UAAU,CAACI,aAAa,EAAEI,OAAO,CAAC;oBACrD,CAAE;oBACFgE,SAAS,EAAE,mEACTxE,UAAU,CAACQ,OAAO,KAAK,uBAAuB,GAC1C,4CAA4C,GAC5C,gEAAgE,EACnE;oBAAAiE,QAAA,gBAEH7F,OAAA;sBAAM4F,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EAAC;oBAAqB;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACvE1F,OAAA;sBAAM4F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAEpD;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACb,eAGD1F,OAAA,CAACd,MAAM,CAAC+G,GAAG;gBACTC,OAAO,EAAE;kBAAEf,OAAO,EAAE,CAAC;kBAAEkC,CAAC,EAAE;gBAAG,CAAE;gBAC/BjB,OAAO,EAAE;kBAAEjB,OAAO,EAAE,CAAC;kBAAEkC,CAAC,EAAE;gBAAE,CAAE;gBAC9BzB,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,gBAE7E7F,OAAA;kBAAK4F,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrD7F,OAAA;oBAAI4F,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EACjD,CAACzE,UAAU,CAACI,aAAa,KAAK,0BAA0B,IACvDJ,UAAU,CAACI,aAAa,KAAK,sBAAsB,IACnDJ,UAAU,CAACI,aAAa,KAAK,WAAW,KAAKJ,UAAU,CAACQ,OAAO,GAC7D,GAAGR,UAAU,CAACI,aAAa,KAAKJ,UAAU,CAACQ,OAAO,aAAa,GAC/D,GAAGR,UAAU,CAACI,aAAa,IAAI,WAAW;kBAAe;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,EAEJtE,UAAU,CAACO,WAAW,IAAIP,UAAU,CAACO,WAAW,CAACqB,MAAM,GAAG,CAAC,iBAC1DhD,OAAA;oBAAK4F,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChC7F,OAAA;sBAAK4F,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,eACvD7F,OAAA;wBACE4F,SAAS,EAAC,iCAAiC;wBAC3CiC,KAAK,EAAE;0BACLC,KAAK,EAAE,GAAI1G,UAAU,CAACO,WAAW,CAACiD,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACR,SAAS,CAAC,CAACrB,MAAM,GAAG5B,UAAU,CAACO,WAAW,CAACqB,MAAM,GAAI,GAAG;wBAChH;sBAAE;wBAAAuC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN1F,OAAA;sBAAM4F,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GACpCzE,UAAU,CAACO,WAAW,CAACiD,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACR,SAAS,CAAC,CAACrB,MAAM,EAAC,GAAC,EAAC5B,UAAU,CAACO,WAAW,CAACqB,MAAM,EAAC,YAChG;oBAAA;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EAEL,CAACtE,UAAU,CAACI,aAAa,gBACxBxB,OAAA;kBAAK4F,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,eAC5D7F,OAAA;oBAAK4F,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB7F,OAAA;sBAAK4F,SAAS,EAAC,eAAe;sBAAAC,QAAA,eAC5B7F,OAAA;wBACE4F,SAAS,EAAC,yBAAyB;wBACnCS,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBAAAT,QAAA,eAEnB7F,OAAA;0BACEsH,QAAQ,EAAC,SAAS;0BAClBV,CAAC,EAAC,mNAAmN;0BACrNW,QAAQ,EAAC;wBAAS;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN1F,OAAA;sBAAK4F,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnB7F,OAAA;wBAAG4F,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EAAC;sBAEvC;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACJtE,UAAU,CAACI,aAAa,KAAK,0BAA0B,IAAI,CAACJ,UAAU,CAACQ,OAAO,gBAChF5B,OAAA;kBAAK4F,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,eAC5D7F,OAAA;oBAAK4F,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB7F,OAAA;sBAAK4F,SAAS,EAAC,eAAe;sBAAAC,QAAA,eAC5B7F,OAAA;wBACE4F,SAAS,EAAC,yBAAyB;wBACnCS,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBAAAT,QAAA,eAEnB7F,OAAA;0BACEsH,QAAQ,EAAC,SAAS;0BAClBV,CAAC,EAAC,mNAAmN;0BACrNW,QAAQ,EAAC;wBAAS;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN1F,OAAA;sBAAK4F,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnB7F,OAAA;wBAAG4F,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EAAC;sBAEvC;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACJtE,UAAU,CAACI,aAAa,KAAK,sBAAsB,IAAI,CAACJ,UAAU,CAACQ,OAAO,gBAC5E5B,OAAA;kBAAK4F,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,eAC5D7F,OAAA;oBAAK4F,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB7F,OAAA;sBAAK4F,SAAS,EAAC,eAAe;sBAAAC,QAAA,eAC5B7F,OAAA;wBACE4F,SAAS,EAAC,yBAAyB;wBACnCS,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBAAAT,QAAA,eAEnB7F,OAAA;0BACEsH,QAAQ,EAAC,SAAS;0BAClBV,CAAC,EAAC,mNAAmN;0BACrNW,QAAQ,EAAC;wBAAS;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN1F,OAAA;sBAAK4F,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnB7F,OAAA;wBAAG4F,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EAAC;sBAEvC;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACJtE,UAAU,CAACI,aAAa,KAAK,WAAW,IAAI,CAACJ,UAAU,CAACQ,OAAO,gBACjE5B,OAAA;kBAAK4F,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,eAC5D7F,OAAA;oBAAK4F,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB7F,OAAA;sBAAK4F,SAAS,EAAC,eAAe;sBAAAC,QAAA,eAC5B7F,OAAA;wBACE4F,SAAS,EAAC,yBAAyB;wBACnCS,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBAAAT,QAAA,eAEnB7F,OAAA;0BACEsH,QAAQ,EAAC,SAAS;0BAClBV,CAAC,EAAC,mNAAmN;0BACrNW,QAAQ,EAAC;wBAAS;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN1F,OAAA;sBAAK4F,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnB7F,OAAA;wBAAG4F,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EAAC;sBAEvC;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACJtE,UAAU,CAACO,WAAW,IAAIP,UAAU,CAACO,WAAW,CAACqB,MAAM,GAAG,CAAC,gBAC7DhD,OAAA;kBAAK4F,SAAS,EAAC,4DAA4D;kBAAAC,QAAA,eACzE7F,OAAA;oBAAK4F,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,EACtCzE,UAAU,CAACO,WAAW,CAACoG,GAAG,CAAC,CAAClD,IAAI,EAAEV,KAAK,kBACtCnE,OAAA;sBAEE4F,SAAS,EAAE,2DACTf,IAAI,CAACR,SAAS,GAAG,cAAc,GAAG,kBAAkB,EACnD;sBAAAwB,QAAA,gBAEH7F,OAAA;wBAAK4F,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,eAChC7F,OAAA;0BAAM4F,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBACjD7F,OAAA;4BAAM4F,SAAS,EAAC,2DAA2D;4BAAAC,QAAA,GAAE1B,KAAK,GAAG,CAAC,EAAC,GAAC;0BAAA;4BAAAoB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,EAC9Fb,IAAI,CAACmD,WAAW;wBAAA;0BAAAzC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACb;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACN1F,OAAA;wBACEgH,OAAO,EAAEA,CAAA,KAAM9C,gBAAgB,CAACC,KAAK,CAAE;wBACvCyB,SAAS,EAAE,4EACTf,IAAI,CAACR,SAAS,GACV,0BAA0B,GAC1B,2BAA2B,EAC9B;wBAAAwB,QAAA,EAEFhB,IAAI,CAACR,SAAS,gBACbrE,OAAA,CAAAE,SAAA;0BAAA2F,QAAA,gBACE7F,OAAA,CAACL,aAAa;4BAACiG,SAAS,EAAC;0BAAc;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAC1C1F,OAAA;4BAAM4F,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,EAAC;0BAAO;4BAAAN,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA,eACpD,CAAC,gBAEH1F,OAAA,CAAAE,SAAA;0BAAA2F,QAAA,gBACE7F,OAAA,CAACJ,aAAa;4BAACgG,SAAS,EAAC;0BAAc;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAC1C1F,OAAA;4BAAM4F,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,EAAC;0BAAQ;4BAAAN,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA,eACrD;sBACH;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACK,CAAC;oBAAA,GA9BJvB,KAAK;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+BP,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEN1F,OAAA;kBAAK4F,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,eAC5D7F,OAAA;oBAAK4F,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB7F,OAAA;sBAAK4F,SAAS,EAAC,eAAe;sBAAAC,QAAA,eAC5B7F,OAAA;wBACE4F,SAAS,EAAC,yBAAyB;wBACnCS,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBAAAT,QAAA,eAEnB7F,OAAA;0BACEsH,QAAQ,EAAC,SAAS;0BAClBV,CAAC,EAAC,mNAAmN;0BACrNW,QAAQ,EAAC;wBAAS;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN1F,OAAA;sBAAK4F,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnB7F,OAAA;wBAAG4F,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EAAC;sBAEvC;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC,eAGb1F,OAAA,CAACd,MAAM,CAAC+G,GAAG;gBACTC,OAAO,EAAE;kBAAEf,OAAO,EAAE,CAAC;kBAAEkC,CAAC,EAAE;gBAAG,CAAE;gBAC/BjB,OAAO,EAAE;kBAAEjB,OAAO,EAAE,CAAC;kBAAEkC,CAAC,EAAE;gBAAE,CAAE;gBAC9BzB,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,gBAE7E7F,OAAA;kBAAI4F,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAA2B;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1F1F,OAAA;kBACEiI,WAAW,EAAC,iDAAiD;kBAC7DxD,KAAK,EAAErD,UAAU,CAACM,IAAK;kBACvBgG,QAAQ,EAAEpD,gBAAiB;kBAC3BsB,SAAS,EAAC,sFAAsF;kBAChGsC,IAAI,EAAC;gBAAG;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAGb1F,OAAA,CAACd,MAAM,CAAC+G,GAAG;gBACTC,OAAO,EAAE;kBAAEf,OAAO,EAAE,CAAC;kBAAEkC,CAAC,EAAE;gBAAG,CAAE;gBAC/BjB,OAAO,EAAE;kBAAEjB,OAAO,EAAE,CAAC;kBAAEkC,CAAC,EAAE;gBAAE,CAAE;gBAC9BzB,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAE5B7F,OAAA,CAACd,MAAM,CAAC2H,MAAM;kBACZC,UAAU,EAAEhG,OAAO,GAAG,CAAC,CAAC,GAAG;oBAAEqF,KAAK,EAAE;kBAAK,CAAE;kBAC3CY,QAAQ,EAAEjG,OAAO,GAAG,CAAC,CAAC,GAAG;oBAAEqF,KAAK,EAAE;kBAAK,CAAE;kBACzCa,OAAO,EAAEtC,kBAAmB;kBAC5ByD,QAAQ,EACNrH,OAAO,IACP,CAACM,UAAU,CAACI,aAAa,IACzBJ,UAAU,CAACO,WAAW,CAACqB,MAAM,KAAK,CAAC,IAClC5B,UAAU,CAACI,aAAa,KAAK,0BAA0B,IAAI,CAACJ,UAAU,CAACQ,OAAQ,IAC/ER,UAAU,CAACI,aAAa,KAAK,sBAAsB,IAAI,CAACJ,UAAU,CAACQ,OAAQ,IAC3ER,UAAU,CAACI,aAAa,KAAK,WAAW,IAAI,CAACJ,UAAU,CAACQ,OAC1D;kBACDgE,SAAS,EAAE,0CACT9E,OAAO,GACH,wCAAwC,GACxC,CAACM,UAAU,CAACI,aAAa,IACzBJ,UAAU,CAACO,WAAW,CAACqB,MAAM,KAAK,CAAC,IAClC5B,UAAU,CAACI,aAAa,KAAK,0BAA0B,IAAI,CAACJ,UAAU,CAACQ,OAAQ,IAC/ER,UAAU,CAACI,aAAa,KAAK,sBAAsB,IAAI,CAACJ,UAAU,CAACQ,OAAQ,IAC3ER,UAAU,CAACI,aAAa,KAAK,WAAW,IAAI,CAACJ,UAAU,CAACQ,OAAQ,GAC/D,8CAA8C,GAC9C,mFAAmF,EACxF;kBAAAiE,QAAA,EAEF/E,OAAO,gBACNd,OAAA,CAAAE,SAAA;oBAAA2F,QAAA,gBACE7F,OAAA,CAACF,SAAS;sBAAC8F,SAAS,EAAC;oBAA2B;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,iBAErD;kBAAA,eAAE,CAAC,gBAEH1F,OAAA,CAAAE,SAAA;oBAAA2F,QAAA,gBACE7F,OAAA,CAACH,MAAM;sBAAC+F,SAAS,EAAC;oBAAc;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,iBAErC;kBAAA,eAAE;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,eACb,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrF,EAAA,CAl7BID,WAAW;EAAA,QACQpB,SAAS,EACfC,WAAW,EACJG,OAAO;AAAA;AAAAgJ,EAAA,GAH3BhI,WAAW;AAo7BjB,eAAeA,WAAW;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}