[{"D:\\Dentlyzer\\dentlyzer-frontend\\src\\index.js": "1", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\App.js": "2", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\reportWebVitals.js": "3", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Home.jsx": "4", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Loader.jsx": "5", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Footer.jsx": "6", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\i18n.js": "7", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Navbar.jsx": "8", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicServices.jsx": "9", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Contact.jsx": "10", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx": "11", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Login.jsx": "12", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\About.jsx": "13", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\TryAI.jsx": "14", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx": "15", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\DentistInfo.jsx": "16", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Universities.jsx": "17", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Clinics.jsx": "18", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicConfirmation.jsx": "19", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Payment.jsx": "20", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Select.jsx": "21", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx": "22", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicBook.jsx": "23", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx": "24", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\router.js": "25", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\context\\AuthContext.js": "26", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Dashboard.jsx": "27", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\XRay.jsx": "28", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Gallery.jsx": "29", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Analytics.jsx": "30", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Appointments.jsx": "31", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\ToothChart.jsx": "32", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Reviews.jsx": "33", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Patients.jsx": "34", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx": "35", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx": "36", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Calendar.jsx": "37", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Calendar.jsx": "38", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx": "39", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Dashboard.jsx": "40", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Analytics.jsx": "41", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Patients.jsx": "42", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Gallery.jsx": "43", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\XRay.jsx": "44", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\PatientProfile.jsx": "45", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Appointments.jsx": "46", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\ToothChart.jsx": "47", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx": "48", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Navbar.jsx": "49", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\PatientNav.jsx": "50", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Sidebar.jsx": "51", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Navbar.jsx": "52", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Sidebar.jsx": "53", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\PatientNav.jsx": "54", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx": "55", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Profile.jsx": "56", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx": "57", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Analytics.jsx": "58", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Appointments.jsx": "59", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\News.jsx": "60", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Reviews.jsx": "61", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\people.jsx": "62", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\People.jsx": "63", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\index.js": "64", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\reportWebVitals.js": "65", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\App.js": "66", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\router.js": "67", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\i18n.js": "68", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\context\\AuthContext.js": "69", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Profile.jsx": "70", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Login.jsx": "71", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicServices.jsx": "72", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Home.jsx": "73", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx": "74", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\DentistInfo.jsx": "75", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Contact.jsx": "76", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\TryAI.jsx": "77", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\About.jsx": "78", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Select.jsx": "79", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx": "80", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Clinics.jsx": "81", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicConfirmation.jsx": "82", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Universities.jsx": "83", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx": "84", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicBook.jsx": "85", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx": "86", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Payment.jsx": "87", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Dashboard.jsx": "88", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Patients.jsx": "89", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx": "90", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Gallery.jsx": "91", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Calendar.jsx": "92", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Appointments.jsx": "93", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Reviews.jsx": "94", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Analytics.jsx": "95", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\XRay.jsx": "96", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx": "97", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\ToothChart.jsx": "98", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx": "99", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\People.jsx": "100", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx": "101", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Reviews.jsx": "102", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Appointments.jsx": "103", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Analytics.jsx": "104", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\News.jsx": "105", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Dashboard.jsx": "106", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\XRay.jsx": "107", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Patients.jsx": "108", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Appointments.jsx": "109", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\PatientProfile.jsx": "110", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Analytics.jsx": "111", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\ToothChart.jsx": "112", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Calendar.jsx": "113", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Gallery.jsx": "114", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx": "115", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Sidebar.jsx": "116", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Loader.jsx": "117", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Navbar.jsx": "118", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Footer.jsx": "119", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Navbar.jsx": "120", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PatientNav.jsx": "121", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx": "122", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\PatientNav.jsx": "123", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Sidebar.jsx": "124", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Navbar.jsx": "125", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ForgotPassword.jsx": "126", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\SuperAdminSidebar.jsx": "127", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Analytics.jsx": "128", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Universities.jsx": "129", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\News.jsx": "130", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Accounts.jsx": "131", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Clinics.jsx": "132", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ConfirmModal.jsx": "133", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\UniversityDetailsModal.jsx": "134", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Sheets.jsx": "135", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\FixedProsthodonticsSheet.jsx": "136", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\History.jsx": "137", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\EndodonticSheet.jsx": "138", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\OperativeSheet.jsx": "139", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\RemovableProsthodonticsSheet.jsx": "140", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ClinicDetailsModal.jsx": "141", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\SuccessModal.jsx": "142", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Consent.jsx": "143", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PeriodonticsSheet.jsx": "144", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\ReviewStepsDisplay.jsx": "145", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\SignatureManager.jsx": "146", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Support.jsx": "147", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Appointments.jsx": "148", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Dashboard.jsx": "149", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Analytics.jsx": "150", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AssistantSidebar.jsx": "151", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AssignStudentModal.jsx": "152", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AppointmentDetailsModal.jsx": "153", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\MedicalTab.jsx": "154", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\index.js": "155", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\reportWebVitals.js": "156", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\App.js": "157", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\router.js": "158", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\context\\AuthContext.js": "159", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\i18n.js": "160", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Support.jsx": "161", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Profile.jsx": "162", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ForgotPassword.jsx": "163", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Home.jsx": "164", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx": "165", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Contact.jsx": "166", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Login.jsx": "167", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\About.jsx": "168", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Universities.jsx": "169", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\TryAI.jsx": "170", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx": "171", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx": "172", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx": "173", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Dashboard.jsx": "174", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Gallery.jsx": "175", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Patients.jsx": "176", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Calendar.jsx": "177", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Analytics.jsx": "178", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Sheets.jsx": "179", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx": "180", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Reviews.jsx": "181", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx": "182", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ToothChart.jsx": "183", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Appointments.jsx": "184", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Consent.jsx": "185", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\History.jsx": "186", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx": "187", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\People.jsx": "188", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\News.jsx": "189", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Appointments.jsx": "190", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Reviews.jsx": "191", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Analytics.jsx": "192", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx": "193", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Universities.jsx": "194", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx": "195", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Clinics.jsx": "196", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\News.jsx": "197", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Accounts.jsx": "198", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Analytics.jsx": "199", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Dashboard.jsx": "200", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Appointments.jsx": "201", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Analytics.jsx": "202", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Sidebar.jsx": "203", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Navbar.jsx": "204", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Loader.jsx": "205", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Footer.jsx": "206", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Navbar.jsx": "207", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PatientNav.jsx": "208", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\OperativeSheet.jsx": "209", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\FixedProsthodonticsSheet.jsx": "210", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\EndodonticSheet.jsx": "211", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PeriodonticsSheet.jsx": "212", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\RemovableProsthodonticsSheet.jsx": "213", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx": "214", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\ReviewStepsDisplay.jsx": "215", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\SignatureManager.jsx": "216", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ConfirmModal.jsx": "217", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\UniversityDetailsModal.jsx": "218", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\SuperAdminSidebar.jsx": "219", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\SuccessModal.jsx": "220", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ClinicDetailsModal.jsx": "221", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AssistantSidebar.jsx": "222", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AssignStudentModal.jsx": "223", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AppointmentDetailsModal.jsx": "224", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\MedicalTab.jsx": "225", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\ProcedureRequests.jsx": "226", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ProcedureRequestsWidget.jsx": "227", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\AppointmentsWidget.jsx": "228", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\utils\\pdfUtils.js": "229", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Activity.jsx": "230"}, {"size": 535, "mtime": 1745523871993, "results": "231", "hashOfConfig": "232"}, {"size": 344, "mtime": 1745593572980, "results": "233", "hashOfConfig": "232"}, {"size": 362, "mtime": 1745521607447, "results": "234", "hashOfConfig": "232"}, {"size": 25522, "mtime": 1746026848966, "results": "235", "hashOfConfig": "232"}, {"size": 4449, "mtime": 1746028375819, "results": "236", "hashOfConfig": "232"}, {"size": 16619, "mtime": 1746027908524, "results": "237", "hashOfConfig": "232"}, {"size": 41067, "mtime": 1746027767487, "results": "238", "hashOfConfig": "232"}, {"size": 15000, "mtime": 1746027574659, "results": "239", "hashOfConfig": "232"}, {"size": 12368, "mtime": 1745533141636, "results": "240", "hashOfConfig": "232"}, {"size": 13375, "mtime": 1745936744323, "results": "241", "hashOfConfig": "232"}, {"size": 12529, "mtime": 1745533076800, "results": "242", "hashOfConfig": "232"}, {"size": 8477, "mtime": 1745593861157, "results": "243", "hashOfConfig": "232"}, {"size": 16004, "mtime": 1745936541582, "results": "244", "hashOfConfig": "232"}, {"size": 25091, "mtime": 1745537009810, "results": "245", "hashOfConfig": "232"}, {"size": 19988, "mtime": 1745737631275, "results": "246", "hashOfConfig": "232"}, {"size": 17000, "mtime": 1745737806371, "results": "247", "hashOfConfig": "232"}, {"size": 10462, "mtime": 1745736150751, "results": "248", "hashOfConfig": "232"}, {"size": 10639, "mtime": 1745737746063, "results": "249", "hashOfConfig": "232"}, {"size": 0, "mtime": 1745579442828, "results": "250", "hashOfConfig": "232"}, {"size": 0, "mtime": 1745579449398, "results": "251", "hashOfConfig": "232"}, {"size": 4794, "mtime": 1745937254876, "results": "252", "hashOfConfig": "232"}, {"size": 17070, "mtime": 1745938807584, "results": "253", "hashOfConfig": "232"}, {"size": 0, "mtime": 1745579393455, "results": "254", "hashOfConfig": "232"}, {"size": 14691, "mtime": 1745777641172, "results": "255", "hashOfConfig": "232"}, {"size": 8330, "mtime": 1745869519257, "results": "256", "hashOfConfig": "232"}, {"size": 2667, "mtime": 1745659005519, "results": "257", "hashOfConfig": "232"}, {"size": 38724, "mtime": 1745692735196, "results": "258", "hashOfConfig": "232"}, {"size": 11723, "mtime": 1745617858943, "results": "259", "hashOfConfig": "232"}, {"size": 12455, "mtime": 1745617379798, "results": "260", "hashOfConfig": "232"}, {"size": 17414, "mtime": 1745781818524, "results": "261", "hashOfConfig": "232"}, {"size": 27311, "mtime": 1745686427761, "results": "262", "hashOfConfig": "232"}, {"size": 72447, "mtime": 1745704882940, "results": "263", "hashOfConfig": "232"}, {"size": 29816, "mtime": 1745790566641, "results": "264", "hashOfConfig": "232"}, {"size": 33179, "mtime": 1745623491029, "results": "265", "hashOfConfig": "232"}, {"size": 24357, "mtime": 1745655454135, "results": "266", "hashOfConfig": "232"}, {"size": 13189, "mtime": 1745869227266, "results": "267", "hashOfConfig": "232"}, {"size": 28031, "mtime": 1745696382431, "results": "268", "hashOfConfig": "232"}, {"size": 20716, "mtime": 1745593781630, "results": "269", "hashOfConfig": "232"}, {"size": 0, "mtime": 1745265060947, "results": "270", "hashOfConfig": "232"}, {"size": 34793, "mtime": 1745593788566, "results": "271", "hashOfConfig": "232"}, {"size": 0, "mtime": 1745781813233, "results": "272", "hashOfConfig": "232"}, {"size": 33183, "mtime": 1745593804781, "results": "273", "hashOfConfig": "232"}, {"size": 6170, "mtime": 1743975365356, "results": "274", "hashOfConfig": "232"}, {"size": 6117, "mtime": 1743975382403, "results": "275", "hashOfConfig": "232"}, {"size": 13795, "mtime": 1744193211886, "results": "276", "hashOfConfig": "232"}, {"size": 15608, "mtime": 1744192669925, "results": "277", "hashOfConfig": "232"}, {"size": 54388, "mtime": 1745274429676, "results": "278", "hashOfConfig": "232"}, {"size": 33472, "mtime": 1745792982698, "results": "279", "hashOfConfig": "232"}, {"size": 3201, "mtime": 1745616939355, "results": "280", "hashOfConfig": "232"}, {"size": 10848, "mtime": 1745781804706, "results": "281", "hashOfConfig": "232"}, {"size": 3380, "mtime": 1745617128650, "results": "282", "hashOfConfig": "232"}, {"size": 4227, "mtime": 1745266557952, "results": "283", "hashOfConfig": "232"}, {"size": 4300, "mtime": 1745260321900, "results": "284", "hashOfConfig": "232"}, {"size": 8692, "mtime": 1744048711130, "results": "285", "hashOfConfig": "232"}, {"size": 24004, "mtime": 1745789621184, "results": "286", "hashOfConfig": "232"}, {"size": 5842, "mtime": 1745909321963, "results": "287", "hashOfConfig": "232"}, {"size": 3575, "mtime": 1745869264459, "results": "288", "hashOfConfig": "232"}, {"size": 8028, "mtime": 1745867656061, "results": "289", "hashOfConfig": "232"}, {"size": 13592, "mtime": 1745867635690, "results": "290", "hashOfConfig": "232"}, {"size": 6292, "mtime": 1745869103376, "results": "291", "hashOfConfig": "232"}, {"size": 11714, "mtime": 1745869124338, "results": "292", "hashOfConfig": "232"}, {"size": 13725, "mtime": 1745869505266, "results": "293", "hashOfConfig": "232"}, {"size": 18292, "mtime": 1745916840861, "results": "294", "hashOfConfig": "232"}, {"size": 535, "mtime": 1745523871993, "results": "295", "hashOfConfig": "296"}, {"size": 362, "mtime": 1745521607447, "results": "297", "hashOfConfig": "296"}, {"size": 344, "mtime": 1745593572980, "results": "298", "hashOfConfig": "296"}, {"size": 10910, "mtime": 1747475995764, "results": "299", "hashOfConfig": "296"}, {"size": 50383, "mtime": 1747404842150, "results": "300", "hashOfConfig": "296"}, {"size": 2667, "mtime": 1745659005519, "results": "301", "hashOfConfig": "296"}, {"size": 5850, "mtime": 1747428149748, "results": "302", "hashOfConfig": "296"}, {"size": 10684, "mtime": 1747425747846, "results": "303", "hashOfConfig": "296"}, {"size": 12527, "mtime": 1747425232861, "results": "304", "hashOfConfig": "296"}, {"size": 25785, "mtime": 1747325877026, "results": "305", "hashOfConfig": "296"}, {"size": 12683, "mtime": 1747424765007, "results": "306", "hashOfConfig": "296"}, {"size": 18324, "mtime": 1747494439022, "results": "307", "hashOfConfig": "296"}, {"size": 17132, "mtime": 1747424064804, "results": "308", "hashOfConfig": "296"}, {"size": 25328, "mtime": 1747478452947, "results": "309", "hashOfConfig": "296"}, {"size": 18234, "mtime": 1747423594113, "results": "310", "hashOfConfig": "296"}, {"size": 3589, "mtime": 1747425356350, "results": "311", "hashOfConfig": "296"}, {"size": 22212, "mtime": 1747494311372, "results": "312", "hashOfConfig": "296"}, {"size": 14883, "mtime": 1747494399105, "results": "313", "hashOfConfig": "296"}, {"size": 0, "mtime": 1745579442828, "results": "314", "hashOfConfig": "296"}, {"size": 11180, "mtime": 1747494346747, "results": "315", "hashOfConfig": "296"}, {"size": 15376, "mtime": 1747479345968, "results": "316", "hashOfConfig": "296"}, {"size": 0, "mtime": 1745579393455, "results": "317", "hashOfConfig": "296"}, {"size": 17301, "mtime": 1747479769046, "results": "318", "hashOfConfig": "296"}, {"size": 0, "mtime": 1745579449398, "results": "319", "hashOfConfig": "296"}, {"size": 40674, "mtime": 1747514654819, "results": "320", "hashOfConfig": "296"}, {"size": 46027, "mtime": 1747514862877, "results": "321", "hashOfConfig": "296"}, {"size": 27784, "mtime": 1747476930996, "results": "322", "hashOfConfig": "296"}, {"size": 19974, "mtime": 1747476730513, "results": "323", "hashOfConfig": "296"}, {"size": 28285, "mtime": 1747465927821, "results": "324", "hashOfConfig": "296"}, {"size": 27390, "mtime": 1747475380406, "results": "325", "hashOfConfig": "296"}, {"size": 38168, "mtime": 1747466995840, "results": "326", "hashOfConfig": "296"}, {"size": 62529, "mtime": 1747466715266, "results": "327", "hashOfConfig": "296"}, {"size": 21853, "mtime": 1746816821604, "results": "328", "hashOfConfig": "296"}, {"size": 46169, "mtime": 1747476516386, "results": "329", "hashOfConfig": "296"}, {"size": 72726, "mtime": 1747477357174, "results": "330", "hashOfConfig": "296"}, {"size": 85588, "mtime": 1747503514872, "results": "331", "hashOfConfig": "296"}, {"size": 42190, "mtime": 1747519685845, "results": "332", "hashOfConfig": "296"}, {"size": 37741, "mtime": 1747521640488, "results": "333", "hashOfConfig": "296"}, {"size": 56736, "mtime": 1747520158742, "results": "334", "hashOfConfig": "296"}, {"size": 20139, "mtime": 1747545610769, "results": "335", "hashOfConfig": "296"}, {"size": 54586, "mtime": 1747521219705, "results": "336", "hashOfConfig": "296"}, {"size": 9955, "mtime": 1747521450018, "results": "337", "hashOfConfig": "296"}, {"size": 34793, "mtime": 1745593788566, "results": "338", "hashOfConfig": "296"}, {"size": 6117, "mtime": 1743975382403, "results": "339", "hashOfConfig": "296"}, {"size": 33183, "mtime": 1745593804781, "results": "340", "hashOfConfig": "296"}, {"size": 15608, "mtime": 1744192669925, "results": "341", "hashOfConfig": "296"}, {"size": 13795, "mtime": 1744193211886, "results": "342", "hashOfConfig": "296"}, {"size": 0, "mtime": 1745781813233, "results": "343", "hashOfConfig": "296"}, {"size": 49478, "mtime": 1747337212647, "results": "344", "hashOfConfig": "296"}, {"size": 20716, "mtime": 1745593781630, "results": "345", "hashOfConfig": "296"}, {"size": 6170, "mtime": 1743975365356, "results": "346", "hashOfConfig": "296"}, {"size": 101715, "mtime": 1747543720582, "results": "347", "hashOfConfig": "296"}, {"size": 3543, "mtime": 1747464059605, "results": "348", "hashOfConfig": "296"}, {"size": 4449, "mtime": 1746028375819, "results": "349", "hashOfConfig": "296"}, {"size": 15112, "mtime": 1747427483008, "results": "350", "hashOfConfig": "296"}, {"size": 16661, "mtime": 1747426879726, "results": "351", "hashOfConfig": "296"}, {"size": 3924, "mtime": 1747464099492, "results": "352", "hashOfConfig": "296"}, {"size": 9285, "mtime": 1747476809201, "results": "353", "hashOfConfig": "296"}, {"size": 4038, "mtime": 1747517788574, "results": "354", "hashOfConfig": "296"}, {"size": 8692, "mtime": 1744048711130, "results": "355", "hashOfConfig": "296"}, {"size": 4433, "mtime": 1746555422128, "results": "356", "hashOfConfig": "296"}, {"size": 4227, "mtime": 1745266557952, "results": "357", "hashOfConfig": "296"}, {"size": 6776, "mtime": 1746130339157, "results": "358", "hashOfConfig": "296"}, {"size": 3694, "mtime": 1746555400983, "results": "359", "hashOfConfig": "296"}, {"size": 32962, "mtime": 1746489951667, "results": "360", "hashOfConfig": "296"}, {"size": 46904, "mtime": 1747543535318, "results": "361", "hashOfConfig": "296"}, {"size": 21956, "mtime": 1746178385159, "results": "362", "hashOfConfig": "296"}, {"size": 52633, "mtime": 1746471592266, "results": "363", "hashOfConfig": "296"}, {"size": 45474, "mtime": 1746383841136, "results": "364", "hashOfConfig": "296"}, {"size": 1366, "mtime": 1746184118198, "results": "365", "hashOfConfig": "296"}, {"size": 15581, "mtime": 1747543564076, "results": "366", "hashOfConfig": "296"}, {"size": 14747, "mtime": 1747475215108, "results": "367", "hashOfConfig": "296"}, {"size": 53245, "mtime": 1747474059613, "results": "368", "hashOfConfig": "296"}, {"size": 23518, "mtime": 1747516218200, "results": "369", "hashOfConfig": "296"}, {"size": 38503, "mtime": 1747474312377, "results": "370", "hashOfConfig": "296"}, {"size": 9088, "mtime": 1747474414469, "results": "371", "hashOfConfig": "296"}, {"size": 19305, "mtime": 1747474671635, "results": "372", "hashOfConfig": "296"}, {"size": 14545, "mtime": 1746383814479, "results": "373", "hashOfConfig": "296"}, {"size": 1606, "mtime": 1746380988907, "results": "374", "hashOfConfig": "296"}, {"size": 22427, "mtime": 1747476255874, "results": "375", "hashOfConfig": "296"}, {"size": 59674, "mtime": 1747544383255, "results": "376", "hashOfConfig": "296"}, {"size": 3136, "mtime": 1747501530874, "results": "377", "hashOfConfig": "296"}, {"size": 18202, "mtime": 1747501681339, "results": "378", "hashOfConfig": "296"}, {"size": 11061, "mtime": 1747427987728, "results": "379", "hashOfConfig": "296"}, {"size": 33942, "mtime": 1747544346809, "results": "380", "hashOfConfig": "296"}, {"size": 21921, "mtime": 1747508196749, "results": "381", "hashOfConfig": "296"}, {"size": 19707, "mtime": 1747508361654, "results": "382", "hashOfConfig": "296"}, {"size": 3855, "mtime": 1747506687993, "results": "383", "hashOfConfig": "296"}, {"size": 12486, "mtime": 1747510357937, "results": "384", "hashOfConfig": "296"}, {"size": 13064, "mtime": 1747544432606, "results": "385", "hashOfConfig": "296"}, {"size": 18365, "mtime": 1747468109841, "results": "386", "hashOfConfig": "296"}, {"size": 535, "mtime": 1745523871993, "results": "387", "hashOfConfig": "388"}, {"size": 362, "mtime": 1745521607447, "results": "389", "hashOfConfig": "388"}, {"size": 344, "mtime": 1745593572980, "results": "390", "hashOfConfig": "388"}, {"size": 10257, "mtime": 1748804925982, "results": "391", "hashOfConfig": "388"}, {"size": 2667, "mtime": 1745659005519, "results": "392", "hashOfConfig": "388"}, {"size": 50244, "mtime": 1748027195662, "results": "393", "hashOfConfig": "388"}, {"size": 11061, "mtime": 1747427987728, "results": "394", "hashOfConfig": "388"}, {"size": 5850, "mtime": 1747428149748, "results": "395", "hashOfConfig": "388"}, {"size": 6776, "mtime": 1746130339157, "results": "396", "hashOfConfig": "388"}, {"size": 26294, "mtime": 1748027939369, "results": "397", "hashOfConfig": "388"}, {"size": 12887, "mtime": 1748031791438, "results": "398", "hashOfConfig": "388"}, {"size": 16450, "mtime": 1748016139440, "results": "399", "hashOfConfig": "388"}, {"size": 11059, "mtime": 1748031616139, "results": "400", "hashOfConfig": "388"}, {"size": 18211, "mtime": 1748029564302, "results": "401", "hashOfConfig": "388"}, {"size": 11180, "mtime": 1747494346747, "results": "402", "hashOfConfig": "388"}, {"size": 25328, "mtime": 1747478452947, "results": "403", "hashOfConfig": "388"}, {"size": 22212, "mtime": 1747494311372, "results": "404", "hashOfConfig": "388"}, {"size": 17301, "mtime": 1747479769046, "results": "405", "hashOfConfig": "388"}, {"size": 15376, "mtime": 1747479345968, "results": "406", "hashOfConfig": "388"}, {"size": 23096, "mtime": 1748045293068, "results": "407", "hashOfConfig": "388"}, {"size": 19974, "mtime": 1747476730513, "results": "408", "hashOfConfig": "388"}, {"size": 44909, "mtime": 1748039359645, "results": "409", "hashOfConfig": "388"}, {"size": 28285, "mtime": 1747465927821, "results": "410", "hashOfConfig": "388"}, {"size": 66253, "mtime": 1750607492404, "results": "411", "hashOfConfig": "388"}, {"size": 14747, "mtime": 1747475215108, "results": "412", "hashOfConfig": "388"}, {"size": 27784, "mtime": 1747476930996, "results": "413", "hashOfConfig": "388"}, {"size": 38144, "mtime": 1750605858561, "results": "414", "hashOfConfig": "388"}, {"size": 46171, "mtime": 1750605874307, "results": "415", "hashOfConfig": "388"}, {"size": 72726, "mtime": 1747477357174, "results": "416", "hashOfConfig": "388"}, {"size": 27390, "mtime": 1747475380406, "results": "417", "hashOfConfig": "388"}, {"size": 28023, "mtime": 1750606100154, "results": "418", "hashOfConfig": "388"}, {"size": 26506, "mtime": 1748045700910, "results": "419", "hashOfConfig": "388"}, {"size": 37741, "mtime": 1747521640488, "results": "420", "hashOfConfig": "388"}, {"size": 42190, "mtime": 1747519685845, "results": "421", "hashOfConfig": "388"}, {"size": 9955, "mtime": 1747521450018, "results": "422", "hashOfConfig": "388"}, {"size": 20139, "mtime": 1747545610769, "results": "423", "hashOfConfig": "388"}, {"size": 56736, "mtime": 1747520158742, "results": "424", "hashOfConfig": "388"}, {"size": 54586, "mtime": 1747521219705, "results": "425", "hashOfConfig": "388"}, {"size": 85588, "mtime": 1747503514872, "results": "426", "hashOfConfig": "388"}, {"size": 47191, "mtime": 1748802395451, "results": "427", "hashOfConfig": "388"}, {"size": 100845, "mtime": 1748803051039, "results": "428", "hashOfConfig": "388"}, {"size": 45474, "mtime": 1746383841136, "results": "429", "hashOfConfig": "388"}, {"size": 21595, "mtime": 1748805302693, "results": "430", "hashOfConfig": "388"}, {"size": 52779, "mtime": 1748803389900, "results": "431", "hashOfConfig": "388"}, {"size": 50998, "mtime": 1748808216624, "results": "432", "hashOfConfig": "388"}, {"size": 21921, "mtime": 1747508196749, "results": "433", "hashOfConfig": "388"}, {"size": 33942, "mtime": 1747544346809, "results": "434", "hashOfConfig": "388"}, {"size": 19707, "mtime": 1747508361654, "results": "435", "hashOfConfig": "388"}, {"size": 3649, "mtime": 1748041735004, "results": "436", "hashOfConfig": "388"}, {"size": 3924, "mtime": 1747464099492, "results": "437", "hashOfConfig": "388"}, {"size": 3964, "mtime": 1748031168996, "results": "438", "hashOfConfig": "388"}, {"size": 16360, "mtime": 1748031453441, "results": "439", "hashOfConfig": "388"}, {"size": 11916, "mtime": 1748030716874, "results": "440", "hashOfConfig": "388"}, {"size": 9285, "mtime": 1747476809201, "results": "441", "hashOfConfig": "388"}, {"size": 10703, "mtime": 1748045792103, "results": "442", "hashOfConfig": "388"}, {"size": 55507, "mtime": 1748045908880, "results": "443", "hashOfConfig": "388"}, {"size": 40213, "mtime": 1748045992367, "results": "444", "hashOfConfig": "388"}, {"size": 62904, "mtime": 1748046208783, "results": "445", "hashOfConfig": "388"}, {"size": 21499, "mtime": 1748046110445, "results": "446", "hashOfConfig": "388"}, {"size": 4038, "mtime": 1747517788574, "results": "447", "hashOfConfig": "388"}, {"size": 3136, "mtime": 1747501530874, "results": "448", "hashOfConfig": "388"}, {"size": 18202, "mtime": 1747501681339, "results": "449", "hashOfConfig": "388"}, {"size": 1366, "mtime": 1746184118198, "results": "450", "hashOfConfig": "388"}, {"size": 15581, "mtime": 1747543564076, "results": "451", "hashOfConfig": "388"}, {"size": 3605, "mtime": 1748802994868, "results": "452", "hashOfConfig": "388"}, {"size": 1606, "mtime": 1746380988907, "results": "453", "hashOfConfig": "388"}, {"size": 14545, "mtime": 1746383814479, "results": "454", "hashOfConfig": "388"}, {"size": 3996, "mtime": 1748039584454, "results": "455", "hashOfConfig": "388"}, {"size": 12486, "mtime": 1747510357937, "results": "456", "hashOfConfig": "388"}, {"size": 13064, "mtime": 1747544432606, "results": "457", "hashOfConfig": "388"}, {"size": 18365, "mtime": 1747468109841, "results": "458", "hashOfConfig": "388"}, {"size": 18702, "mtime": 1748041839217, "results": "459", "hashOfConfig": "388"}, {"size": 3419, "mtime": 1748043262730, "results": "460", "hashOfConfig": "388"}, {"size": 5567, "mtime": 1748043390657, "results": "461", "hashOfConfig": "388"}, {"size": 10432, "mtime": 1748045669415, "results": "462", "hashOfConfig": "388"}, {"size": 15075, "mtime": 1748803110961, "results": "463", "hashOfConfig": "388"}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1y3igld", {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "18v30mu", {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "887", "messages": "888", "suppressedMessages": "889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "890", "messages": "891", "suppressedMessages": "892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "902", "messages": "903", "suppressedMessages": "904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "905", "messages": "906", "suppressedMessages": "907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "908", "messages": "909", "suppressedMessages": "910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "911", "messages": "912", "suppressedMessages": "913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "914", "messages": "915", "suppressedMessages": "916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "917", "messages": "918", "suppressedMessages": "919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gwk9g1", {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "962", "messages": "963", "suppressedMessages": "964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "965", "messages": "966", "suppressedMessages": "967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "968", "messages": "969", "suppressedMessages": "970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "971", "messages": "972", "suppressedMessages": "973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "974", "messages": "975", "suppressedMessages": "976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "977", "messages": "978", "suppressedMessages": "979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "980", "messages": "981", "suppressedMessages": "982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "983", "messages": "984", "suppressedMessages": "985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "986", "messages": "987", "suppressedMessages": "988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "989", "messages": "990", "suppressedMessages": "991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "992", "messages": "993", "suppressedMessages": "994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "995", "messages": "996", "suppressedMessages": "997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "998", "messages": "999", "suppressedMessages": "1000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1001", "messages": "1002", "suppressedMessages": "1003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1004", "messages": "1005", "suppressedMessages": "1006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1007", "messages": "1008", "suppressedMessages": "1009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1010", "messages": "1011", "suppressedMessages": "1012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1013", "messages": "1014", "suppressedMessages": "1015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1016", "messages": "1017", "suppressedMessages": "1018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1019", "messages": "1020", "suppressedMessages": "1021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1022", "messages": "1023", "suppressedMessages": "1024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1025", "messages": "1026", "suppressedMessages": "1027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1028", "messages": "1029", "suppressedMessages": "1030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1031", "messages": "1032", "suppressedMessages": "1033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1034", "messages": "1035", "suppressedMessages": "1036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1037", "messages": "1038", "suppressedMessages": "1039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1040", "messages": "1041", "suppressedMessages": "1042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1043", "messages": "1044", "suppressedMessages": "1045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1046", "messages": "1047", "suppressedMessages": "1048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1049", "messages": "1050", "suppressedMessages": "1051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1052", "messages": "1053", "suppressedMessages": "1054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1055", "messages": "1056", "suppressedMessages": "1057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1058", "messages": "1059", "suppressedMessages": "1060", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1061", "messages": "1062", "suppressedMessages": "1063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1064", "messages": "1065", "suppressedMessages": "1066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1067", "messages": "1068", "suppressedMessages": "1069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1070", "messages": "1071", "suppressedMessages": "1072", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1073", "messages": "1074", "suppressedMessages": "1075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1076", "messages": "1077", "suppressedMessages": "1078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1079", "messages": "1080", "suppressedMessages": "1081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1082", "messages": "1083", "suppressedMessages": "1084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1085", "messages": "1086", "suppressedMessages": "1087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1088", "messages": "1089", "suppressedMessages": "1090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1091", "messages": "1092", "suppressedMessages": "1093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1094", "messages": "1095", "suppressedMessages": "1096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1097", "messages": "1098", "suppressedMessages": "1099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1100", "messages": "1101", "suppressedMessages": "1102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1103", "messages": "1104", "suppressedMessages": "1105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1106", "messages": "1107", "suppressedMessages": "1108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1109", "messages": "1110", "suppressedMessages": "1111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1112", "messages": "1113", "suppressedMessages": "1114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1115", "messages": "1116", "suppressedMessages": "1117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1118", "messages": "1119", "suppressedMessages": "1120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1121", "messages": "1122", "suppressedMessages": "1123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1124", "messages": "1125", "suppressedMessages": "1126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1127", "messages": "1128", "suppressedMessages": "1129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1130", "messages": "1131", "suppressedMessages": "1132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1133", "messages": "1134", "suppressedMessages": "1135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1136", "messages": "1137", "suppressedMessages": "1138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1139", "messages": "1140", "suppressedMessages": "1141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1142", "messages": "1143", "suppressedMessages": "1144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1145", "messages": "1146", "suppressedMessages": "1147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1148", "messages": "1149", "suppressedMessages": "1150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1151", "messages": "1152", "suppressedMessages": "1153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Dentlyzer\\dentlyzer-frontend\\src\\index.js", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\App.js", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\reportWebVitals.js", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Home.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Loader.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Footer.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\i18n.js", ["1154"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Navbar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicServices.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Contact.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Login.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\About.jsx", ["1155", "1156", "1157"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\TryAI.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\DentistInfo.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Universities.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Clinics.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicConfirmation.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Payment.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Select.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicBook.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\router.js", ["1158", "1159", "1160", "1161", "1162", "1163", "1164", "1165", "1166"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\context\\AuthContext.js", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Dashboard.jsx", ["1167", "1168", "1169", "1170"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\XRay.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Gallery.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Analytics.jsx", ["1171"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Appointments.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\ToothChart.jsx", ["1172", "1173"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Reviews.jsx", ["1174"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Patients.jsx", ["1175", "1176", "1177"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx", ["1178"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Calendar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Calendar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Dashboard.jsx", ["1179"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Analytics.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Patients.jsx", ["1180"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Gallery.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\XRay.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\PatientProfile.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Appointments.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\ToothChart.jsx", ["1181", "1182", "1183"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx", ["1184"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Navbar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\PatientNav.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Sidebar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Navbar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Sidebar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\PatientNav.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx", ["1185"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Profile.jsx", ["1186", "1187", "1188"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Analytics.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Appointments.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\News.jsx", ["1189"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Reviews.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\people.jsx", ["1190", "1191", "1192", "1193"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\People.jsx", ["1194"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\index.js", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\reportWebVitals.js", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\App.js", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\router.js", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\i18n.js", ["1195", "1196", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\context\\AuthContext.js", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Profile.jsx", ["1211", "1212", "1213"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Login.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicServices.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Home.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\DentistInfo.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Contact.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\TryAI.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\About.jsx", ["1214", "1215"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Select.jsx", ["1216", "1217"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Clinics.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicConfirmation.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Universities.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicBook.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Payment.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Dashboard.jsx", ["1218", "1219", "1220", "1221"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Patients.jsx", ["1222", "1223", "1224", "1225"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Gallery.jsx", ["1226"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Calendar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Appointments.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Reviews.jsx", ["1227", "1228", "1229", "1230", "1231"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Analytics.jsx", ["1232"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\XRay.jsx", ["1233"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx", ["1234", "1235", "1236"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\ToothChart.jsx", ["1237", "1238"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx", ["1239", "1240", "1241", "1242"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\People.jsx", ["1243"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx", ["1244", "1245", "1246", "1247", "1248"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Reviews.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Appointments.jsx", ["1249"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Analytics.jsx", ["1250", "1251", "1252", "1253"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\News.jsx", ["1254"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Dashboard.jsx", ["1255"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\XRay.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Patients.jsx", ["1256"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Appointments.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\PatientProfile.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Analytics.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\ToothChart.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Calendar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Gallery.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Sidebar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Loader.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Navbar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Footer.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Navbar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PatientNav.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\PatientNav.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Sidebar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Navbar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ForgotPassword.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\SuperAdminSidebar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Analytics.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Universities.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\News.jsx", ["1257"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Accounts.jsx", ["1258"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Clinics.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ConfirmModal.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\UniversityDetailsModal.jsx", ["1259"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Sheets.jsx", ["1260"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\FixedProsthodonticsSheet.jsx", ["1261"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\History.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\EndodonticSheet.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\OperativeSheet.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\RemovableProsthodonticsSheet.jsx", ["1262"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ClinicDetailsModal.jsx", ["1263"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\SuccessModal.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Consent.jsx", ["1264", "1265", "1266", "1267"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PeriodonticsSheet.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\ReviewStepsDisplay.jsx", ["1268"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\SignatureManager.jsx", ["1269", "1270", "1271"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Support.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Appointments.jsx", ["1272", "1273"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Dashboard.jsx", ["1274", "1275", "1276", "1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287", "1288", "1289", "1290", "1291"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Analytics.jsx", ["1292", "1293", "1294", "1295", "1296", "1297", "1298"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AssistantSidebar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AssignStudentModal.jsx", ["1299"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AppointmentDetailsModal.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\MedicalTab.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\index.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\reportWebVitals.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\App.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\router.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\context\\AuthContext.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\i18n.js", ["1300", "1301", "1302", "1303", "1304", "1305", "1306", "1307", "1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Support.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Profile.jsx", ["1316", "1317", "1318"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ForgotPassword.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Home.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Contact.jsx", ["1319"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Login.jsx", ["1320"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\About.jsx", ["1321", "1322"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Universities.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\TryAI.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Dashboard.jsx", ["1323", "1324", "1325", "1326", "1327", "1328"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Gallery.jsx", ["1329"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Patients.jsx", ["1330", "1331", "1332", "1333", "1334", "1335"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Calendar.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Analytics.jsx", ["1336", "1337"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Sheets.jsx", ["1338"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Reviews.jsx", ["1339", "1340", "1341", "1342", "1343"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx", ["1344", "1345", "1346"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ToothChart.jsx", ["1347", "1348"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Appointments.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Consent.jsx", ["1349", "1350", "1351", "1352"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\History.jsx", ["1353"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx", ["1354", "1355", "1356", "1357", "1358"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\People.jsx", ["1359"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\News.jsx", ["1360"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Appointments.jsx", ["1361"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Reviews.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Analytics.jsx", ["1362", "1363", "1364", "1365"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx", ["1366", "1367", "1368", "1369"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Universities.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx", ["1370"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Clinics.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\News.jsx", ["1371"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Accounts.jsx", ["1372"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Analytics.jsx", ["1373", "1374", "1375"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Dashboard.jsx", ["1376", "1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384", "1385", "1386", "1387", "1388", "1389", "1390", "1391", "1392", "1393"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Appointments.jsx", ["1394", "1395"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Analytics.jsx", ["1396", "1397", "1398", "1399", "1400", "1401", "1402"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Sidebar.jsx", ["1403"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Navbar.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Loader.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Footer.jsx", ["1404"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Navbar.jsx", ["1405", "1406", "1407"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PatientNav.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\OperativeSheet.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\FixedProsthodonticsSheet.jsx", ["1408"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\EndodonticSheet.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PeriodonticsSheet.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\RemovableProsthodonticsSheet.jsx", ["1409"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\ReviewStepsDisplay.jsx", ["1410"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\SignatureManager.jsx", ["1411", "1412", "1413"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ConfirmModal.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\UniversityDetailsModal.jsx", ["1414"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\SuperAdminSidebar.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\SuccessModal.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ClinicDetailsModal.jsx", ["1415"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AssistantSidebar.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AssignStudentModal.jsx", ["1416"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AppointmentDetailsModal.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\MedicalTab.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\ProcedureRequests.jsx", ["1417", "1418"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ProcedureRequestsWidget.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\AppointmentsWidget.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\utils\\pdfUtils.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Activity.jsx", ["1419"], [], {"ruleId": "1420", "severity": 1, "message": "1421", "line": 354, "column": 9, "nodeType": "1422", "messageId": "1423", "endLine": 354, "endColumn": 15}, {"ruleId": "1424", "severity": 1, "message": "1425", "line": 8, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 8, "endColumn": 20}, {"ruleId": "1424", "severity": 1, "message": "1428", "line": 8, "column": 22, "nodeType": "1426", "messageId": "1427", "endLine": 8, "endColumn": 30}, {"ruleId": "1424", "severity": 1, "message": "1429", "line": 9, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 22}, {"ruleId": "1424", "severity": 1, "message": "1430", "line": 47, "column": 8, "nodeType": "1426", "messageId": "1427", "endLine": 47, "endColumn": 24}, {"ruleId": "1424", "severity": 1, "message": "1431", "line": 48, "column": 8, "nodeType": "1426", "messageId": "1427", "endLine": 48, "endColumn": 23}, {"ruleId": "1424", "severity": 1, "message": "1432", "line": 49, "column": 8, "nodeType": "1426", "messageId": "1427", "endLine": 49, "endColumn": 23}, {"ruleId": "1424", "severity": 1, "message": "1433", "line": 51, "column": 8, "nodeType": "1426", "messageId": "1427", "endLine": 51, "endColumn": 24}, {"ruleId": "1424", "severity": 1, "message": "1434", "line": 52, "column": 8, "nodeType": "1426", "messageId": "1427", "endLine": 52, "endColumn": 29}, {"ruleId": "1424", "severity": 1, "message": "1435", "line": 53, "column": 8, "nodeType": "1426", "messageId": "1427", "endLine": 53, "endColumn": 22}, {"ruleId": "1424", "severity": 1, "message": "1436", "line": 54, "column": 8, "nodeType": "1426", "messageId": "1427", "endLine": 54, "endColumn": 19}, {"ruleId": "1424", "severity": 1, "message": "1437", "line": 55, "column": 8, "nodeType": "1426", "messageId": "1427", "endLine": 55, "endColumn": 25}, {"ruleId": "1424", "severity": 1, "message": "1438", "line": 56, "column": 8, "nodeType": "1426", "messageId": "1427", "endLine": 56, "endColumn": 27}, {"ruleId": "1424", "severity": 1, "message": "1439", "line": 9, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 17}, {"ruleId": "1424", "severity": 1, "message": "1440", "line": 9, "column": 44, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 56}, {"ruleId": "1424", "severity": 1, "message": "1441", "line": 9, "column": 58, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 73}, {"ruleId": "1424", "severity": 1, "message": "1442", "line": 10, "column": 38, "nodeType": "1426", "messageId": "1427", "endLine": 10, "endColumn": 55}, {"ruleId": "1424", "severity": 1, "message": "1443", "line": 9, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 20}, {"ruleId": "1444", "severity": 1, "message": "1445", "line": 132, "column": 6, "nodeType": "1446", "endLine": 132, "endColumn": 18, "suggestions": "1447"}, {"ruleId": "1424", "severity": 1, "message": "1448", "line": 725, "column": 11, "nodeType": "1426", "messageId": "1427", "endLine": 725, "endColumn": 21}, {"ruleId": "1449", "severity": 1, "message": "1450", "line": 535, "column": 31, "nodeType": "1451", "endLine": 544, "endColumn": 33}, {"ruleId": "1424", "severity": 1, "message": "1452", "line": 2, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 2, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1453", "line": 9, "column": 86, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 93}, {"ruleId": "1424", "severity": 1, "message": "1429", "line": 10, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 10, "endColumn": 22}, {"ruleId": "1424", "severity": 1, "message": "1454", "line": 16, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 16, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1452", "line": 2, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 2, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1452", "line": 2, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 2, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1455", "line": 1, "column": 38, "nodeType": "1426", "messageId": "1427", "endLine": 1, "endColumn": 48}, {"ruleId": "1424", "severity": 1, "message": "1448", "line": 609, "column": 11, "nodeType": "1426", "messageId": "1427", "endLine": 609, "endColumn": 21}, {"ruleId": "1424", "severity": 1, "message": "1456", "line": 635, "column": 11, "nodeType": "1426", "messageId": "1427", "endLine": 635, "endColumn": 19}, {"ruleId": "1424", "severity": 1, "message": "1457", "line": 8, "column": 63, "nodeType": "1426", "messageId": "1427", "endLine": 8, "endColumn": 70}, {"ruleId": "1424", "severity": 1, "message": "1458", "line": 10, "column": 54, "nodeType": "1426", "messageId": "1427", "endLine": 10, "endColumn": 67}, {"ruleId": "1424", "severity": 1, "message": "1440", "line": 9, "column": 18, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 30}, {"ruleId": "1424", "severity": 1, "message": "1459", "line": 9, "column": 32, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 42}, {"ruleId": "1424", "severity": 1, "message": "1460", "line": 9, "column": 44, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 52}, {"ruleId": "1424", "severity": 1, "message": "1461", "line": 57, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 57, "endColumn": 13}, {"ruleId": "1462", "severity": 2, "message": "1463", "line": 22, "column": 85, "nodeType": "1464", "messageId": "1465", "endLine": 22, "endColumn": 94}, {"ruleId": "1462", "severity": 2, "message": "1466", "line": 24, "column": 91, "nodeType": "1464", "messageId": "1465", "endLine": 24, "endColumn": 102}, {"ruleId": "1462", "severity": 2, "message": "1467", "line": 25, "column": 94, "nodeType": "1464", "messageId": "1465", "endLine": 25, "endColumn": 102}, {"ruleId": "1444", "severity": 1, "message": "1468", "line": 67, "column": 6, "nodeType": "1446", "endLine": 67, "endColumn": 39, "suggestions": "1469"}, {"ruleId": "1444", "severity": 1, "message": "1468", "line": 81, "column": 6, "nodeType": "1446", "endLine": 81, "endColumn": 39, "suggestions": "1470"}, {"ruleId": "1420", "severity": 1, "message": "1471", "line": 338, "column": 11, "nodeType": "1422", "messageId": "1423", "endLine": 338, "endColumn": 23}, {"ruleId": "1420", "severity": 1, "message": "1472", "line": 339, "column": 11, "nodeType": "1422", "messageId": "1423", "endLine": 339, "endColumn": 16}, {"ruleId": "1420", "severity": 1, "message": "1473", "line": 340, "column": 11, "nodeType": "1422", "messageId": "1423", "endLine": 340, "endColumn": 19}, {"ruleId": "1420", "severity": 1, "message": "1474", "line": 341, "column": 11, "nodeType": "1422", "messageId": "1423", "endLine": 341, "endColumn": 27}, {"ruleId": "1420", "severity": 1, "message": "1475", "line": 342, "column": 11, "nodeType": "1422", "messageId": "1423", "endLine": 342, "endColumn": 23}, {"ruleId": "1420", "severity": 1, "message": "1476", "line": 343, "column": 11, "nodeType": "1422", "messageId": "1423", "endLine": 343, "endColumn": 20}, {"ruleId": "1420", "severity": 1, "message": "1477", "line": 370, "column": 7, "nodeType": "1422", "messageId": "1423", "endLine": 370, "endColumn": 23}, {"ruleId": "1420", "severity": 1, "message": "1478", "line": 388, "column": 7, "nodeType": "1422", "messageId": "1423", "endLine": 388, "endColumn": 21}, {"ruleId": "1420", "severity": 1, "message": "1479", "line": 397, "column": 7, "nodeType": "1422", "messageId": "1423", "endLine": 397, "endColumn": 16}, {"ruleId": "1420", "severity": 1, "message": "1480", "line": 409, "column": 5, "nodeType": "1422", "messageId": "1423", "endLine": 409, "endColumn": 18}, {"ruleId": "1420", "severity": 1, "message": "1471", "line": 707, "column": 15, "nodeType": "1422", "messageId": "1423", "endLine": 707, "endColumn": 27}, {"ruleId": "1420", "severity": 1, "message": "1472", "line": 708, "column": 15, "nodeType": "1422", "messageId": "1423", "endLine": 708, "endColumn": 20}, {"ruleId": "1420", "severity": 1, "message": "1473", "line": 709, "column": 15, "nodeType": "1422", "messageId": "1423", "endLine": 709, "endColumn": 23}, {"ruleId": "1420", "severity": 1, "message": "1474", "line": 710, "column": 15, "nodeType": "1422", "messageId": "1423", "endLine": 710, "endColumn": 31}, {"ruleId": "1420", "severity": 1, "message": "1475", "line": 711, "column": 15, "nodeType": "1422", "messageId": "1423", "endLine": 711, "endColumn": 27}, {"ruleId": "1420", "severity": 1, "message": "1476", "line": 712, "column": 15, "nodeType": "1422", "messageId": "1423", "endLine": 712, "endColumn": 24}, {"ruleId": "1424", "severity": 1, "message": "1440", "line": 9, "column": 18, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 30}, {"ruleId": "1424", "severity": 1, "message": "1459", "line": 9, "column": 32, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 42}, {"ruleId": "1424", "severity": 1, "message": "1460", "line": 9, "column": 44, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 52}, {"ruleId": "1424", "severity": 1, "message": "1428", "line": 8, "column": 22, "nodeType": "1426", "messageId": "1427", "endLine": 8, "endColumn": 30}, {"ruleId": "1424", "severity": 1, "message": "1429", "line": 9, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 22}, {"ruleId": "1424", "severity": 1, "message": "1481", "line": 1, "column": 17, "nodeType": "1426", "messageId": "1427", "endLine": 1, "endColumn": 25}, {"ruleId": "1424", "severity": 1, "message": "1482", "line": 4, "column": 41, "nodeType": "1426", "messageId": "1427", "endLine": 4, "endColumn": 53}, {"ruleId": "1424", "severity": 1, "message": "1439", "line": 9, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 17}, {"ruleId": "1424", "severity": 1, "message": "1440", "line": 9, "column": 44, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 56}, {"ruleId": "1424", "severity": 1, "message": "1441", "line": 9, "column": 58, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 73}, {"ruleId": "1424", "severity": 1, "message": "1442", "line": 10, "column": 38, "nodeType": "1426", "messageId": "1427", "endLine": 10, "endColumn": 55}, {"ruleId": "1424", "severity": 1, "message": "1452", "line": 2, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 2, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1453", "line": 9, "column": 86, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 93}, {"ruleId": "1424", "severity": 1, "message": "1483", "line": 9, "column": 95, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 103}, {"ruleId": "1424", "severity": 1, "message": "1429", "line": 10, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 10, "endColumn": 22}, {"ruleId": "1424", "severity": 1, "message": "1461", "line": 213, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 213, "endColumn": 13}, {"ruleId": "1424", "severity": 1, "message": "1484", "line": 9, "column": 96, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 111}, {"ruleId": "1424", "severity": 1, "message": "1485", "line": 25, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 25, "endColumn": 32}, {"ruleId": "1424", "severity": 1, "message": "1486", "line": 28, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 28, "endColumn": 25}, {"ruleId": "1424", "severity": 1, "message": "1487", "line": 202, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 202, "endColumn": 26}, {"ruleId": "1424", "severity": 1, "message": "1461", "line": 215, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 215, "endColumn": 13}, {"ruleId": "1424", "severity": 1, "message": "1452", "line": 2, "column": 23, "nodeType": "1426", "messageId": "1427", "endLine": 2, "endColumn": 27}, {"ruleId": "1424", "severity": 1, "message": "1488", "line": 9, "column": 43, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 53}, {"ruleId": "1424", "severity": 1, "message": "1489", "line": 10, "column": 19, "nodeType": "1426", "messageId": "1427", "endLine": 10, "endColumn": 25}, {"ruleId": "1444", "severity": 1, "message": "1490", "line": 53, "column": 6, "nodeType": "1446", "endLine": 53, "endColumn": 8, "suggestions": "1491"}, {"ruleId": "1444", "severity": 1, "message": "1490", "line": 78, "column": 6, "nodeType": "1446", "endLine": 78, "endColumn": 32, "suggestions": "1492"}, {"ruleId": "1444", "severity": 1, "message": "1445", "line": 132, "column": 6, "nodeType": "1446", "endLine": 132, "endColumn": 18, "suggestions": "1493"}, {"ruleId": "1424", "severity": 1, "message": "1448", "line": 726, "column": 11, "nodeType": "1426", "messageId": "1427", "endLine": 726, "endColumn": 21}, {"ruleId": "1424", "severity": 1, "message": "1494", "line": 11, "column": 33, "nodeType": "1426", "messageId": "1427", "endLine": 11, "endColumn": 47}, {"ruleId": "1424", "severity": 1, "message": "1495", "line": 12, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 12, "endColumn": 18}, {"ruleId": "1424", "severity": 1, "message": "1496", "line": 12, "column": 31, "nodeType": "1426", "messageId": "1427", "endLine": 12, "endColumn": 44}, {"ruleId": "1424", "severity": 1, "message": "1497", "line": 40, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 40, "endColumn": 20}, {"ruleId": "1444", "severity": 1, "message": "1468", "line": 126, "column": 6, "nodeType": "1446", "endLine": 126, "endColumn": 39, "suggestions": "1498"}, {"ruleId": "1424", "severity": 1, "message": "1494", "line": 12, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 12, "endColumn": 17}, {"ruleId": "1424", "severity": 1, "message": "1454", "line": 16, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 16, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1499", "line": 17, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 17, "endColumn": 15}, {"ruleId": "1424", "severity": 1, "message": "1500", "line": 18, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 18, "endColumn": 9}, {"ruleId": "1424", "severity": 1, "message": "1501", "line": 81, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 81, "endColumn": 14}, {"ruleId": "1444", "severity": 1, "message": "1502", "line": 87, "column": 6, "nodeType": "1446", "endLine": 87, "endColumn": 29, "suggestions": "1503"}, {"ruleId": "1424", "severity": 1, "message": "1504", "line": 14, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 14, "endColumn": 10}, {"ruleId": "1424", "severity": 1, "message": "1505", "line": 16, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 16, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1506", "line": 17, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 17, "endColumn": 11}, {"ruleId": "1424", "severity": 1, "message": "1507", "line": 18, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 18, "endColumn": 19}, {"ruleId": "1444", "severity": 1, "message": "1508", "line": 72, "column": 6, "nodeType": "1446", "endLine": 72, "endColumn": 29, "suggestions": "1509"}, {"ruleId": "1424", "severity": 1, "message": "1452", "line": 2, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 2, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1452", "line": 2, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 2, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1461", "line": 110, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 110, "endColumn": 13}, {"ruleId": "1424", "severity": 1, "message": "1510", "line": 5, "column": 37, "nodeType": "1426", "messageId": "1427", "endLine": 5, "endColumn": 43}, {"ruleId": "1424", "severity": 1, "message": "1511", "line": 2, "column": 103, "nodeType": "1426", "messageId": "1427", "endLine": 2, "endColumn": 110}, {"ruleId": "1424", "severity": 1, "message": "1512", "line": 25, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 25, "endColumn": 22}, {"ruleId": "1424", "severity": 1, "message": "1513", "line": 257, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 257, "endColumn": 21}, {"ruleId": "1424", "severity": 1, "message": "1514", "line": 91, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 91, "endColumn": 31}, {"ruleId": "1424", "severity": 1, "message": "1511", "line": 2, "column": 106, "nodeType": "1426", "messageId": "1427", "endLine": 2, "endColumn": 113}, {"ruleId": "1424", "severity": 1, "message": "1515", "line": 4, "column": 23, "nodeType": "1426", "messageId": "1427", "endLine": 4, "endColumn": 32}, {"ruleId": "1424", "severity": 1, "message": "1457", "line": 4, "column": 43, "nodeType": "1426", "messageId": "1427", "endLine": 4, "endColumn": 50}, {"ruleId": "1424", "severity": 1, "message": "1516", "line": 14, "column": 11, "nodeType": "1426", "messageId": "1427", "endLine": 14, "endColumn": 15}, {"ruleId": "1424", "severity": 1, "message": "1517", "line": 28, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 28, "endColumn": 23}, {"ruleId": "1424", "severity": 1, "message": "1518", "line": 3, "column": 25, "nodeType": "1426", "messageId": "1427", "endLine": 3, "endColumn": 38}, {"ruleId": "1424", "severity": 1, "message": "1517", "line": 31, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 31, "endColumn": 23}, {"ruleId": "1444", "severity": 1, "message": "1519", "line": 98, "column": 6, "nodeType": "1446", "endLine": 98, "endColumn": 24, "suggestions": "1520"}, {"ruleId": "1424", "severity": 1, "message": "1521", "line": 219, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 219, "endColumn": 28}, {"ruleId": "1424", "severity": 1, "message": "1522", "line": 26, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 26, "endColumn": 18}, {"ruleId": "1444", "severity": 1, "message": "1502", "line": 420, "column": 6, "nodeType": "1446", "endLine": 420, "endColumn": 29, "suggestions": "1523"}, {"ruleId": "1424", "severity": 1, "message": "1524", "line": 10, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 10, "endColumn": 12}, {"ruleId": "1424", "severity": 1, "message": "1525", "line": 12, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 12, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1488", "line": 14, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 14, "endColumn": 13}, {"ruleId": "1424", "severity": 1, "message": "1506", "line": 15, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 15, "endColumn": 11}, {"ruleId": "1424", "severity": 1, "message": "1505", "line": 16, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 16, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1507", "line": 17, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 17, "endColumn": 19}, {"ruleId": "1424", "severity": 1, "message": "1526", "line": 18, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 18, "endColumn": 24}, {"ruleId": "1424", "severity": 1, "message": "1527", "line": 19, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 19, "endColumn": 21}, {"ruleId": "1424", "severity": 1, "message": "1528", "line": 21, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 21, "endColumn": 13}, {"ruleId": "1424", "severity": 1, "message": "1529", "line": 21, "column": 15, "nodeType": "1426", "messageId": "1427", "endLine": 21, "endColumn": 18}, {"ruleId": "1424", "severity": 1, "message": "1530", "line": 21, "column": 20, "nodeType": "1426", "messageId": "1427", "endLine": 21, "endColumn": 24}, {"ruleId": "1424", "severity": 1, "message": "1531", "line": 66, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 66, "endColumn": 18}, {"ruleId": "1424", "severity": 1, "message": "1532", "line": 171, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 171, "endColumn": 36}, {"ruleId": "1424", "severity": 1, "message": "1533", "line": 185, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 185, "endColumn": 36}, {"ruleId": "1424", "severity": 1, "message": "1534", "line": 211, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 211, "endColumn": 35}, {"ruleId": "1424", "severity": 1, "message": "1535", "line": 232, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 232, "endColumn": 24}, {"ruleId": "1424", "severity": 1, "message": "1536", "line": 268, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 268, "endColumn": 25}, {"ruleId": "1424", "severity": 1, "message": "1537", "line": 323, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 323, "endColumn": 29}, {"ruleId": "1424", "severity": 1, "message": "1525", "line": 10, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 10, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1524", "line": 12, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 12, "endColumn": 12}, {"ruleId": "1424", "severity": 1, "message": "1538", "line": 73, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 73, "endColumn": 18}, {"ruleId": "1424", "severity": 1, "message": "1522", "line": 74, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 74, "endColumn": 18}, {"ruleId": "1424", "severity": 1, "message": "1531", "line": 82, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 82, "endColumn": 18}, {"ruleId": "1424", "severity": 1, "message": "1461", "line": 92, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 92, "endColumn": 13}, {"ruleId": "1444", "severity": 1, "message": "1502", "line": 153, "column": 6, "nodeType": "1446", "endLine": 153, "endColumn": 29, "suggestions": "1539"}, {"ruleId": "1444", "severity": 1, "message": "1540", "line": 29, "column": 6, "nodeType": "1446", "endLine": 29, "endColumn": 40, "suggestions": "1541"}, {"ruleId": "1420", "severity": 1, "message": "1471", "line": 336, "column": 11, "nodeType": "1422", "messageId": "1423", "endLine": 336, "endColumn": 23}, {"ruleId": "1420", "severity": 1, "message": "1472", "line": 337, "column": 11, "nodeType": "1422", "messageId": "1423", "endLine": 337, "endColumn": 16}, {"ruleId": "1420", "severity": 1, "message": "1473", "line": 338, "column": 11, "nodeType": "1422", "messageId": "1423", "endLine": 338, "endColumn": 19}, {"ruleId": "1420", "severity": 1, "message": "1474", "line": 339, "column": 11, "nodeType": "1422", "messageId": "1423", "endLine": 339, "endColumn": 27}, {"ruleId": "1420", "severity": 1, "message": "1475", "line": 340, "column": 11, "nodeType": "1422", "messageId": "1423", "endLine": 340, "endColumn": 23}, {"ruleId": "1420", "severity": 1, "message": "1476", "line": 341, "column": 11, "nodeType": "1422", "messageId": "1423", "endLine": 341, "endColumn": 20}, {"ruleId": "1420", "severity": 1, "message": "1477", "line": 368, "column": 7, "nodeType": "1422", "messageId": "1423", "endLine": 368, "endColumn": 23}, {"ruleId": "1420", "severity": 1, "message": "1478", "line": 386, "column": 7, "nodeType": "1422", "messageId": "1423", "endLine": 386, "endColumn": 21}, {"ruleId": "1420", "severity": 1, "message": "1479", "line": 395, "column": 7, "nodeType": "1422", "messageId": "1423", "endLine": 395, "endColumn": 16}, {"ruleId": "1420", "severity": 1, "message": "1480", "line": 407, "column": 5, "nodeType": "1422", "messageId": "1423", "endLine": 407, "endColumn": 18}, {"ruleId": "1420", "severity": 1, "message": "1471", "line": 702, "column": 15, "nodeType": "1422", "messageId": "1423", "endLine": 702, "endColumn": 27}, {"ruleId": "1420", "severity": 1, "message": "1472", "line": 703, "column": 15, "nodeType": "1422", "messageId": "1423", "endLine": 703, "endColumn": 20}, {"ruleId": "1420", "severity": 1, "message": "1473", "line": 704, "column": 15, "nodeType": "1422", "messageId": "1423", "endLine": 704, "endColumn": 23}, {"ruleId": "1420", "severity": 1, "message": "1474", "line": 705, "column": 15, "nodeType": "1422", "messageId": "1423", "endLine": 705, "endColumn": 31}, {"ruleId": "1420", "severity": 1, "message": "1475", "line": 706, "column": 15, "nodeType": "1422", "messageId": "1423", "endLine": 706, "endColumn": 27}, {"ruleId": "1420", "severity": 1, "message": "1476", "line": 707, "column": 15, "nodeType": "1422", "messageId": "1423", "endLine": 707, "endColumn": 24}, {"ruleId": "1424", "severity": 1, "message": "1440", "line": 9, "column": 18, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 30}, {"ruleId": "1424", "severity": 1, "message": "1459", "line": 9, "column": 32, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 42}, {"ruleId": "1424", "severity": 1, "message": "1460", "line": 9, "column": 44, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 52}, {"ruleId": "1424", "severity": 1, "message": "1439", "line": 9, "column": 47, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 54}, {"ruleId": "1424", "severity": 1, "message": "1439", "line": 6, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 6, "endColumn": 17}, {"ruleId": "1424", "severity": 1, "message": "1428", "line": 8, "column": 22, "nodeType": "1426", "messageId": "1427", "endLine": 8, "endColumn": 30}, {"ruleId": "1424", "severity": 1, "message": "1429", "line": 9, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 22}, {"ruleId": "1424", "severity": 1, "message": "1506", "line": 12, "column": 34, "nodeType": "1426", "messageId": "1427", "endLine": 12, "endColumn": 42}, {"ruleId": "1424", "severity": 1, "message": "1440", "line": 12, "column": 44, "nodeType": "1426", "messageId": "1427", "endLine": 12, "endColumn": 56}, {"ruleId": "1424", "severity": 1, "message": "1441", "line": 12, "column": 58, "nodeType": "1426", "messageId": "1427", "endLine": 12, "endColumn": 73}, {"ruleId": "1424", "severity": 1, "message": "1496", "line": 12, "column": 75, "nodeType": "1426", "messageId": "1427", "endLine": 12, "endColumn": 88}, {"ruleId": "1424", "severity": 1, "message": "1542", "line": 35, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 35, "endColumn": 24}, {"ruleId": "1424", "severity": 1, "message": "1543", "line": 136, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 136, "endColumn": 28}, {"ruleId": "1424", "severity": 1, "message": "1461", "line": 213, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 213, "endColumn": 13}, {"ruleId": "1424", "severity": 1, "message": "1452", "line": 2, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 2, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1544", "line": 9, "column": 18, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 28}, {"ruleId": "1424", "severity": 1, "message": "1453", "line": 9, "column": 86, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 93}, {"ruleId": "1424", "severity": 1, "message": "1483", "line": 9, "column": 95, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 103}, {"ruleId": "1424", "severity": 1, "message": "1429", "line": 10, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 10, "endColumn": 22}, {"ruleId": "1424", "severity": 1, "message": "1545", "line": 197, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 197, "endColumn": 25}, {"ruleId": "1424", "severity": 1, "message": "1452", "line": 2, "column": 23, "nodeType": "1426", "messageId": "1427", "endLine": 2, "endColumn": 27}, {"ruleId": "1424", "severity": 1, "message": "1546", "line": 345, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 345, "endColumn": 29}, {"ruleId": "1424", "severity": 1, "message": "1512", "line": 25, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 25, "endColumn": 22}, {"ruleId": "1424", "severity": 1, "message": "1484", "line": 9, "column": 96, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 111}, {"ruleId": "1424", "severity": 1, "message": "1485", "line": 25, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 25, "endColumn": 32}, {"ruleId": "1424", "severity": 1, "message": "1486", "line": 28, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 28, "endColumn": 25}, {"ruleId": "1424", "severity": 1, "message": "1487", "line": 202, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 202, "endColumn": 26}, {"ruleId": "1424", "severity": 1, "message": "1461", "line": 215, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 215, "endColumn": 13}, {"ruleId": "1424", "severity": 1, "message": "1489", "line": 10, "column": 19, "nodeType": "1426", "messageId": "1427", "endLine": 10, "endColumn": 25}, {"ruleId": "1444", "severity": 1, "message": "1490", "line": 53, "column": 6, "nodeType": "1446", "endLine": 53, "endColumn": 8, "suggestions": "1547"}, {"ruleId": "1444", "severity": 1, "message": "1490", "line": 78, "column": 6, "nodeType": "1446", "endLine": 78, "endColumn": 32, "suggestions": "1548"}, {"ruleId": "1444", "severity": 1, "message": "1445", "line": 132, "column": 6, "nodeType": "1446", "endLine": 132, "endColumn": 18, "suggestions": "1549"}, {"ruleId": "1424", "severity": 1, "message": "1448", "line": 726, "column": 11, "nodeType": "1426", "messageId": "1427", "endLine": 726, "endColumn": 21}, {"ruleId": "1424", "severity": 1, "message": "1515", "line": 4, "column": 23, "nodeType": "1426", "messageId": "1427", "endLine": 4, "endColumn": 32}, {"ruleId": "1424", "severity": 1, "message": "1457", "line": 4, "column": 43, "nodeType": "1426", "messageId": "1427", "endLine": 4, "endColumn": 50}, {"ruleId": "1424", "severity": 1, "message": "1516", "line": 14, "column": 11, "nodeType": "1426", "messageId": "1427", "endLine": 14, "endColumn": 15}, {"ruleId": "1424", "severity": 1, "message": "1517", "line": 30, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 30, "endColumn": 23}, {"ruleId": "1424", "severity": 1, "message": "1550", "line": 9, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 9, "endColumn": 27}, {"ruleId": "1424", "severity": 1, "message": "1494", "line": 12, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 12, "endColumn": 17}, {"ruleId": "1424", "severity": 1, "message": "1454", "line": 16, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 16, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1499", "line": 17, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 17, "endColumn": 15}, {"ruleId": "1424", "severity": 1, "message": "1500", "line": 18, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 18, "endColumn": 9}, {"ruleId": "1424", "severity": 1, "message": "1501", "line": 81, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 81, "endColumn": 14}, {"ruleId": "1444", "severity": 1, "message": "1468", "line": 126, "column": 6, "nodeType": "1446", "endLine": 126, "endColumn": 39, "suggestions": "1551"}, {"ruleId": "1444", "severity": 1, "message": "1508", "line": 72, "column": 6, "nodeType": "1446", "endLine": 72, "endColumn": 29, "suggestions": "1552"}, {"ruleId": "1444", "severity": 1, "message": "1502", "line": 87, "column": 6, "nodeType": "1446", "endLine": 87, "endColumn": 29, "suggestions": "1553"}, {"ruleId": "1424", "severity": 1, "message": "1504", "line": 14, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 14, "endColumn": 10}, {"ruleId": "1424", "severity": 1, "message": "1505", "line": 16, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 16, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1506", "line": 17, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 17, "endColumn": 11}, {"ruleId": "1424", "severity": 1, "message": "1507", "line": 18, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 18, "endColumn": 19}, {"ruleId": "1424", "severity": 1, "message": "1494", "line": 11, "column": 33, "nodeType": "1426", "messageId": "1427", "endLine": 11, "endColumn": 47}, {"ruleId": "1424", "severity": 1, "message": "1495", "line": 12, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 12, "endColumn": 18}, {"ruleId": "1424", "severity": 1, "message": "1496", "line": 12, "column": 31, "nodeType": "1426", "messageId": "1427", "endLine": 12, "endColumn": 44}, {"ruleId": "1424", "severity": 1, "message": "1497", "line": 40, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 40, "endColumn": 20}, {"ruleId": "1424", "severity": 1, "message": "1441", "line": 5, "column": 24, "nodeType": "1426", "messageId": "1427", "endLine": 5, "endColumn": 39}, {"ruleId": "1424", "severity": 1, "message": "1461", "line": 107, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 107, "endColumn": 13}, {"ruleId": "1424", "severity": 1, "message": "1510", "line": 5, "column": 37, "nodeType": "1426", "messageId": "1427", "endLine": 5, "endColumn": 43}, {"ruleId": "1424", "severity": 1, "message": "1554", "line": 11, "column": 96, "nodeType": "1426", "messageId": "1427", "endLine": 11, "endColumn": 102}, {"ruleId": "1424", "severity": 1, "message": "1504", "line": 11, "column": 117, "nodeType": "1426", "messageId": "1427", "endLine": 11, "endColumn": 124}, {"ruleId": "1424", "severity": 1, "message": "1555", "line": 11, "column": 126, "nodeType": "1426", "messageId": "1427", "endLine": 11, "endColumn": 141}, {"ruleId": "1424", "severity": 1, "message": "1524", "line": 10, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 10, "endColumn": 12}, {"ruleId": "1424", "severity": 1, "message": "1525", "line": 12, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 12, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1488", "line": 14, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 14, "endColumn": 13}, {"ruleId": "1424", "severity": 1, "message": "1506", "line": 15, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 15, "endColumn": 11}, {"ruleId": "1424", "severity": 1, "message": "1505", "line": 16, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 16, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1507", "line": 17, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 17, "endColumn": 19}, {"ruleId": "1424", "severity": 1, "message": "1526", "line": 18, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 18, "endColumn": 24}, {"ruleId": "1424", "severity": 1, "message": "1527", "line": 19, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 19, "endColumn": 21}, {"ruleId": "1424", "severity": 1, "message": "1528", "line": 21, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 21, "endColumn": 13}, {"ruleId": "1424", "severity": 1, "message": "1529", "line": 21, "column": 15, "nodeType": "1426", "messageId": "1427", "endLine": 21, "endColumn": 18}, {"ruleId": "1424", "severity": 1, "message": "1530", "line": 21, "column": 20, "nodeType": "1426", "messageId": "1427", "endLine": 21, "endColumn": 24}, {"ruleId": "1424", "severity": 1, "message": "1531", "line": 66, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 66, "endColumn": 18}, {"ruleId": "1424", "severity": 1, "message": "1532", "line": 171, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 171, "endColumn": 36}, {"ruleId": "1424", "severity": 1, "message": "1533", "line": 185, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 185, "endColumn": 36}, {"ruleId": "1424", "severity": 1, "message": "1534", "line": 211, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 211, "endColumn": 35}, {"ruleId": "1424", "severity": 1, "message": "1535", "line": 232, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 232, "endColumn": 24}, {"ruleId": "1424", "severity": 1, "message": "1536", "line": 268, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 268, "endColumn": 25}, {"ruleId": "1424", "severity": 1, "message": "1537", "line": 323, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 323, "endColumn": 29}, {"ruleId": "1424", "severity": 1, "message": "1522", "line": 26, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 26, "endColumn": 18}, {"ruleId": "1444", "severity": 1, "message": "1502", "line": 420, "column": 6, "nodeType": "1446", "endLine": 420, "endColumn": 29, "suggestions": "1556"}, {"ruleId": "1424", "severity": 1, "message": "1525", "line": 10, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 10, "endColumn": 14}, {"ruleId": "1424", "severity": 1, "message": "1524", "line": 12, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 12, "endColumn": 12}, {"ruleId": "1424", "severity": 1, "message": "1538", "line": 73, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 73, "endColumn": 18}, {"ruleId": "1424", "severity": 1, "message": "1522", "line": 74, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 74, "endColumn": 18}, {"ruleId": "1424", "severity": 1, "message": "1531", "line": 82, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 82, "endColumn": 18}, {"ruleId": "1424", "severity": 1, "message": "1461", "line": 92, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 92, "endColumn": 13}, {"ruleId": "1444", "severity": 1, "message": "1502", "line": 153, "column": 6, "nodeType": "1446", "endLine": 153, "endColumn": 29, "suggestions": "1557"}, {"ruleId": "1424", "severity": 1, "message": "1439", "line": 8, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 8, "endColumn": 10}, {"ruleId": "1424", "severity": 1, "message": "1439", "line": 5, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 5, "endColumn": 17}, {"ruleId": "1424", "severity": 1, "message": "1439", "line": 6, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 6, "endColumn": 17}, {"ruleId": "1424", "severity": 1, "message": "1558", "line": 69, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 69, "endColumn": 31}, {"ruleId": "1424", "severity": 1, "message": "1559", "line": 110, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 110, "endColumn": 17}, {"ruleId": "1424", "severity": 1, "message": "1513", "line": 292, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 292, "endColumn": 21}, {"ruleId": "1424", "severity": 1, "message": "1514", "line": 92, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 92, "endColumn": 31}, {"ruleId": "1424", "severity": 1, "message": "1518", "line": 3, "column": 25, "nodeType": "1426", "messageId": "1427", "endLine": 3, "endColumn": 38}, {"ruleId": "1424", "severity": 1, "message": "1517", "line": 31, "column": 10, "nodeType": "1426", "messageId": "1427", "endLine": 31, "endColumn": 23}, {"ruleId": "1444", "severity": 1, "message": "1519", "line": 98, "column": 6, "nodeType": "1446", "endLine": 98, "endColumn": 24, "suggestions": "1560"}, {"ruleId": "1424", "severity": 1, "message": "1521", "line": 219, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 219, "endColumn": 28}, {"ruleId": "1424", "severity": 1, "message": "1511", "line": 2, "column": 103, "nodeType": "1426", "messageId": "1427", "endLine": 2, "endColumn": 110}, {"ruleId": "1424", "severity": 1, "message": "1511", "line": 2, "column": 106, "nodeType": "1426", "messageId": "1427", "endLine": 2, "endColumn": 113}, {"ruleId": "1444", "severity": 1, "message": "1540", "line": 29, "column": 6, "nodeType": "1446", "endLine": 29, "endColumn": 40, "suggestions": "1561"}, {"ruleId": "1424", "severity": 1, "message": "1562", "line": 13, "column": 3, "nodeType": "1426", "messageId": "1427", "endLine": 13, "endColumn": 11}, {"ruleId": "1424", "severity": 1, "message": "1563", "line": 32, "column": 9, "nodeType": "1426", "messageId": "1427", "endLine": 32, "endColumn": 17}, {"ruleId": "1444", "severity": 1, "message": "1564", "line": 33, "column": 6, "nodeType": "1446", "endLine": 33, "endColumn": 32, "suggestions": "1565"}, "no-dupe-keys", "Duplicate key 'select'.", "ObjectExpression", "unexpected", "no-unused-vars", "'FaLinkedin' is defined but never used.", "Identifier", "unusedVar", "'FaGithub' is defined but never used.", "'RiAiGenerate' is defined but never used.", "'DentistDashboard' is defined but never used.", "'DentistCalendar' is defined but never used.", "'DentistPatients' is defined but never used.", "'DentistAnalytics' is defined but never used.", "'DentistPatientProfile' is defined but never used.", "'DentistGallery' is defined but never used.", "'DentistXRay' is defined but never used.", "'Dentist<PERSON><PERSON><PERSON><PERSON>hart' is defined but never used.", "'DentistAppointments' is defined but never used.", "'FaTooth' is defined but never used.", "'FaUniversity' is defined but never used.", "'FaClinicMedical' is defined but never used.", "'MdHealthAndSafety' is defined but never used.", "'FaChartPie' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'upperTeethNumbers'. Either include it or remove the dependency array.", "ArrayExpression", ["1566"], "'toothColor' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'Link' is defined but never used.", "'FaPills' is defined but never used.", "'FaNewspaper' is defined but never used.", "'useContext' is defined but never used.", "'surfaces' is assigned a value but never used.", "'FaTimes' is defined but never used.", "'FaFileMedical' is defined but never used.", "'FaEnvelope' is defined but never used.", "'FaIdCard' is defined but never used.", "'item' is assigned a value but never used.", "react/jsx-no-undef", "'FaUserAlt' is not defined.", "JSXIdentifier", "undefined", "'FaUserNurse' is not defined.", "'FaUserMd' is not defined.", "React Hook useEffect has a missing dependency: 'categories'. Either include it or remove the dependency array.", ["1567"], ["1568"], "Duplicate key 'NationalID'.", "Duplicate key 'Age'.", "Duplicate key 'Gender'.", "Duplicate key '<PERSON><PERSON><PERSON><PERSON><PERSON>'.", "Duplicate key 'Occupation'.", "Duplicate key 'Address'.", "Duplicate key 'universityInfo'.", "Duplicate key 'universities'.", "Duplicate key 'clinics'.", "Duplicate key 'dentistInfo'.", "'useState' is defined but never used.", "'FaInfoCircle' is defined but never used.", "'FaUpload' is defined but never used.", "'FaHourglassHalf' is defined but never used.", "'procedureTypeAnalytics' is assigned a value but never used.", "'showReviewModal' is assigned a value but never used.", "'handleReviewClick' is assigned a value but never used.", "'FaDownload' is defined but never used.", "'FaUser' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReviewSteps'. Either include it or remove the dependency array.", ["1569"], ["1570"], ["1571"], "'FaUserGraduate' is defined but never used.", "'FaCalendarCheck' is defined but never used.", "'FaCheckCircle' is defined but never used.", "'allReviews' is assigned a value but never used.", ["1572"], "'FaArrowRight' is defined but never used.", "'FaBell' is defined but never used.", "'news' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["1573"], "'FaUsers' is defined but never used.", "'FaUserNurse' is defined but never used.", "'FaUserMd' is defined but never used.", "'FaClipboardCheck' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchNews'. Either include it or remove the dependency array.", ["1574"], "'FaEdit' is defined but never used.", "'FaImage' is defined but never used.", "'savedSheetId' is assigned a value but never used.", "'renderSelect' is assigned a value but never used.", "'handleDeepNestedChange' is assigned a value but never used.", "'FaFileAlt' is defined but never used.", "'user' is assigned a value but never used.", "'canvasContext' is assigned a value but never used.", "'FaTimesCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSavedSignature'. Either include it or remove the dependency array.", ["1575"], "'toggleSignatureType' is assigned a value but never used.", "'students' is assigned a value but never used.", ["1576"], "'FaUserAlt' is defined but never used.", "'FaChartLine' is defined but never used.", "'FaExclamationTriangle' is defined but never used.", "'FaRegCalendarCheck' is defined but never used.", "'Bar' is defined but never used.", "'Pie' is defined but never used.", "'Line' is defined but never used.", "'container' is assigned a value but never used.", "'appointmentsStatusChartData' is assigned a value but never used.", "'appointmentsByTypeChartData' is assigned a value but never used.", "'appointmentsByDayChartData' is assigned a value but never used.", "'pieChartOptions' is assigned a value but never used.", "'lineChartOptions' is assigned a value but never used.", "'downloadAppointments' is assigned a value but never used.", "'patients' is assigned a value but never used.", ["1577"], "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", ["1578"], "'chronicDiseases' is assigned a value but never used.", "'getStatusBadgeClass' is assigned a value but never used.", "'FaUserPlus' is defined but never used.", "'handleAddPatient' is assigned a value but never used.", "'appointmentTypesData' is assigned a value but never used.", ["1579"], ["1580"], ["1581"], "'printElementAsPDF' is defined but never used.", ["1582"], ["1583"], ["1584"], "'FaCogs' is defined but never used.", "'FaGraduationCap' is defined but never used.", ["1585"], ["1586"], "'toggleServicesDropdown' is assigned a value but never used.", "'dropdown' is assigned a value but never used.", ["1587"], ["1588"], "'FaFilter' is defined but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchActivities'. Either include it or remove the dependency array.", ["1589"], {"desc": "1590", "fix": "1591"}, {"desc": "1592", "fix": "1593"}, {"desc": "1592", "fix": "1594"}, {"desc": "1595", "fix": "1596"}, {"desc": "1597", "fix": "1598"}, {"desc": "1590", "fix": "1599"}, {"desc": "1592", "fix": "1600"}, {"desc": "1601", "fix": "1602"}, {"desc": "1603", "fix": "1604"}, {"desc": "1605", "fix": "1606"}, {"desc": "1601", "fix": "1607"}, {"desc": "1601", "fix": "1608"}, {"desc": "1609", "fix": "1610"}, {"desc": "1595", "fix": "1611"}, {"desc": "1597", "fix": "1612"}, {"desc": "1590", "fix": "1613"}, {"desc": "1592", "fix": "1614"}, {"desc": "1603", "fix": "1615"}, {"desc": "1601", "fix": "1616"}, {"desc": "1601", "fix": "1617"}, {"desc": "1601", "fix": "1618"}, {"desc": "1605", "fix": "1619"}, {"desc": "1609", "fix": "1620"}, {"desc": "1621", "fix": "1622"}, "Update the dependencies array to be: [nationalId, upperTeethNumbers]", {"range": "1623", "text": "1624"}, "Update the dependencies array to be: [user, token, navigate, category, categories]", {"range": "1625", "text": "1626"}, {"range": "1627", "text": "1626"}, "Update the dependencies array to be: [fetchReviewSteps]", {"range": "1628", "text": "1629"}, "Update the dependencies array to be: [fetchReviewSteps, reviewData.procedureType]", {"range": "1630", "text": "1631"}, {"range": "1632", "text": "1624"}, {"range": "1633", "text": "1626"}, "Update the dependencies array to be: [user, token, navigate, fetchData]", {"range": "1634", "text": "1635"}, "Update the dependencies array to be: [user, token, navigate, fetchNews]", {"range": "1636", "text": "1637"}, "Update the dependencies array to be: [fetchSavedSignature, initialSignature]", {"range": "1638", "text": "1639"}, {"range": "1640", "text": "1635"}, {"range": "1641", "text": "1635"}, "Update the dependencies array to be: [isOpen, appointment, token, user, fetchStudents]", {"range": "1642", "text": "1643"}, {"range": "1644", "text": "1629"}, {"range": "1645", "text": "1631"}, {"range": "1646", "text": "1624"}, {"range": "1647", "text": "1626"}, {"range": "1648", "text": "1637"}, {"range": "1649", "text": "1635"}, {"range": "1650", "text": "1635"}, {"range": "1651", "text": "1635"}, {"range": "1652", "text": "1639"}, {"range": "1653", "text": "1643"}, "Update the dependencies array to be: [fetchActivities, filters, pagination.page]", {"range": "1654", "text": "1655"}, [4827, 4839], "[nationalId, upperTeethNumbers]", [2682, 2715], "[user, token, navigate, category, categories]", [3328, 3361], [1741, 1743], "[fetchReviewSteps]", [2511, 2537], "[fetchReviewSteps, reviewData.procedureType]", [4827, 4839], [4697, 4730], [2932, 2955], "[user, token, navigate, fetchData]", [2276, 2299], "[user, token, navigate, fetchNews]", [3227, 3245], "[fetchSavedSignature, initialSignature]", [17029, 17052], [4536, 4559], [941, 975], "[isOpen, appointment, token, user, fetchStudents]", [1741, 1743], [2511, 2537], [4827, 4839], [4697, 4730], [2276, 2299], [2932, 2955], [17029, 17052], [4536, 4559], [3227, 3245], [941, 975], [1008, 1034], "[fetchActivities, filters, pagination.page]"]