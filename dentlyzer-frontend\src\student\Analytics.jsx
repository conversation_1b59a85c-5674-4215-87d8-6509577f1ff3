import { useState, useEffect, useRef } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import Loader from '../components/Loader';
import { motion } from 'framer-motion';
import {
  FaUsers,
  FaTooth,
  FaCalendarAlt,
  FaClipboardCheck,
  FaCheckCircle,
  FaHourglassHalf,
  FaStarHalfAlt,
  FaStar,
  FaUserInjured,
  FaChartBar,
} from 'react-icons/fa';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  PointElement,
  LineElement,
  RadialLinearScale,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Bar, Pie, Doughnut, Line, PolarArea, Radar } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  PointElement,
  LineElement,
  RadialLinearScale,
  Title,
  Tooltip,
  Legend,
  Filler
);

const Analytics = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [timeRange, setTimeRange] = useState('all'); // 'week', 'month', 'all'
  const [activeTab, setActiveTab] = useState('overview'); // 'overview', 'patients', 'procedures', 'reviews'
  const [chartType, setChartType] = useState({
    appointments: 'doughnut',
    appointmentsPerMonth: 'line',
    genderOverview: 'doughnut',
    age: 'bar',
    gender: 'pie',
    procedures: 'bar',
    treatmentSheetTrend: 'line',
    diseases: 'doughnut',
    reviewStatus: 'doughnut',
    reviewProcedures: 'bar',
    reviewTrend: 'line',
    reviewQuality: 'radar'
  });

  // Chart references
  const statusChartRef = useRef(null);
  const procedureChartRef = useRef(null);
  const reviewTrendChartRef = useRef(null);
  const qualityMetricsChartRef = useRef(null);

  const [analyticsData, setAnalyticsData] = useState({
    appointmentStats: { pending: 0, completed: 0, cancelled: 0 },
    appointmentTypes: {},
    ageDistribution: {},
    genderDistribution: {},
    procedureFrequency: {},
    chronicDiseasePrevalence: {},
    reviewStats: {
      statusDistribution: { accepted: 0, pending: 0, denied: 0 },
      procedureTypeDistribution: {},
      qualityMetrics: {
        avgProcedureQuality: 0,
        avgPatientInteraction: 0
      },
      reviewsByMonth: [],
      acceptanceRate: 0,
      denialRate: 0,
      totalReviews: 0
    }
  });
  const navigate = useNavigate();
  const { user, token } = useAuth();

  useEffect(() => {
    const fetchAnalytics = async () => {
      if (!user || !token) {
        setError('Please log in to view analytics.');
        setLoading(false);
        return;
      }

      try {
        const config = {
          headers: { Authorization: `Bearer ${token}` },
          params: { range: timeRange }
        };

        // Fetch general analytics data
        const analyticsResponse = await axios.get('http://localhost:5000/api/analytics', config);

        // Fetch reviews data separately
        let reviewsData = {
          statusDistribution: { accepted: 0, pending: 0, denied: 0 },
          procedureTypeDistribution: {},
          qualityMetrics: {
            avgProcedureQuality: 0,
            avgPatientInteraction: 0,
            avgDocumentation: 0,
            avgTechnique: 0,
            avgTimeManagement: 0,
            avgCommunication: 0
          },
          reviewsByMonth: [],
          acceptanceRate: 0,
          denialRate: 0,
          totalReviews: 0
        };

        if (user.studentId) {
          try {
            const reviewsResponse = await axios.get(`http://localhost:5000/api/reviews/student?studentId=${user.studentId}`, config);
            const reviews = reviewsResponse.data;

            if (Array.isArray(reviews) && reviews.length > 0) {
              // Calculate review statistics
              const totalReviews = reviews.length;
              const pendingReviews = reviews.filter(r => r.status === 'pending');
              const doneReviews = reviews.filter(r => r.status !== 'pending');
              const acceptedReviews = doneReviews.filter(r => r.status === 'accepted');
              const deniedReviews = doneReviews.filter(r => r.status === 'denied');

              const acceptanceRate = totalReviews > 0 ? ((acceptedReviews.length / totalReviews) * 100).toFixed(1) : 0;
              const denialRate = totalReviews > 0 ? ((deniedReviews.length / totalReviews) * 100).toFixed(1) : 0;

              // Calculate average ratings
              const avgProcedureQuality = doneReviews.length > 0
                ? (doneReviews.reduce((sum, r) => sum + (r.procedureQuality || 0), 0) / doneReviews.length).toFixed(1)
                : 0;
              const avgPatientInteraction = doneReviews.length > 0
                ? (doneReviews.reduce((sum, r) => sum + (r.patientInteraction || 0), 0) / doneReviews.length).toFixed(1)
                : 0;

              // Group reviews by procedure type
              const procedureTypes = {};
              reviews.forEach(review => {
                const type = review.procedureType || 'Unknown';
                if (!procedureTypes[type]) {
                  procedureTypes[type] = {
                    total: 0,
                    accepted: 0,
                    denied: 0,
                    pending: 0
                  };
                }
                procedureTypes[type].total++;
                if (review.status === 'accepted') procedureTypes[type].accepted++;
                else if (review.status === 'denied') procedureTypes[type].denied++;
                else procedureTypes[type].pending++;
              });

              // Group reviews by month
              const reviewsByMonth = [];
              const monthsMap = {};

              reviews.forEach(review => {
                const date = new Date(review.submittedDate);
                const monthYear = `${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}`;

                if (!monthsMap[monthYear]) {
                  monthsMap[monthYear] = {
                    month: date.toLocaleString('default', { month: 'short' }),
                    year: date.getFullYear(),
                    total: 0,
                    accepted: 0,
                    pending: 0,
                    denied: 0
                  };
                }

                monthsMap[monthYear].total++;
                if (review.status === 'accepted') monthsMap[monthYear].accepted++;
                else if (review.status === 'pending') monthsMap[monthYear].pending++;
                else monthsMap[monthYear].denied++;
              });

              // Convert to array and sort by date
              Object.values(monthsMap).forEach(month => {
                reviewsByMonth.push(month);
              });

              reviewsByMonth.sort((a, b) => {
                if (a.year !== b.year) return a.year - b.year;
                const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                return months.indexOf(a.month) - months.indexOf(b.month);
              });

              // Update reviewsData
              reviewsData = {
                statusDistribution: {
                  accepted: acceptedReviews.length,
                  pending: pendingReviews.length,
                  denied: deniedReviews.length
                },
                procedureTypeDistribution: procedureTypes,
                qualityMetrics: {
                  avgProcedureQuality: parseFloat(avgProcedureQuality),
                  avgPatientInteraction: parseFloat(avgPatientInteraction),
                  avgDocumentation: parseFloat(avgProcedureQuality), // Using procedure quality as a placeholder
                  avgTechnique: parseFloat(avgProcedureQuality), // Using procedure quality as a placeholder
                  avgTimeManagement: parseFloat(avgPatientInteraction), // Using patient interaction as a placeholder
                  avgCommunication: parseFloat(avgPatientInteraction) // Using patient interaction as a placeholder
                },
                reviewsByMonth: reviewsByMonth,
                acceptanceRate: parseFloat(acceptanceRate),
                denialRate: parseFloat(denialRate),
                totalReviews: totalReviews
              };
            }
          } catch (err) {
            console.error('Error fetching reviews:', err);
            // Continue with default reviewsData if reviews fetch fails
          }
        }

        // Transform data for better chart display
        const transformedData = {
          ...analyticsResponse.data,
          ageDistribution: transformAgeData(analyticsResponse.data.ageDistribution),
          chronicDiseasePrevalence: transformDiseaseData(analyticsResponse.data.chronicDiseasePrevalence),
          // Transform procedures data from new backend format
          procedureFrequency: analyticsResponse.data.procedures ?
            analyticsResponse.data.procedures.reduce((acc, proc) => {
              acc[proc.name] = proc.count;
              return acc;
            }, {}) : (analyticsResponse.data.procedureFrequency || {}),
          // Ensure appointmentsPerMonth is available
          appointmentsPerMonth: analyticsResponse.data.appointmentsPerMonth || [],
          // Ensure timeSeries is available for treatment sheet trends
          timeSeries: analyticsResponse.data.timeSeries || { sheets: [], appointments: [] },
          reviewStats: reviewsData
        };

        setAnalyticsData(transformedData);
        setLoading(false);
      } catch (err) {
        console.error('Fetch analytics error:', err);
        let errorMessage = 'Failed to load analytics data';
        if (err.response) {
          if (err.response.status === 401) {
            navigate('/login');
            return;
          }
          errorMessage = err.response.data?.message || errorMessage;
        }
        setError(errorMessage);
        setLoading(false);
      }
    };
    fetchAnalytics();
  }, [user, token, navigate, timeRange]);

  // Helper functions to transform data
  const transformAgeData = (ageData) => {
    return Object.entries(ageData)
      .sort(([a], [b]) => parseInt(a.split('-')[0]) - parseInt(b.split('-')[0]))
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});
  };

  const transformDiseaseData = (diseaseData) => {
    const sorted = Object.entries(diseaseData)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5); // Show top 5 only
    return Object.fromEntries(sorted);
  };

  // Function to toggle chart types
  const toggleChartType = (chartKey) => {
    const chartTypes = {
      appointments: ['doughnut', 'pie', 'bar'],
      appointmentsPerMonth: ['line', 'bar', 'area'],
      genderOverview: ['doughnut', 'pie', 'bar'],
      age: ['bar', 'line'],
      gender: ['pie', 'doughnut', 'bar'],
      procedures: ['bar', 'pie', 'doughnut'],
      treatmentSheetTrend: ['line', 'bar', 'area'],
      diseases: ['doughnut', 'pie', 'bar'],
      reviewStatus: ['doughnut', 'pie', 'bar'],
      reviewProcedures: ['bar', 'line'],
      reviewTrend: ['line', 'bar'],
      reviewQuality: ['radar', 'bar', 'polarArea']
    };

    const currentType = chartType[chartKey];
    const types = chartTypes[chartKey];
    const currentIndex = types.indexOf(currentType);
    const nextIndex = (currentIndex + 1) % types.length;

    setChartType({
      ...chartType,
      [chartKey]: types[nextIndex]
    });
  };

  // Function to render stars for ratings
  const renderStars = (rating) => (
    <div className="flex">
      {[...Array(5)].map((_, i) => (
        <FaStar
          key={i}
          className={`h-5 w-5 ${i < (rating || 0) ? 'text-yellow-400' : 'text-gray-300'}`}
        />
      ))}
    </div>
  );

  // Chart data configurations
  const appointmentStatusData = {
    labels: ['Pending', 'Completed', 'Cancelled'],
    datasets: [{
      data: [
        analyticsData.appointmentStats.pending,
        analyticsData.appointmentStats.completed,
        analyticsData.appointmentStats.cancelled,
      ],
      backgroundColor: ['#FBBF24', '#10B981', '#EF4444'],
      hoverBackgroundColor: ['#F59E0B', '#059669', '#DC2626'],
    }]
  };

  const appointmentTypesData = {
    labels: Object.keys(analyticsData.appointmentTypes),
    datasets: [{
      data: Object.values(analyticsData.appointmentTypes),
      backgroundColor: ['#3B82F6', '#10B981', '#FBBF24', '#EF4444', '#8B5CF6'],
    }]
  };

  // Appointments per month data
  const appointmentsPerMonthData = {
    labels: (analyticsData.appointmentsPerMonth || []).length > 0
      ? analyticsData.appointmentsPerMonth.map(item => item.month)
      : ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Total Appointments',
        data: (analyticsData.appointmentsPerMonth || []).length > 0
          ? analyticsData.appointmentsPerMonth.map(item => item.total)
          : [0, 0, 0, 0, 0, 0],
        backgroundColor: 'rgba(59, 130, 246, 0.5)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 2,
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Completed',
        data: (analyticsData.appointmentsPerMonth || []).length > 0
          ? analyticsData.appointmentsPerMonth.map(item => item.completed)
          : [0, 0, 0, 0, 0, 0],
        backgroundColor: 'rgba(16, 185, 129, 0.5)',
        borderColor: 'rgba(16, 185, 129, 1)',
        borderWidth: 2,
        tension: 0.4,
        fill: false,
      },
      {
        label: 'Pending',
        data: (analyticsData.appointmentsPerMonth || []).length > 0
          ? analyticsData.appointmentsPerMonth.map(item => item.pending)
          : [0, 0, 0, 0, 0, 0],
        backgroundColor: 'rgba(245, 158, 11, 0.5)',
        borderColor: 'rgba(245, 158, 11, 1)',
        borderWidth: 2,
        tension: 0.4,
        fill: false,
      }
    ]
  };

  // Treatment sheet trend data
  const treatmentSheetTrendData = {
    labels: (analyticsData.timeSeries?.sheets || []).length > 0
      ? analyticsData.timeSeries.sheets.map(item => item.month)
      : ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Treatment Sheets Submitted',
        data: (analyticsData.timeSeries?.sheets || []).length > 0
          ? analyticsData.timeSeries.sheets.map(item => item.sheets)
          : [0, 0, 0, 0, 0, 0],
        backgroundColor: 'rgba(16, 185, 129, 0.5)',
        borderColor: 'rgba(16, 185, 129, 1)',
        borderWidth: 2,
        tension: 0.4,
        fill: true,
      }
    ]
  };

  const ageDistributionData = {
    labels: Object.keys(analyticsData.ageDistribution),
    datasets: [{
      label: 'Patients',
      data: Object.values(analyticsData.ageDistribution),
      backgroundColor: '#3B82F6',
      borderColor: '#2563EB',
      borderWidth: 1,
    }]
  };

  const genderDistributionData = {
    labels: Object.keys(analyticsData.genderDistribution),
    datasets: [{
      data: Object.values(analyticsData.genderDistribution),
      backgroundColor: ['#3B82F6', '#EC4899', '#6B7280'],
      hoverBackgroundColor: ['#2563EB', '#DB2777', '#4B5563'],
    }]
  };

  const procedureFrequencyData = {
    labels: Object.keys(analyticsData.procedureFrequency),
    datasets: [{
      label: 'Treatment Sheets',
      data: Object.values(analyticsData.procedureFrequency),
      backgroundColor: [
        '#3B82F6', // Blue
        '#10B981', // Green
        '#F59E0B', // Amber
        '#EF4444', // Red
        '#8B5CF6', // Purple
        '#EC4899', // Pink
        '#06B6D4', // Cyan
        '#84CC16', // Lime
        '#F97316', // Orange
        '#6366F1', // Indigo
      ],
      borderColor: [
        '#2563EB',
        '#059669',
        '#D97706',
        '#DC2626',
        '#7C3AED',
        '#DB2777',
        '#0891B2',
        '#65A30D',
        '#EA580C',
        '#4F46E5',
      ],
      borderWidth: 1,
    }]
  };

  const chronicDiseaseData = {
    labels: Object.keys(analyticsData.chronicDiseasePrevalence),
    datasets: [{
      data: Object.values(analyticsData.chronicDiseasePrevalence),
      backgroundColor: ['#EF4444', '#FBBF24', '#3B82F6', '#8B5CF6', '#EC4899'],
      hoverBackgroundColor: ['#DC2626', '#F59E0B', '#2563EB', '#7C3AED', '#DB2777'],
    }]
  };

  // Review status distribution data
  const reviewStatusData = {
    labels: ['Accepted', 'Pending', 'Denied'],
    datasets: [{
      data: [
        analyticsData.reviewStats?.statusDistribution?.accepted || 0,
        analyticsData.reviewStats?.statusDistribution?.pending || 0,
        analyticsData.reviewStats?.statusDistribution?.denied || 0
      ],
      backgroundColor: ['#10B981', '#F59E0B', '#EF4444'],
      hoverBackgroundColor: ['#059669', '#D97706', '#DC2626'],
      borderWidth: 1
    }]
  };

  // Review procedure type distribution data
  const reviewProcedureData = {
    labels: Object.keys(analyticsData.reviewStats?.procedureTypeDistribution || {}),
    datasets: [
      {
        label: 'Accepted',
        data: Object.values(analyticsData.reviewStats?.procedureTypeDistribution || {}).map(item => item?.accepted || 0),
        backgroundColor: 'rgba(16, 185, 129, 0.7)',
        borderColor: 'rgba(16, 185, 129, 1)',
        borderWidth: 1,
      },
      {
        label: 'Pending',
        data: Object.values(analyticsData.reviewStats?.procedureTypeDistribution || {}).map(item => item?.pending || 0),
        backgroundColor: 'rgba(245, 158, 11, 0.7)',
        borderColor: 'rgba(245, 158, 11, 1)',
        borderWidth: 1,
      },
      {
        label: 'Denied',
        data: Object.values(analyticsData.reviewStats?.procedureTypeDistribution || {}).map(item => item?.denied || 0),
        backgroundColor: 'rgba(239, 68, 68, 0.7)',
        borderColor: 'rgba(239, 68, 68, 1)',
        borderWidth: 1,
      },
    ],
  };

  // Review trend data (mock data for now)
  const defaultMonths = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
  const defaultTotalData = [5, 8, 12, 15, 20, 25];
  const defaultAcceptedData = [4, 6, 9, 12, 16, 20];

  const reviewTrendData = {
    labels: (analyticsData.reviewStats?.reviewsByMonth || []).length > 0
      ? analyticsData.reviewStats?.reviewsByMonth.map(item => item.month)
      : defaultMonths,
    datasets: [
      {
        label: 'Total Reviews',
        data: (analyticsData.reviewStats?.reviewsByMonth || []).length > 0
          ? analyticsData.reviewStats?.reviewsByMonth.map(item => item.total)
          : defaultTotalData,
        backgroundColor: 'rgba(59, 130, 246, 0.5)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 2,
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Accepted Reviews',
        data: (analyticsData.reviewStats?.reviewsByMonth || []).length > 0
          ? analyticsData.reviewStats?.reviewsByMonth.map(item => item.accepted)
          : defaultAcceptedData,
        backgroundColor: 'rgba(16, 185, 129, 0.5)',
        borderColor: 'rgba(16, 185, 129, 1)',
        borderWidth: 2,
        tension: 0.4,
        fill: false,
      },
    ],
  };

  // Review quality metrics data
  const reviewQualityData = {
    labels: ['Procedure Quality', 'Patient Interaction', 'Documentation', 'Technique', 'Time Management', 'Communication'],
    datasets: [
      {
        label: 'Average Ratings',
        data: [
          analyticsData.reviewStats?.qualityMetrics?.avgProcedureQuality || 0,
          analyticsData.reviewStats?.qualityMetrics?.avgPatientInteraction || 0,
          analyticsData.reviewStats?.qualityMetrics?.avgDocumentation || 0,
          analyticsData.reviewStats?.qualityMetrics?.avgTechnique || 0,
          analyticsData.reviewStats?.qualityMetrics?.avgTimeManagement || 0,
          analyticsData.reviewStats?.qualityMetrics?.avgCommunication || 0,
        ],
        backgroundColor: 'rgba(59, 130, 246, 0.5)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 2,
        pointBackgroundColor: 'rgba(59, 130, 246, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(59, 130, 246, 1)',
        pointRadius: 4,
      },
    ],
  };

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading) {
    return <Loader />;
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />

        <main className="flex-1 overflow-y-auto p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">
                    Analytics Dashboard
                  </h1>
                  <p className="text-[#333333]">Insights for {user?.name || 'Student'}</p>
                </div>
                <div className="flex items-center gap-4">
                  <select
                    value={timeRange}
                    onChange={(e) => setTimeRange(e.target.value)}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] bg-white text-sm transition-all duration-300"
                  >
                    <option value="week">Last 7 Days</option>
                    <option value="month">Last 30 Days</option>
                    <option value="all">All Time</option>
                  </select>
                </div>
              </div>

              {/* Tab Navigation */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white rounded-xl shadow-md overflow-hidden mb-8"
              >
                <div className="flex overflow-x-auto">
                  <button
                    onClick={() => setActiveTab('overview')}
                    className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${
                      activeTab === 'overview'
                        ? 'border-[#20B2AA] text-[#0077B6]'
                        : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)]'
                    }`}
                  >
                    <FaChartBar className="mr-2" />
                    Overview
                  </button>
                  <button
                    onClick={() => setActiveTab('patients')}
                    className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${
                      activeTab === 'patients'
                        ? 'border-[#20B2AA] text-[#0077B6]'
                        : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)]'
                    }`}
                  >
                    <FaUserInjured className="mr-2" />
                    Patients
                  </button>
                  <button
                    onClick={() => setActiveTab('procedures')}
                    className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${
                      activeTab === 'procedures'
                        ? 'border-[#20B2AA] text-[#0077B6]'
                        : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)]'
                    }`}
                  >
                    <FaTooth className="mr-2" />
                    Treatment Sheets
                  </button>
                  <button
                    onClick={() => setActiveTab('reviews')}
                    className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${
                      activeTab === 'reviews'
                        ? 'border-[#20B2AA] text-[#0077B6]'
                        : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)]'
                    }`}
                  >
                    <FaClipboardCheck className="mr-2" />
                    Reviews
                  </button>
                </div>
              </motion.div>

              {/* Overview Tab */}
              {activeTab === 'overview' && (
                <>
                  {/* Summary Cards */}
                  <motion.div
                    variants={container}
                    initial="hidden"
                    whileInView="show"
                    viewport={{ once: true }}
                    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
                  >
                    <motion.div
                      variants={item}
                      className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#28A745] group"
                    >
                      <div className="flex items-center">
                        <div className="bg-[rgba(0,119,182,0.05)] w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-[rgba(0,119,182,0.1)] transition-colors duration-300">
                          <FaCalendarAlt className="h-6 w-6 text-[#0077B6]" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Appointments</p>
                          <p className="text-2xl font-bold text-[#0077B6]">
                            {analyticsData.appointmentStats.pending +
                             analyticsData.appointmentStats.completed +
                             analyticsData.appointmentStats.cancelled}
                          </p>
                          <div className="flex items-center text-xs text-gray-600 mt-1">
                            <span className="flex items-center text-yellow-600 mr-2">
                              <FaHourglassHalf className="h-3 w-3 mr-1" /> {analyticsData.appointmentStats.pending} pending
                            </span>
                            <span className="flex items-center text-green-600">
                              <FaCheckCircle className="h-3 w-3 mr-1" /> {analyticsData.appointmentStats.completed} completed
                            </span>
                          </div>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      variants={item}
                      className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#28A745] group"
                    >
                      <div className="flex items-center">
                        <div className="bg-green-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-green-100 transition-colors duration-300">
                          <FaUsers className="h-6 w-6 text-green-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Patients</p>
                          <p className="text-2xl font-bold text-[#0077B6]">
                            {Object.values(analyticsData.ageDistribution).reduce((a, b) => a + b, 0)}
                          </p>
                          <p className="text-xs text-gray-600 mt-1">
                            {Object.keys(analyticsData.genderDistribution).length > 0 &&
                              `${Math.round((analyticsData.genderDistribution['Male'] || 0) /
                                Object.values(analyticsData.genderDistribution).reduce((a, b) => a + b, 0) * 100)}% male,
                               ${Math.round((analyticsData.genderDistribution['Female'] || 0) /
                                Object.values(analyticsData.genderDistribution).reduce((a, b) => a + b, 0) * 100)}% female`
                            }
                          </p>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      variants={item}
                      className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#20B2AA] group"
                    >
                      <div className="flex items-center">
                        <div className="bg-purple-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-purple-100 transition-colors duration-300">
                          <FaTooth className="h-6 w-6 text-purple-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Treatment Sheets</p>
                          <p className="text-2xl font-bold text-[#0077B6]">
                            {Object.values(analyticsData.procedureFrequency).reduce((a, b) => a + b, 0)}
                          </p>
                          <p className="text-xs text-gray-600 mt-1">
                            {Object.keys(analyticsData.procedureFrequency).length > 0 &&
                              `Top: ${Object.entries(analyticsData.procedureFrequency)
                                .sort(([,a], [,b]) => b - a)[0][0]}`
                            }
                          </p>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      variants={item}
                      className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#20B2AA] group"
                    >
                      <div className="flex items-center">
                        <div className="bg-yellow-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-yellow-100 transition-colors duration-300">
                          <FaClipboardCheck className="h-6 w-6 text-yellow-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Reviews</p>
                          <p className="text-2xl font-bold text-[#0077B6]">
                            {analyticsData.reviewStats?.totalReviews || 0}
                          </p>
                          <div className="flex items-center text-xs text-gray-600 mt-1">
                            <span className="flex items-center text-green-600 mr-2">
                              <FaCheckCircle className="h-3 w-3 mr-1" /> {analyticsData.reviewStats?.statusDistribution?.accepted || 0} accepted
                            </span>
                            <span className="flex items-center text-yellow-600">
                              <FaHourglassHalf className="h-3 w-3 mr-1" /> {analyticsData.reviewStats?.statusDistribution?.pending || 0} pending
                            </span>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  </motion.div>
                </>
              )}

              {/* Overview Tab Charts */}
              {activeTab === 'overview' && (
                <motion.div
                  variants={container}
                  initial="hidden"
                  whileInView="show"
                  viewport={{ once: true }}
                  className="grid grid-cols-1 lg:grid-cols-2 gap-6"
                >
                  {/* Patient Gender Distribution */}
                  <motion.div
                    variants={item}
                    className="bg-white p-6 rounded-xl shadow-sm border border-[rgba(0,119,182,0.1)] hover:shadow-md transition-all duration-300"
                  >
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-bold text-[#0077B6]">Patient Gender Distribution</h2>
                      <button
                        onClick={() => toggleChartType('genderOverview')}
                        className="px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm"
                      >
                        Change Chart
                      </button>
                    </div>
                    <div className="h-64">
                      {chartType.genderOverview === 'doughnut' && (
                        <Doughnut
                          data={genderDistributionData}
                          options={{
                            maintainAspectRatio: false,
                            plugins: {
                              legend: { position: 'bottom' },
                            },
                          }}
                        />
                      )}
                      {chartType.genderOverview === 'pie' && (
                        <Pie
                          data={genderDistributionData}
                          options={{
                            maintainAspectRatio: false,
                            plugins: {
                              legend: { position: 'bottom' },
                            },
                          }}
                        />
                      )}
                      {chartType.genderOverview === 'bar' && (
                        <Bar
                          data={genderDistributionData}
                          options={{
                            maintainAspectRatio: false,
                            scales: {
                              y: { beginAtZero: true },
                            },
                          }}
                        />
                      )}
                    </div>
                  </motion.div>

                  {/* Appointments Per Month */}
                  <motion.div
                    variants={item}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-bold text-[#0077B6]">Appointments Per Month</h2>
                      <button
                        onClick={() => toggleChartType('appointmentsPerMonth')}
                        className="px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm"
                      >
                        Change Chart
                      </button>
                    </div>
                    <div className="h-64">
                      {chartType.appointmentsPerMonth === 'line' && (
                        <Line
                          data={appointmentsPerMonthData}
                          options={{
                            maintainAspectRatio: false,
                            scales: {
                              y: { beginAtZero: true },
                            },
                            plugins: {
                              legend: { position: 'bottom' },
                            },
                          }}
                        />
                      )}
                      {chartType.appointmentsPerMonth === 'bar' && (
                        <Bar
                          data={appointmentsPerMonthData}
                          options={{
                            maintainAspectRatio: false,
                            scales: {
                              y: { beginAtZero: true },
                            },
                            plugins: {
                              legend: { position: 'bottom' },
                            },
                          }}
                        />
                      )}
                      {chartType.appointmentsPerMonth === 'area' && (
                        <Line
                          data={appointmentsPerMonthData}
                          options={{
                            maintainAspectRatio: false,
                            scales: {
                              y: { beginAtZero: true },
                            },
                            plugins: {
                              legend: { position: 'bottom' },
                            },
                            elements: {
                              line: {
                                fill: true,
                              },
                            },
                          }}
                        />
                      )}
                    </div>
                  </motion.div>
                </motion.div>
              )}

              {/* Patients Tab */}
              {activeTab === 'patients' && (
                <motion.div
                  variants={container}
                  initial="hidden"
                  whileInView="show"
                  viewport={{ once: true }}
                  className="grid grid-cols-1 lg:grid-cols-2 gap-6"
                >
                  {/* Age Distribution */}
                  <motion.div
                    variants={item}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-bold text-[#0077B6]">Patient Age Distribution</h2>
                      <button
                        onClick={() => toggleChartType('age')}
                        className="px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm"
                      >
                        Change Chart
                      </button>
                    </div>
                    <div className="h-64">
                      {chartType.age === 'bar' && (
                        <Bar
                          data={ageDistributionData}
                          options={{
                            maintainAspectRatio: false,
                            scales: {
                              y: { beginAtZero: true },
                            },
                          }}
                        />
                      )}
                      {chartType.age === 'line' && (
                        <Line
                          data={ageDistributionData}
                          options={{
                            maintainAspectRatio: false,
                            scales: {
                              y: { beginAtZero: true },
                            },
                          }}
                        />
                      )}
                    </div>
                  </motion.div>

                  {/* Gender Distribution */}
                  <motion.div
                    variants={item}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-bold text-[#0077B6]">Patient Gender</h2>
                      <button
                        onClick={() => toggleChartType('gender')}
                        className="px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm"
                      >
                        Change Chart
                      </button>
                    </div>
                    <div className="h-64">
                      {chartType.gender === 'pie' && (
                        <Pie
                          data={genderDistributionData}
                          options={{
                            maintainAspectRatio: false,
                            plugins: {
                              legend: { position: 'bottom' },
                            },
                          }}
                        />
                      )}
                      {chartType.gender === 'doughnut' && (
                        <Doughnut
                          data={genderDistributionData}
                          options={{
                            maintainAspectRatio: false,
                            plugins: {
                              legend: { position: 'bottom' },
                            },
                          }}
                        />
                      )}
                      {chartType.gender === 'bar' && (
                        <Bar
                          data={genderDistributionData}
                          options={{
                            maintainAspectRatio: false,
                            scales: {
                              y: { beginAtZero: true },
                            },
                          }}
                        />
                      )}
                    </div>
                  </motion.div>

                  {/* Chronic Diseases */}
                  <motion.div
                    variants={item}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 lg:col-span-2"
                  >
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-bold text-[#0077B6]">Chronic Disease Prevalence</h2>
                      <button
                        onClick={() => toggleChartType('diseases')}
                        className="px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm"
                      >
                        Change Chart
                      </button>
                    </div>
                    <div className="h-64">
                      {chartType.diseases === 'doughnut' && (
                        <Doughnut
                          data={chronicDiseaseData}
                          options={{
                            maintainAspectRatio: false,
                            plugins: {
                              legend: { position: 'bottom' },
                            },
                          }}
                        />
                      )}
                      {chartType.diseases === 'pie' && (
                        <Pie
                          data={chronicDiseaseData}
                          options={{
                            maintainAspectRatio: false,
                            plugins: {
                              legend: { position: 'bottom' },
                            },
                          }}
                        />
                      )}
                      {chartType.diseases === 'bar' && (
                        <Bar
                          data={chronicDiseaseData}
                          options={{
                            maintainAspectRatio: false,
                            scales: {
                              y: { beginAtZero: true },
                            },
                          }}
                        />
                      )}
                    </div>
                  </motion.div>
                </motion.div>
              )}

              {/* Procedures Tab */}
              {activeTab === 'procedures' && (
                <motion.div
                  variants={container}
                  initial="hidden"
                  whileInView="show"
                  viewport={{ once: true }}
                  className="grid grid-cols-1 lg:grid-cols-2 gap-6"
                >
                  {/* Procedure Frequency */}
                  <motion.div
                    variants={item}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-bold text-[#0077B6]">Treatment Sheet Submissions by Type</h2>
                      <button
                        onClick={() => toggleChartType('procedures')}
                        className="px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm"
                      >
                        Change Chart
                      </button>
                    </div>
                    <div className="h-80">
                      {chartType.procedures === 'bar' && (
                        <Bar
                          data={procedureFrequencyData}
                          options={{
                            maintainAspectRatio: false,
                            scales: {
                              y: { beginAtZero: true },
                            },
                            indexAxis: 'y', // Horizontal bars
                          }}
                        />
                      )}
                      {chartType.procedures === 'pie' && (
                        <Pie
                          data={procedureFrequencyData}
                          options={{
                            maintainAspectRatio: false,
                            plugins: {
                              legend: { position: 'bottom' },
                            },
                          }}
                        />
                      )}
                      {chartType.procedures === 'doughnut' && (
                        <Doughnut
                          data={procedureFrequencyData}
                          options={{
                            maintainAspectRatio: false,
                            plugins: {
                              legend: { position: 'bottom' },
                            },
                          }}
                        />
                      )}
                    </div>
                  </motion.div>

                  {/* Treatment Sheet Submissions Over Time */}
                  <motion.div
                    variants={item}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                  >
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-bold text-[#0077B6]">Treatment Sheet Submissions Over Time</h2>
                      <button
                        onClick={() => toggleChartType('treatmentSheetTrend')}
                        className="px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm"
                      >
                        Change Chart
                      </button>
                    </div>
                    <div className="h-64">
                      {chartType.treatmentSheetTrend === 'line' && (
                        <Line
                          data={treatmentSheetTrendData}
                          options={{
                            maintainAspectRatio: false,
                            scales: {
                              y: { beginAtZero: true },
                            },
                            plugins: {
                              legend: { position: 'bottom' },
                            },
                          }}
                        />
                      )}
                      {chartType.treatmentSheetTrend === 'bar' && (
                        <Bar
                          data={treatmentSheetTrendData}
                          options={{
                            maintainAspectRatio: false,
                            scales: {
                              y: { beginAtZero: true },
                            },
                            plugins: {
                              legend: { position: 'bottom' },
                            },
                          }}
                        />
                      )}
                      {chartType.treatmentSheetTrend === 'area' && (
                        <Line
                          data={treatmentSheetTrendData}
                          options={{
                            maintainAspectRatio: false,
                            scales: {
                              y: { beginAtZero: true },
                            },
                            plugins: {
                              legend: { position: 'bottom' },
                            },
                            elements: {
                              line: {
                                fill: true,
                              },
                            },
                          }}
                        />
                      )}
                    </div>
                  </motion.div>
                </motion.div>
              )}

              {/* Reviews Tab */}
              {activeTab === 'reviews' && (
                <>
                  {/* Review Summary Cards */}
                  <motion.div
                    variants={container}
                    initial="hidden"
                    whileInView="show"
                    viewport={{ once: true }}
                    className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
                  >
                    <motion.div
                      variants={item}
                      className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100"
                    >
                      <div className="flex items-center mb-4">
                        <div className="bg-[rgba(0,119,182,0.1)] w-10 h-10 rounded-full flex items-center justify-center mr-3">
                          <FaClipboardCheck className="h-5 w-5 text-[#0077B6]" />
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Total Reviews</h3>
                          <p className="text-2xl font-bold text-[#0077B6]">
                            {analyticsData.reviewStats?.totalReviews || 0}
                          </p>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      variants={item}
                      className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100"
                    >
                      <div className="flex items-center mb-4">
                        <div className="bg-[rgba(40,167,69,0.1)] w-10 h-10 rounded-full flex items-center justify-center mr-3">
                          <FaCheckCircle className="h-5 w-5 text-[#28A745]" />
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Acceptance Rate</h3>
                          <p className="text-2xl font-bold text-[#28A745]">
                            {analyticsData.reviewStats?.acceptanceRate || 0}%
                          </p>
                        </div>
                      </div>
                      <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-[#28A745] rounded-full"
                          style={{ width: `${analyticsData.reviewStats?.acceptanceRate || 0}%` }}
                        ></div>
                      </div>
                    </motion.div>

                    <motion.div
                      variants={item}
                      className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100"
                    >
                      <div className="flex items-center mb-4">
                        <div className="bg-[rgba(32,178,170,0.1)] w-10 h-10 rounded-full flex items-center justify-center mr-3">
                          <FaStarHalfAlt className="h-5 w-5 text-[#20B2AA]" />
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Avg. Procedure Quality</h3>
                          <p className="text-2xl font-bold text-[#20B2AA]">
                            {analyticsData.reviewStats?.qualityMetrics?.avgProcedureQuality || 0}/5
                          </p>
                        </div>
                      </div>
                      <div className="mt-1">
                        {renderStars(analyticsData.reviewStats?.qualityMetrics?.avgProcedureQuality || 0)}
                      </div>
                    </motion.div>

                    <motion.div
                      variants={item}
                      className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100"
                    >
                      <div className="flex items-center mb-4">
                        <div className="bg-[rgba(0,119,182,0.1)] w-10 h-10 rounded-full flex items-center justify-center mr-3">
                          <FaStarHalfAlt className="h-5 w-5 text-[#0077B6]" />
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Avg. Patient Interaction</h3>
                          <p className="text-2xl font-bold text-[#0077B6]">
                            {analyticsData.reviewStats?.qualityMetrics?.avgPatientInteraction || 0}/5
                          </p>
                        </div>
                      </div>
                      <div className="mt-1">
                        {renderStars(analyticsData.reviewStats?.qualityMetrics?.avgPatientInteraction || 0)}
                      </div>
                    </motion.div>
                  </motion.div>

                  {/* Review Charts */}
                  <motion.div
                    variants={container}
                    initial="hidden"
                    whileInView="show"
                    viewport={{ once: true }}
                    className="grid grid-cols-1 lg:grid-cols-2 gap-6"
                  >
                    {/* Review Status Distribution */}
                    <motion.div
                      variants={item}
                      className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                    >
                      <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-bold text-[#0077B6]">Review Status Distribution</h2>
                        <button
                          onClick={() => toggleChartType('reviewStatus')}
                          className="px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm"
                        >
                          Change Chart
                        </button>
                      </div>
                      <div className="h-64" ref={statusChartRef}>
                        {chartType.reviewStatus === 'doughnut' && (
                          <Doughnut
                            data={reviewStatusData}
                            options={{
                              maintainAspectRatio: false,
                              plugins: {
                                legend: { position: 'bottom' },
                              },
                            }}
                          />
                        )}
                        {chartType.reviewStatus === 'pie' && (
                          <Pie
                            data={reviewStatusData}
                            options={{
                              maintainAspectRatio: false,
                              plugins: {
                                legend: { position: 'bottom' },
                              },
                            }}
                          />
                        )}
                        {chartType.reviewStatus === 'bar' && (
                          <Bar
                            data={reviewStatusData}
                            options={{
                              maintainAspectRatio: false,
                              scales: {
                                y: { beginAtZero: true },
                              },
                            }}
                          />
                        )}
                      </div>
                    </motion.div>

                    {/* Review by Procedure Type */}
                    <motion.div
                      variants={item}
                      className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                    >
                      <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-bold text-[#0077B6]">Reviews by Procedure Type</h2>
                        <button
                          onClick={() => toggleChartType('reviewProcedures')}
                          className="px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm"
                        >
                          Change Chart
                        </button>
                      </div>
                      <div className="h-64" ref={procedureChartRef}>
                        {chartType.reviewProcedures === 'bar' && (
                          <Bar
                            data={reviewProcedureData}
                            options={{
                              maintainAspectRatio: false,
                              scales: {
                                x: { stacked: true },
                                y: { stacked: true, beginAtZero: true },
                              },
                            }}
                          />
                        )}
                        {chartType.reviewProcedures === 'line' && (
                          <Line
                            data={reviewProcedureData}
                            options={{
                              maintainAspectRatio: false,
                              scales: {
                                y: { beginAtZero: true },
                              },
                            }}
                          />
                        )}
                      </div>
                    </motion.div>

                    {/* Review Quality Metrics */}
                    <motion.div
                      variants={item}
                      className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                    >
                      <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-bold text-[#0077B6]">Review Quality Metrics</h2>
                        <button
                          onClick={() => toggleChartType('reviewQuality')}
                          className="px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm"
                        >
                          Change Chart
                        </button>
                      </div>
                      <div className="h-64" ref={qualityMetricsChartRef}>
                        {chartType.reviewQuality === 'radar' && (
                          <Radar
                            data={reviewQualityData}
                            options={{
                              maintainAspectRatio: false,
                              scales: {
                                r: {
                                  beginAtZero: true,
                                  max: 5,
                                  ticks: {
                                    stepSize: 1
                                  }
                                }
                              },
                            }}
                          />
                        )}
                        {chartType.reviewQuality === 'bar' && (
                          <Bar
                            data={reviewQualityData}
                            options={{
                              maintainAspectRatio: false,
                              scales: {
                                y: {
                                  beginAtZero: true,
                                  max: 5,
                                  ticks: {
                                    stepSize: 1
                                  }
                                },
                              },
                            }}
                          />
                        )}
                        {chartType.reviewQuality === 'polarArea' && (
                          <PolarArea
                            data={reviewQualityData}
                            options={{
                              maintainAspectRatio: false,
                              scales: {
                                r: {
                                  beginAtZero: true,
                                  max: 5,
                                  ticks: {
                                    stepSize: 1
                                  }
                                }
                              },
                            }}
                          />
                        )}
                      </div>
                    </motion.div>

                    {/* Review Trend Over Time */}
                    <motion.div
                      variants={item}
                      className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
                    >
                      <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-bold text-[#0077B6]">Review Trend Over Time</h2>
                        <button
                          onClick={() => toggleChartType('reviewTrend')}
                          className="px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm"
                        >
                          Change Chart
                        </button>
                      </div>
                      <div className="h-64" ref={reviewTrendChartRef}>
                        {chartType.reviewTrend === 'line' && (
                          <Line
                            data={reviewTrendData}
                            options={{
                              maintainAspectRatio: false,
                              scales: {
                                y: { beginAtZero: true },
                              },
                            }}
                          />
                        )}
                        {chartType.reviewTrend === 'bar' && (
                          <Bar
                            data={reviewTrendData}
                            options={{
                              maintainAspectRatio: false,
                              scales: {
                                y: { beginAtZero: true },
                              },
                            }}
                          />
                        )}
                      </div>
                    </motion.div>
                  </motion.div>
                </>
              )}
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Analytics;