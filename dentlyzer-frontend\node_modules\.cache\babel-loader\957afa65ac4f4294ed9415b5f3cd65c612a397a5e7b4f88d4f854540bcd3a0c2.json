{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\student\\\\Consent.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useRef } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { FaSignature, FaFileAlt, FaCheck, FaTimes, FaPen, FaFont, FaUpload } from 'react-icons/fa';\nimport axios from 'axios';\nimport Navbar from './Navbar';\nimport Sidebar from './Sidebar';\nimport PatientNav from './PatientNav';\nimport { useAuth } from '../context/AuthContext';\nimport Loader from '../components/Loader';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Consent = () => {\n  _s();\n  var _patientData$consent6, _patientData$consent7, _patientData$consent8, _patientData$consent9, _patientData$consent10, _patientData$consent11, _patientData$consent12, _patientData$consent13, _patientData$consent14, _patientData$consent15, _patientData$consent16;\n  const {\n    nationalId\n  } = useParams();\n  const {\n    user,\n    token\n  } = useAuth();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [patientData, setPatientData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [signatureMethod, setSignatureMethod] = useState('text'); // 'text', 'draw', or 'upload'\n  const [signatureText, setSignatureText] = useState('');\n  const [signatureImage, setSignatureImage] = useState('');\n  const [uploadedSignature, setUploadedSignature] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // Canvas refs and state\n  const canvasRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const [isDrawing, setIsDrawing] = useState(false);\n  const [canvasContext, setCanvasContext] = useState(null);\n  useEffect(() => {\n    const fetchPatientData = async () => {\n      try {\n        const response = await axios.get(`http://localhost:5000/api/patients/public/${nationalId}`, {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        });\n\n        // Ensure the patient data has a consent field, even if it's empty\n        const patientWithConsent = {\n          ...response.data,\n          consent: response.data.consent || {\n            isSigned: false\n          }\n        };\n        setPatientData(patientWithConsent);\n\n        // If patient already has a signed consent, populate the signature fields\n        if (patientWithConsent.consent && patientWithConsent.consent.isSigned) {\n          if (patientWithConsent.consent.signatureText) {\n            setSignatureText(patientWithConsent.consent.signatureText);\n            setSignatureMethod('text');\n          } else if (patientWithConsent.consent.signatureImage) {\n            // Check if it's a drawn signature or uploaded image\n            if (patientWithConsent.consent.signatureImage.startsWith('data:image')) {\n              setSignatureImage(patientWithConsent.consent.signatureImage);\n              setSignatureMethod('draw');\n            } else {\n              setUploadedSignature(patientWithConsent.consent.signatureImage);\n              setSignatureMethod('upload');\n            }\n          }\n        }\n      } catch (err) {\n        var _err$response, _err$response2, _err$response2$data;\n        console.error('Error fetching patient data:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n        setError(((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || 'Failed to load patient data');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (token && nationalId) {\n      fetchPatientData();\n    }\n  }, [nationalId, token]);\n\n  // Initialize canvas when component mounts\n  useEffect(() => {\n    // This effect needs to run after the canvas is rendered in the DOM\n    const initializeCanvas = () => {\n      if (canvasRef.current) {\n        const canvas = canvasRef.current;\n        const ctx = canvas.getContext('2d');\n        if (ctx) {\n          // Set canvas properties\n          ctx.lineWidth = 2;\n          ctx.lineCap = 'round';\n          ctx.lineJoin = 'round';\n          ctx.strokeStyle = 'black';\n\n          // Clear the canvas first\n          ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n          // Set canvas background to white for better visibility\n          ctx.fillStyle = 'white';\n          ctx.fillRect(0, 0, canvas.width, canvas.height);\n          setCanvasContext(ctx);\n\n          // If there's a signature image and we're in draw mode, draw it on the canvas\n          if (signatureImage && signatureMethod === 'draw') {\n            const img = new Image();\n            img.onload = () => {\n              // Clear canvas before drawing\n              ctx.clearRect(0, 0, canvas.width, canvas.height);\n              ctx.fillStyle = 'white';\n              ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n              // Draw the image centered and scaled appropriately\n              ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n            };\n            img.src = signatureImage;\n          }\n        }\n      }\n    };\n\n    // Initialize immediately and also after a short delay to ensure DOM is ready\n    initializeCanvas();\n    const timer = setTimeout(initializeCanvas, 100);\n\n    // Also reinitialize when the window is resized\n    const handleResize = () => {\n      initializeCanvas();\n    };\n    window.addEventListener('resize', handleResize);\n    return () => {\n      clearTimeout(timer);\n      window.removeEventListener('resize', handleResize);\n    };\n  }, [signatureMethod, signatureImage]);\n\n  // Drawing functions\n  const getMousePos = (canvas, evt) => {\n    const rect = canvas.getBoundingClientRect();\n    const scaleX = canvas.width / rect.width;\n    const scaleY = canvas.height / rect.height;\n    return {\n      x: (evt.clientX - rect.left) * scaleX,\n      y: (evt.clientY - rect.top) * scaleY\n    };\n  };\n  const startDrawing = e => {\n    var _patientData$consent;\n    if (patientData !== null && patientData !== void 0 && (_patientData$consent = patientData.consent) !== null && _patientData$consent !== void 0 && _patientData$consent.isSigned || !canvasRef.current) return; // Prevent drawing if already signed or canvas not ready\n\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n    const pos = getMousePos(canvas, e);\n\n    // Set drawing style\n    ctx.lineWidth = 2;\n    ctx.lineCap = 'round';\n    ctx.lineJoin = 'round';\n    ctx.strokeStyle = 'black';\n    ctx.beginPath();\n    ctx.moveTo(pos.x, pos.y);\n    setCanvasContext(ctx);\n    setIsDrawing(true);\n  };\n  const draw = e => {\n    var _patientData$consent2;\n    if (!isDrawing || patientData !== null && patientData !== void 0 && (_patientData$consent2 = patientData.consent) !== null && _patientData$consent2 !== void 0 && _patientData$consent2.isSigned || !canvasRef.current) return;\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n    const pos = getMousePos(canvas, e);\n    ctx.lineTo(pos.x, pos.y);\n    ctx.stroke();\n  };\n  const stopDrawing = () => {\n    if (isDrawing && canvasRef.current) {\n      const ctx = canvasRef.current.getContext('2d');\n      if (ctx) {\n        ctx.closePath();\n      }\n      setIsDrawing(false);\n\n      // Save the canvas content as an image\n      const dataUrl = canvasRef.current.toDataURL('image/png');\n      setSignatureImage(dataUrl);\n    }\n  };\n  const clearCanvas = () => {\n    var _patientData$consent3;\n    if (patientData !== null && patientData !== void 0 && (_patientData$consent3 = patientData.consent) !== null && _patientData$consent3 !== void 0 && _patientData$consent3.isSigned || !canvasRef.current) return; // Prevent clearing if already signed\n\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Clear the canvas\n    ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n    // Reset to white background\n    ctx.fillStyle = 'white';\n    ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n    // Reset drawing state\n    setIsDrawing(false);\n    setSignatureImage('');\n\n    // Reset context properties\n    ctx.lineWidth = 2;\n    ctx.lineCap = 'round';\n    ctx.lineJoin = 'round';\n    ctx.strokeStyle = 'black';\n    setCanvasContext(ctx);\n  };\n  const handleSignatureMethodChange = method => {\n    var _patientData$consent4;\n    if (patientData !== null && patientData !== void 0 && (_patientData$consent4 = patientData.consent) !== null && _patientData$consent4 !== void 0 && _patientData$consent4.isSigned) return; // Prevent changing if already signed\n    setSignatureMethod(method);\n  };\n  const handleFileUpload = event => {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    // Validate file type\n    if (!file.type.startsWith('image/')) {\n      setError('Please select a valid image file.');\n      return;\n    }\n\n    // Validate file size (max 5MB)\n    if (file.size > 5 * 1024 * 1024) {\n      setError('File size must be less than 5MB.');\n      return;\n    }\n    const reader = new FileReader();\n    reader.onload = e => {\n      setUploadedSignature(e.target.result);\n      setError(''); // Clear any previous errors\n    };\n    reader.readAsDataURL(file);\n  };\n  const handleSignConsent = async () => {\n    var _patientData$consent5;\n    if (patientData !== null && patientData !== void 0 && (_patientData$consent5 = patientData.consent) !== null && _patientData$consent5 !== void 0 && _patientData$consent5.isSigned) {\n      setError('This consent form has already been signed.');\n      return;\n    }\n\n    // Validate signature\n    if (signatureMethod === 'text' && !signatureText.trim()) {\n      setError('Please enter your signature in the text field.');\n      return;\n    }\n    if (signatureMethod === 'draw' && !signatureImage) {\n      setError('Please draw your signature on the canvas.');\n      return;\n    }\n    if (signatureMethod === 'upload' && !uploadedSignature) {\n      setError('Please upload a signature image.');\n      return;\n    }\n    setIsSubmitting(true);\n    setError('');\n    try {\n      const response = await axios.put(`http://localhost:5000/api/patients/${nationalId}/consent`, {\n        signatureText: signatureMethod === 'text' ? signatureText : '',\n        signatureImage: signatureMethod === 'draw' ? signatureImage : signatureMethod === 'upload' ? uploadedSignature : ''\n      }, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n\n      // Update the patient data with the new consent information\n      setPatientData({\n        ...patientData,\n        consent: response.data.consent\n      });\n      setSuccess('Consent form signed successfully!');\n      setTimeout(() => setSuccess(''), 3000);\n    } catch (err) {\n      var _err$response3, _err$response4, _err$response4$data;\n      console.error('Error signing consent:', ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.data) || err.message);\n      setError(((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.message) || 'Failed to sign consent form');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n        isOpen: sidebarOpen,\n        setIsOpen: setSidebarOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {\n          toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PatientNav, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\",\n          children: /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PatientNav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto\",\n          children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            className: \"mb-6 p-4 bg-red-100 text-[#333333] rounded-lg\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this), success && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            className: \"mb-6 p-4 bg-[#28A745]/20 text-[#28A745] rounded-lg\",\n            children: success\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            className: \"bg-white rounded-xl shadow-lg overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 border-b border-gray-100\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-2xl font-bold text-[#0077B6]\",\n                    children: \"Dental Treatment Consent Form\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600 mt-1\",\n                    children: patientData !== null && patientData !== void 0 && (_patientData$consent6 = patientData.consent) !== null && _patientData$consent6 !== void 0 && _patientData$consent6.isSigned ? 'This consent form has been signed and cannot be modified.' : 'Please read carefully and sign to provide your consent for dental treatment.'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `p-3 rounded-full ${patientData !== null && patientData !== void 0 && (_patientData$consent7 = patientData.consent) !== null && _patientData$consent7 !== void 0 && _patientData$consent7.isSigned ? 'bg-[#28A745]/20' : 'bg-[#0077B6]/10'}`,\n                  children: patientData !== null && patientData !== void 0 && (_patientData$consent8 = patientData.consent) !== null && _patientData$consent8 !== void 0 && _patientData$consent8.isSigned ? /*#__PURE__*/_jsxDEV(FaCheck, {\n                    className: \"h-6 w-6 text-[#28A745]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(FaSignature, {\n                    className: \"h-6 w-6 text-[#0077B6]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"prose max-w-none\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-4\",\n                  children: \"General Dental Treatment Consent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-4\",\n                  children: [\"I, \", (patientData === null || patientData === void 0 ? void 0 : patientData.fullName) || '[Patient Name]', \", hereby authorize the dental team to perform the following dental treatment or oral surgery procedure(s): examination, radiographs, prophylaxis (cleaning), fluoride treatment, restorations (fillings), crowns, bridges, extractions, root canal therapy, periodontal therapy, and other procedures deemed necessary.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-4\",\n                  children: \"I understand that there are risks associated with dental treatment including but not limited to:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"list-disc pl-5 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Sensitivity or pain in treated or adjacent teeth\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Infection requiring additional treatment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Fracture of tooth/teeth or dental restorations\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Swelling, bleeding, or discomfort following procedures\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Reactions to medications, anesthetics, or materials used\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Changes in occlusion (bite) requiring adjustment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-4\",\n                  children: \"I understand that during treatment, unforeseen conditions may arise which may necessitate procedures different from those planned. I consent to those additional procedures that are necessary in the professional judgment of my treating dentist.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-4\",\n                  children: \"I understand that I may ask questions about the planned procedures and that I have the right to be informed of alternative treatments. I have disclosed my complete medical history, including medications and allergies.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-4\",\n                  children: \"I consent to the administration of local anesthesia, antibiotics, analgesics, or any other medication necessary for dental treatment. I understand the risks involved in the administration of these medications.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-4\",\n                  children: \"I consent to the making of photographs, video recordings, and x-rays before, during, and after treatment, and to their use for scientific, educational, or research purposes.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-6\",\n                  children: \"By signing below, I acknowledge that I have read and understand this consent form, had the opportunity to ask questions, and give my consent for dental treatment.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border-t border-gray-200 pt-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-lg font-semibold\",\n                      children: \"Patient Signature\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 23\n                    }, this), (patientData === null || patientData === void 0 ? void 0 : (_patientData$consent9 = patientData.consent) === null || _patientData$consent9 === void 0 ? void 0 : _patientData$consent9.isSigned) && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-green-600 font-medium flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                        className: \"mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 420,\n                        columnNumber: 27\n                      }, this), \" Signed on \", new Date(patientData.consent.signedAt).toLocaleDateString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 419,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 21\n                  }, this), !(patientData !== null && patientData !== void 0 && (_patientData$consent10 = patientData.consent) !== null && _patientData$consent10 !== void 0 && _patientData$consent10.isSigned) && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex space-x-4 mb-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleSignatureMethodChange('text'),\n                        className: `flex items-center px-4 py-2 rounded-lg ${signatureMethod === 'text' ? 'bg-[#0077B6]/10 text-[#0077B6] border border-[#0077B6]/30' : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'}`,\n                        children: [/*#__PURE__*/_jsxDEV(FaFont, {\n                          className: \"mr-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 436,\n                          columnNumber: 29\n                        }, this), \" Text Signature\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 428,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleSignatureMethodChange('draw'),\n                        className: `flex items-center px-4 py-2 rounded-lg ${signatureMethod === 'draw' ? 'bg-[#20B2AA]/10 text-[#20B2AA] border border-[#20B2AA]/30' : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'}`,\n                        children: [/*#__PURE__*/_jsxDEV(FaPen, {\n                          className: \"mr-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 446,\n                          columnNumber: 29\n                        }, this), \" Draw Signature\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 438,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 427,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 23\n                  }, this), signatureMethod === 'text' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-2\",\n                      children: \"Type your full name as your signature:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 455,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: signatureText,\n                      onChange: e => setSignatureText(e.target.value),\n                      disabled: patientData === null || patientData === void 0 ? void 0 : (_patientData$consent11 = patientData.consent) === null || _patientData$consent11 === void 0 ? void 0 : _patientData$consent11.isSigned,\n                      placeholder: \"Type your full name here\",\n                      className: `w-full px-4 py-2 border ${patientData !== null && patientData !== void 0 && (_patientData$consent12 = patientData.consent) !== null && _patientData$consent12 !== void 0 && _patientData$consent12.isSigned ? 'bg-gray-100 border-gray-300' : 'border-gray-300 focus:border-[#0077B6] focus:ring-1 focus:ring-[#0077B6]'} rounded-lg`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 458,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 23\n                  }, this), signatureMethod === 'draw' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-2\",\n                      children: \"Draw your signature below:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 476,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"border border-gray-300 rounded-lg overflow-hidden bg-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n                        ref: canvasRef,\n                        width: 600,\n                        height: 150,\n                        onMouseDown: startDrawing,\n                        onMouseMove: draw,\n                        onMouseUp: stopDrawing,\n                        onMouseLeave: stopDrawing,\n                        onTouchStart: e => {\n                          e.preventDefault(); // Prevent scrolling when drawing\n                          if (e.touches && e.touches.length > 0) {\n                            const touch = e.touches[0];\n                            startDrawing({\n                              clientX: touch.clientX,\n                              clientY: touch.clientY\n                            });\n                          }\n                        },\n                        onTouchMove: e => {\n                          e.preventDefault(); // Prevent scrolling when drawing\n                          if (e.touches && e.touches.length > 0) {\n                            const touch = e.touches[0];\n                            draw({\n                              clientX: touch.clientX,\n                              clientY: touch.clientY\n                            });\n                          }\n                        },\n                        onTouchEnd: stopDrawing,\n                        className: `w-full ${patientData !== null && patientData !== void 0 && (_patientData$consent13 = patientData.consent) !== null && _patientData$consent13 !== void 0 && _patientData$consent13.isSigned ? 'cursor-not-allowed' : 'cursor-crosshair'}`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 480,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 479,\n                      columnNumber: 25\n                    }, this), !(patientData !== null && patientData !== void 0 && (_patientData$consent14 = patientData.consent) !== null && _patientData$consent14 !== void 0 && _patientData$consent14.isSigned) && /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: clearCanvas,\n                      className: \"mt-2 px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300\",\n                      children: \"Clear\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 513,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col md:flex-row justify-between mt-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-4 md:mb-0\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: \"Patient Name:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 526,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: (patientData === null || patientData === void 0 ? void 0 : patientData.fullName) || 'Patient Name'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 527,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 525,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: \"Date:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 532,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: patientData !== null && patientData !== void 0 && (_patientData$consent15 = patientData.consent) !== null && _patientData$consent15 !== void 0 && _patientData$consent15.signedAt ? new Date(patientData.consent.signedAt).toLocaleDateString() : new Date().toLocaleDateString()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 533,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 531,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 524,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 border-t border-gray-100 bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-end\",\n                children: !(patientData !== null && patientData !== void 0 && (_patientData$consent16 = patientData.consent) !== null && _patientData$consent16 !== void 0 && _patientData$consent16.isSigned) && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSignConsent,\n                  disabled: isSubmitting,\n                  className: \"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                  children: isSubmitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 556,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 557,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 555,\n                      columnNumber: 27\n                    }, this), \"Processing...\"]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(FaSignature, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 563,\n                      columnNumber: 27\n                    }, this), \"Sign Consent Form\"]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 317,\n    columnNumber: 5\n  }, this);\n};\n_s(Consent, \"6NKb8sMusLnzHNcPZ2Lyl9owFGU=\", false, function () {\n  return [useParams, useAuth];\n});\n_c = Consent;\nexport default Consent;\nvar _c;\n$RefreshReg$(_c, \"Consent\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "useParams", "motion", "FaSignature", "FaFileAlt", "FaCheck", "FaTimes", "FaPen", "FaFont", "FaUpload", "axios", "<PERSON><PERSON><PERSON>", "Sidebar", "PatientNav", "useAuth", "Loader", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Consent", "_s", "_patientData$consent6", "_patientData$consent7", "_patientData$consent8", "_patientData$consent9", "_patientData$consent10", "_patientData$consent11", "_patientData$consent12", "_patientData$consent13", "_patientData$consent14", "_patientData$consent15", "_patientData$consent16", "nationalId", "user", "token", "sidebarOpen", "setSidebarOpen", "patientData", "setPatientData", "loading", "setLoading", "error", "setError", "success", "setSuccess", "signatureMethod", "setSignatureMethod", "signatureText", "setSignatureText", "signatureImage", "setSignatureImage", "uploadedSignature", "setUploadedSignature", "isSubmitting", "setIsSubmitting", "canvasRef", "fileInputRef", "isDrawing", "setIsDrawing", "canvasContext", "setCanvasContext", "fetchPatientData", "response", "get", "headers", "Authorization", "patientWithConsent", "data", "consent", "isSigned", "startsWith", "err", "_err$response", "_err$response2", "_err$response2$data", "console", "message", "initializeCanvas", "current", "canvas", "ctx", "getContext", "lineWidth", "lineCap", "lineJoin", "strokeStyle", "clearRect", "width", "height", "fillStyle", "fillRect", "img", "Image", "onload", "drawImage", "src", "timer", "setTimeout", "handleResize", "window", "addEventListener", "clearTimeout", "removeEventListener", "getMousePos", "evt", "rect", "getBoundingClientRect", "scaleX", "scaleY", "x", "clientX", "left", "y", "clientY", "top", "startDrawing", "e", "_patientData$consent", "pos", "beginPath", "moveTo", "draw", "_patientData$consent2", "lineTo", "stroke", "stopDrawing", "closePath", "dataUrl", "toDataURL", "clearCanvas", "_patientData$consent3", "handleSignatureMethodChange", "method", "_patientData$consent4", "handleFileUpload", "event", "file", "target", "files", "type", "size", "reader", "FileReader", "result", "readAsDataURL", "handleSignConsent", "_patientData$consent5", "trim", "put", "_err$response3", "_err$response4", "_err$response4$data", "className", "children", "isOpen", "setIsOpen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toggleSidebar", "div", "initial", "opacity", "animate", "transition", "duration", "fullName", "Date", "signedAt", "toLocaleDateString", "onClick", "value", "onChange", "disabled", "placeholder", "ref", "onMouseDown", "onMouseMove", "onMouseUp", "onMouseLeave", "onTouchStart", "preventDefault", "touches", "length", "touch", "onTouchMove", "onTouchEnd", "xmlns", "fill", "viewBox", "cx", "cy", "r", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/student/Consent.jsx"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { FaSignature, FaFileAlt, FaCheck, FaTimes, FaPen, FaFont, FaUpload } from 'react-icons/fa';\nimport axios from 'axios';\nimport Navbar from './Navbar';\nimport Sidebar from './Sidebar';\nimport PatientNav from './PatientNav';\nimport { useAuth } from '../context/AuthContext';\nimport Loader from '../components/Loader';\n\nconst Consent = () => {\n  const { nationalId } = useParams();\n  const { user, token } = useAuth();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [patientData, setPatientData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [signatureMethod, setSignatureMethod] = useState('text'); // 'text', 'draw', or 'upload'\n  const [signatureText, setSignatureText] = useState('');\n  const [signatureImage, setSignatureImage] = useState('');\n  const [uploadedSignature, setUploadedSignature] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // Canvas refs and state\n  const canvasRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const [isDrawing, setIsDrawing] = useState(false);\n  const [canvasContext, setCanvasContext] = useState(null);\n\n  useEffect(() => {\n    const fetchPatientData = async () => {\n      try {\n        const response = await axios.get(`http://localhost:5000/api/patients/public/${nationalId}`, {\n          headers: { Authorization: `Bearer ${token}` },\n        });\n\n        // Ensure the patient data has a consent field, even if it's empty\n        const patientWithConsent = {\n          ...response.data,\n          consent: response.data.consent || { isSigned: false }\n        };\n\n        setPatientData(patientWithConsent);\n\n        // If patient already has a signed consent, populate the signature fields\n        if (patientWithConsent.consent && patientWithConsent.consent.isSigned) {\n          if (patientWithConsent.consent.signatureText) {\n            setSignatureText(patientWithConsent.consent.signatureText);\n            setSignatureMethod('text');\n          } else if (patientWithConsent.consent.signatureImage) {\n            // Check if it's a drawn signature or uploaded image\n            if (patientWithConsent.consent.signatureImage.startsWith('data:image')) {\n              setSignatureImage(patientWithConsent.consent.signatureImage);\n              setSignatureMethod('draw');\n            } else {\n              setUploadedSignature(patientWithConsent.consent.signatureImage);\n              setSignatureMethod('upload');\n            }\n          }\n        }\n      } catch (err) {\n        console.error('Error fetching patient data:', err.response?.data || err.message);\n        setError(err.response?.data?.message || 'Failed to load patient data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (token && nationalId) {\n      fetchPatientData();\n    }\n  }, [nationalId, token]);\n\n  // Initialize canvas when component mounts\n  useEffect(() => {\n    // This effect needs to run after the canvas is rendered in the DOM\n    const initializeCanvas = () => {\n      if (canvasRef.current) {\n        const canvas = canvasRef.current;\n        const ctx = canvas.getContext('2d');\n        if (ctx) {\n          // Set canvas properties\n          ctx.lineWidth = 2;\n          ctx.lineCap = 'round';\n          ctx.lineJoin = 'round';\n          ctx.strokeStyle = 'black';\n\n          // Clear the canvas first\n          ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n          // Set canvas background to white for better visibility\n          ctx.fillStyle = 'white';\n          ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n          setCanvasContext(ctx);\n\n          // If there's a signature image and we're in draw mode, draw it on the canvas\n          if (signatureImage && signatureMethod === 'draw') {\n            const img = new Image();\n            img.onload = () => {\n              // Clear canvas before drawing\n              ctx.clearRect(0, 0, canvas.width, canvas.height);\n              ctx.fillStyle = 'white';\n              ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n              // Draw the image centered and scaled appropriately\n              ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n            };\n            img.src = signatureImage;\n          }\n        }\n      }\n    };\n\n    // Initialize immediately and also after a short delay to ensure DOM is ready\n    initializeCanvas();\n    const timer = setTimeout(initializeCanvas, 100);\n\n    // Also reinitialize when the window is resized\n    const handleResize = () => {\n      initializeCanvas();\n    };\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      clearTimeout(timer);\n      window.removeEventListener('resize', handleResize);\n    };\n  }, [signatureMethod, signatureImage]);\n\n  // Drawing functions\n  const getMousePos = (canvas, evt) => {\n    const rect = canvas.getBoundingClientRect();\n    const scaleX = canvas.width / rect.width;\n    const scaleY = canvas.height / rect.height;\n\n    return {\n      x: (evt.clientX - rect.left) * scaleX,\n      y: (evt.clientY - rect.top) * scaleY\n    };\n  };\n\n  const startDrawing = (e) => {\n    if (patientData?.consent?.isSigned || !canvasRef.current) return; // Prevent drawing if already signed or canvas not ready\n\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    const pos = getMousePos(canvas, e);\n\n    // Set drawing style\n    ctx.lineWidth = 2;\n    ctx.lineCap = 'round';\n    ctx.lineJoin = 'round';\n    ctx.strokeStyle = 'black';\n\n    ctx.beginPath();\n    ctx.moveTo(pos.x, pos.y);\n    setCanvasContext(ctx);\n    setIsDrawing(true);\n  };\n\n  const draw = (e) => {\n    if (!isDrawing || patientData?.consent?.isSigned || !canvasRef.current) return;\n\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    const pos = getMousePos(canvas, e);\n\n    ctx.lineTo(pos.x, pos.y);\n    ctx.stroke();\n  };\n\n  const stopDrawing = () => {\n    if (isDrawing && canvasRef.current) {\n      const ctx = canvasRef.current.getContext('2d');\n      if (ctx) {\n        ctx.closePath();\n      }\n      setIsDrawing(false);\n\n      // Save the canvas content as an image\n      const dataUrl = canvasRef.current.toDataURL('image/png');\n      setSignatureImage(dataUrl);\n    }\n  };\n\n  const clearCanvas = () => {\n    if (patientData?.consent?.isSigned || !canvasRef.current) return; // Prevent clearing if already signed\n\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Clear the canvas\n    ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n    // Reset to white background\n    ctx.fillStyle = 'white';\n    ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n    // Reset drawing state\n    setIsDrawing(false);\n    setSignatureImage('');\n\n    // Reset context properties\n    ctx.lineWidth = 2;\n    ctx.lineCap = 'round';\n    ctx.lineJoin = 'round';\n    ctx.strokeStyle = 'black';\n    setCanvasContext(ctx);\n  };\n\n  const handleSignatureMethodChange = (method) => {\n    if (patientData?.consent?.isSigned) return; // Prevent changing if already signed\n    setSignatureMethod(method);\n  };\n\n  const handleFileUpload = (event) => {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    // Validate file type\n    if (!file.type.startsWith('image/')) {\n      setError('Please select a valid image file.');\n      return;\n    }\n\n    // Validate file size (max 5MB)\n    if (file.size > 5 * 1024 * 1024) {\n      setError('File size must be less than 5MB.');\n      return;\n    }\n\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      setUploadedSignature(e.target.result);\n      setError(''); // Clear any previous errors\n    };\n    reader.readAsDataURL(file);\n  };\n\n  const handleSignConsent = async () => {\n    if (patientData?.consent?.isSigned) {\n      setError('This consent form has already been signed.');\n      return;\n    }\n\n    // Validate signature\n    if (signatureMethod === 'text' && !signatureText.trim()) {\n      setError('Please enter your signature in the text field.');\n      return;\n    }\n\n    if (signatureMethod === 'draw' && !signatureImage) {\n      setError('Please draw your signature on the canvas.');\n      return;\n    }\n\n    if (signatureMethod === 'upload' && !uploadedSignature) {\n      setError('Please upload a signature image.');\n      return;\n    }\n\n    setIsSubmitting(true);\n    setError('');\n\n    try {\n      const response = await axios.put(\n        `http://localhost:5000/api/patients/${nationalId}/consent`,\n        {\n          signatureText: signatureMethod === 'text' ? signatureText : '',\n          signatureImage: signatureMethod === 'draw' ? signatureImage : signatureMethod === 'upload' ? uploadedSignature : ''\n        },\n        {\n          headers: { Authorization: `Bearer ${token}` }\n        }\n      );\n\n      // Update the patient data with the new consent information\n      setPatientData({\n        ...patientData,\n        consent: response.data.consent\n      });\n\n      setSuccess('Consent form signed successfully!');\n      setTimeout(() => setSuccess(''), 3000);\n    } catch (err) {\n      console.error('Error signing consent:', err.response?.data || err.message);\n      setError(err.response?.data?.message || 'Failed to sign consent form');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex h-screen bg-gray-50\">\n        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\n          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\n          <PatientNav />\n          <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\">\n            <Loader />\n          </main>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\n        <PatientNav />\n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\">\n          <div className=\"max-w-4xl mx-auto\">\n            {error && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                className=\"mb-6 p-4 bg-red-100 text-[#333333] rounded-lg\"\n              >\n                {error}\n              </motion.div>\n            )}\n\n            {success && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                className=\"mb-6 p-4 bg-[#28A745]/20 text-[#28A745] rounded-lg\"\n              >\n                {success}\n              </motion.div>\n            )}\n\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5 }}\n              className=\"bg-white rounded-xl shadow-lg overflow-hidden\"\n            >\n              <div className=\"p-6 border-b border-gray-100\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h1 className=\"text-2xl font-bold text-[#0077B6]\">\n                      Dental Treatment Consent Form\n                    </h1>\n                    <p className=\"text-gray-600 mt-1\">\n                      {patientData?.consent?.isSigned\n                        ? 'This consent form has been signed and cannot be modified.'\n                        : 'Please read carefully and sign to provide your consent for dental treatment.'}\n                    </p>\n                  </div>\n                  <div className={`p-3 rounded-full ${patientData?.consent?.isSigned ? 'bg-[#28A745]/20' : 'bg-[#0077B6]/10'}`}>\n                    {patientData?.consent?.isSigned ? (\n                      <FaCheck className=\"h-6 w-6 text-[#28A745]\" />\n                    ) : (\n                      <FaSignature className=\"h-6 w-6 text-[#0077B6]\" />\n                    )}\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"p-6\">\n                <div className=\"prose max-w-none\">\n                  <h3 className=\"text-lg font-semibold mb-4\">General Dental Treatment Consent</h3>\n\n                  <p className=\"mb-4\">\n                    I, {patientData?.fullName || '[Patient Name]'}, hereby authorize the dental team to perform the following dental treatment or oral surgery procedure(s):\n                    examination, radiographs, prophylaxis (cleaning), fluoride treatment, restorations (fillings), crowns, bridges, extractions, root canal therapy, periodontal therapy, and other procedures deemed necessary.\n                  </p>\n\n                  <p className=\"mb-4\">\n                    I understand that there are risks associated with dental treatment including but not limited to:\n                  </p>\n\n                  <ul className=\"list-disc pl-5 mb-4\">\n                    <li>Sensitivity or pain in treated or adjacent teeth</li>\n                    <li>Infection requiring additional treatment</li>\n                    <li>Fracture of tooth/teeth or dental restorations</li>\n                    <li>Swelling, bleeding, or discomfort following procedures</li>\n                    <li>Reactions to medications, anesthetics, or materials used</li>\n                    <li>Changes in occlusion (bite) requiring adjustment</li>\n                  </ul>\n\n                  <p className=\"mb-4\">\n                    I understand that during treatment, unforeseen conditions may arise which may necessitate procedures different from those planned. I consent to those additional procedures that are necessary in the professional judgment of my treating dentist.\n                  </p>\n\n                  <p className=\"mb-4\">\n                    I understand that I may ask questions about the planned procedures and that I have the right to be informed of alternative treatments. I have disclosed my complete medical history, including medications and allergies.\n                  </p>\n\n                  <p className=\"mb-4\">\n                    I consent to the administration of local anesthesia, antibiotics, analgesics, or any other medication necessary for dental treatment. I understand the risks involved in the administration of these medications.\n                  </p>\n\n                  <p className=\"mb-4\">\n                    I consent to the making of photographs, video recordings, and x-rays before, during, and after treatment, and to their use for scientific, educational, or research purposes.\n                  </p>\n\n                  <p className=\"mb-6\">\n                    By signing below, I acknowledge that I have read and understand this consent form, had the opportunity to ask questions, and give my consent for dental treatment.\n                  </p>\n\n                  {/* Signature Section */}\n                  <div className=\"border-t border-gray-200 pt-6\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <h4 className=\"text-lg font-semibold\">Patient Signature</h4>\n                      {patientData?.consent?.isSigned && (\n                        <div className=\"text-sm text-green-600 font-medium flex items-center\">\n                          <FaCheck className=\"mr-1\" /> Signed on {new Date(patientData.consent.signedAt).toLocaleDateString()}\n                        </div>\n                      )}\n                    </div>\n\n                    {!patientData?.consent?.isSigned && (\n                      <div className=\"mb-6\">\n                        <div className=\"flex space-x-4 mb-4\">\n                          <button\n                            onClick={() => handleSignatureMethodChange('text')}\n                            className={`flex items-center px-4 py-2 rounded-lg ${\n                              signatureMethod === 'text'\n                                ? 'bg-[#0077B6]/10 text-[#0077B6] border border-[#0077B6]/30'\n                                : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'\n                            }`}\n                          >\n                            <FaFont className=\"mr-2\" /> Text Signature\n                          </button>\n                          <button\n                            onClick={() => handleSignatureMethodChange('draw')}\n                            className={`flex items-center px-4 py-2 rounded-lg ${\n                              signatureMethod === 'draw'\n                                ? 'bg-[#20B2AA]/10 text-[#20B2AA] border border-[#20B2AA]/30'\n                                : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'\n                            }`}\n                          >\n                            <FaPen className=\"mr-2\" /> Draw Signature\n                          </button>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Text Signature */}\n                    {signatureMethod === 'text' && (\n                      <div className=\"mb-6\">\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                          Type your full name as your signature:\n                        </label>\n                        <input\n                          type=\"text\"\n                          value={signatureText}\n                          onChange={(e) => setSignatureText(e.target.value)}\n                          disabled={patientData?.consent?.isSigned}\n                          placeholder=\"Type your full name here\"\n                          className={`w-full px-4 py-2 border ${\n                            patientData?.consent?.isSigned\n                              ? 'bg-gray-100 border-gray-300'\n                              : 'border-gray-300 focus:border-[#0077B6] focus:ring-1 focus:ring-[#0077B6]'\n                          } rounded-lg`}\n                        />\n                      </div>\n                    )}\n\n                    {/* Drawing Signature */}\n                    {signatureMethod === 'draw' && (\n                      <div className=\"mb-6\">\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                          Draw your signature below:\n                        </label>\n                        <div className=\"border border-gray-300 rounded-lg overflow-hidden bg-white\">\n                          <canvas\n                            ref={canvasRef}\n                            width={600}\n                            height={150}\n                            onMouseDown={startDrawing}\n                            onMouseMove={draw}\n                            onMouseUp={stopDrawing}\n                            onMouseLeave={stopDrawing}\n                            onTouchStart={(e) => {\n                              e.preventDefault(); // Prevent scrolling when drawing\n                              if (e.touches && e.touches.length > 0) {\n                                const touch = e.touches[0];\n                                startDrawing({\n                                  clientX: touch.clientX,\n                                  clientY: touch.clientY\n                                });\n                              }\n                            }}\n                            onTouchMove={(e) => {\n                              e.preventDefault(); // Prevent scrolling when drawing\n                              if (e.touches && e.touches.length > 0) {\n                                const touch = e.touches[0];\n                                draw({\n                                  clientX: touch.clientX,\n                                  clientY: touch.clientY\n                                });\n                              }\n                            }}\n                            onTouchEnd={stopDrawing}\n                            className={`w-full ${patientData?.consent?.isSigned ? 'cursor-not-allowed' : 'cursor-crosshair'}`}\n                          />\n                        </div>\n                        {!patientData?.consent?.isSigned && (\n                          <button\n                            onClick={clearCanvas}\n                            className=\"mt-2 px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300\"\n                          >\n                            Clear\n                          </button>\n                        )}\n                      </div>\n                    )}\n\n                    {/* Patient Info and Date */}\n                    <div className=\"flex flex-col md:flex-row justify-between mt-6\">\n                      <div className=\"mb-4 md:mb-0\">\n                        <p className=\"text-sm font-medium text-gray-700\">Patient Name:</p>\n                        <p className=\"text-lg font-semibold text-gray-900\">\n                          {patientData?.fullName || 'Patient Name'}\n                        </p>\n                      </div>\n                      <div>\n                        <p className=\"text-sm font-medium text-gray-700\">Date:</p>\n                        <p className=\"text-lg font-semibold text-gray-900\">\n                          {patientData?.consent?.signedAt\n                            ? new Date(patientData.consent.signedAt).toLocaleDateString()\n                            : new Date().toLocaleDateString()}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Footer with action buttons */}\n              <div className=\"p-6 border-t border-gray-100 bg-gray-50\">\n                <div className=\"flex justify-end\">\n                  {!patientData?.consent?.isSigned && (\n                    <button\n                      onClick={handleSignConsent}\n                      disabled={isSubmitting}\n                      className=\"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n                    >\n                      {isSubmitting ? (\n                        <>\n                          <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                          </svg>\n                          Processing...\n                        </>\n                      ) : (\n                        <>\n                          <FaSignature className=\"h-4 w-4\" />\n                          Sign Consent Form\n                        </>\n                      )}\n                    </button>\n                  )}\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default Consent;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,gBAAgB;AAClG,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1C,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACpB,MAAM;IAAEC;EAAW,CAAC,GAAGhC,SAAS,CAAC,CAAC;EAClC,MAAM;IAAEiC,IAAI;IAAEC;EAAM,CAAC,GAAGrB,OAAO,CAAC,CAAC;EACjC,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4C,KAAK,EAAEC,QAAQ,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAChE,MAAM,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoD,cAAc,EAAEC,iBAAiB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACsD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM0D,SAAS,GAAGxD,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMyD,YAAY,GAAGzD,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAAC0D,SAAS,EAAEC,YAAY,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8D,aAAa,EAAEC,gBAAgB,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAExDC,SAAS,CAAC,MAAM;IACd,MAAM+D,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMrD,KAAK,CAACsD,GAAG,CAAC,6CAA6C/B,UAAU,EAAE,EAAE;UAC1FgC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAU/B,KAAK;UAAG;QAC9C,CAAC,CAAC;;QAEF;QACA,MAAMgC,kBAAkB,GAAG;UACzB,GAAGJ,QAAQ,CAACK,IAAI;UAChBC,OAAO,EAAEN,QAAQ,CAACK,IAAI,CAACC,OAAO,IAAI;YAAEC,QAAQ,EAAE;UAAM;QACtD,CAAC;QAED/B,cAAc,CAAC4B,kBAAkB,CAAC;;QAElC;QACA,IAAIA,kBAAkB,CAACE,OAAO,IAAIF,kBAAkB,CAACE,OAAO,CAACC,QAAQ,EAAE;UACrE,IAAIH,kBAAkB,CAACE,OAAO,CAACrB,aAAa,EAAE;YAC5CC,gBAAgB,CAACkB,kBAAkB,CAACE,OAAO,CAACrB,aAAa,CAAC;YAC1DD,kBAAkB,CAAC,MAAM,CAAC;UAC5B,CAAC,MAAM,IAAIoB,kBAAkB,CAACE,OAAO,CAACnB,cAAc,EAAE;YACpD;YACA,IAAIiB,kBAAkB,CAACE,OAAO,CAACnB,cAAc,CAACqB,UAAU,CAAC,YAAY,CAAC,EAAE;cACtEpB,iBAAiB,CAACgB,kBAAkB,CAACE,OAAO,CAACnB,cAAc,CAAC;cAC5DH,kBAAkB,CAAC,MAAM,CAAC;YAC5B,CAAC,MAAM;cACLM,oBAAoB,CAACc,kBAAkB,CAACE,OAAO,CAACnB,cAAc,CAAC;cAC/DH,kBAAkB,CAAC,QAAQ,CAAC;YAC9B;UACF;QACF;MACF,CAAC,CAAC,OAAOyB,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZC,OAAO,CAAClC,KAAK,CAAC,8BAA8B,EAAE,EAAA+B,aAAA,GAAAD,GAAG,CAACT,QAAQ,cAAAU,aAAA,uBAAZA,aAAA,CAAcL,IAAI,KAAII,GAAG,CAACK,OAAO,CAAC;QAChFlC,QAAQ,CAAC,EAAA+B,cAAA,GAAAF,GAAG,CAACT,QAAQ,cAAAW,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcN,IAAI,cAAAO,mBAAA,uBAAlBA,mBAAA,CAAoBE,OAAO,KAAI,6BAA6B,CAAC;MACxE,CAAC,SAAS;QACRpC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIN,KAAK,IAAIF,UAAU,EAAE;MACvB6B,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAC7B,UAAU,EAAEE,KAAK,CAAC,CAAC;;EAEvB;EACApC,SAAS,CAAC,MAAM;IACd;IACA,MAAM+E,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,IAAItB,SAAS,CAACuB,OAAO,EAAE;QACrB,MAAMC,MAAM,GAAGxB,SAAS,CAACuB,OAAO;QAChC,MAAME,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;QACnC,IAAID,GAAG,EAAE;UACP;UACAA,GAAG,CAACE,SAAS,GAAG,CAAC;UACjBF,GAAG,CAACG,OAAO,GAAG,OAAO;UACrBH,GAAG,CAACI,QAAQ,GAAG,OAAO;UACtBJ,GAAG,CAACK,WAAW,GAAG,OAAO;;UAEzB;UACAL,GAAG,CAACM,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEP,MAAM,CAACQ,KAAK,EAAER,MAAM,CAACS,MAAM,CAAC;;UAEhD;UACAR,GAAG,CAACS,SAAS,GAAG,OAAO;UACvBT,GAAG,CAACU,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEX,MAAM,CAACQ,KAAK,EAAER,MAAM,CAACS,MAAM,CAAC;UAE/C5B,gBAAgB,CAACoB,GAAG,CAAC;;UAErB;UACA,IAAI/B,cAAc,IAAIJ,eAAe,KAAK,MAAM,EAAE;YAChD,MAAM8C,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;YACvBD,GAAG,CAACE,MAAM,GAAG,MAAM;cACjB;cACAb,GAAG,CAACM,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEP,MAAM,CAACQ,KAAK,EAAER,MAAM,CAACS,MAAM,CAAC;cAChDR,GAAG,CAACS,SAAS,GAAG,OAAO;cACvBT,GAAG,CAACU,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEX,MAAM,CAACQ,KAAK,EAAER,MAAM,CAACS,MAAM,CAAC;;cAE/C;cACAR,GAAG,CAACc,SAAS,CAACH,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEZ,MAAM,CAACQ,KAAK,EAAER,MAAM,CAACS,MAAM,CAAC;YACvD,CAAC;YACDG,GAAG,CAACI,GAAG,GAAG9C,cAAc;UAC1B;QACF;MACF;IACF,CAAC;;IAED;IACA4B,gBAAgB,CAAC,CAAC;IAClB,MAAMmB,KAAK,GAAGC,UAAU,CAACpB,gBAAgB,EAAE,GAAG,CAAC;;IAE/C;IACA,MAAMqB,YAAY,GAAGA,CAAA,KAAM;MACzBrB,gBAAgB,CAAC,CAAC;IACpB,CAAC;IACDsB,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAE/C,OAAO,MAAM;MACXG,YAAY,CAACL,KAAK,CAAC;MACnBG,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,CAACrD,eAAe,EAAEI,cAAc,CAAC,CAAC;;EAErC;EACA,MAAMsD,WAAW,GAAGA,CAACxB,MAAM,EAAEyB,GAAG,KAAK;IACnC,MAAMC,IAAI,GAAG1B,MAAM,CAAC2B,qBAAqB,CAAC,CAAC;IAC3C,MAAMC,MAAM,GAAG5B,MAAM,CAACQ,KAAK,GAAGkB,IAAI,CAAClB,KAAK;IACxC,MAAMqB,MAAM,GAAG7B,MAAM,CAACS,MAAM,GAAGiB,IAAI,CAACjB,MAAM;IAE1C,OAAO;MACLqB,CAAC,EAAE,CAACL,GAAG,CAACM,OAAO,GAAGL,IAAI,CAACM,IAAI,IAAIJ,MAAM;MACrCK,CAAC,EAAE,CAACR,GAAG,CAACS,OAAO,GAAGR,IAAI,CAACS,GAAG,IAAIN;IAChC,CAAC;EACH,CAAC;EAED,MAAMO,YAAY,GAAIC,CAAC,IAAK;IAAA,IAAAC,oBAAA;IAC1B,IAAIhF,WAAW,aAAXA,WAAW,gBAAAgF,oBAAA,GAAXhF,WAAW,CAAE+B,OAAO,cAAAiD,oBAAA,eAApBA,oBAAA,CAAsBhD,QAAQ,IAAI,CAACd,SAAS,CAACuB,OAAO,EAAE,OAAO,CAAC;;IAElE,MAAMC,MAAM,GAAGxB,SAAS,CAACuB,OAAO;IAChC,MAAME,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IACnC,IAAI,CAACD,GAAG,EAAE;IAEV,MAAMsC,GAAG,GAAGf,WAAW,CAACxB,MAAM,EAAEqC,CAAC,CAAC;;IAElC;IACApC,GAAG,CAACE,SAAS,GAAG,CAAC;IACjBF,GAAG,CAACG,OAAO,GAAG,OAAO;IACrBH,GAAG,CAACI,QAAQ,GAAG,OAAO;IACtBJ,GAAG,CAACK,WAAW,GAAG,OAAO;IAEzBL,GAAG,CAACuC,SAAS,CAAC,CAAC;IACfvC,GAAG,CAACwC,MAAM,CAACF,GAAG,CAACT,CAAC,EAAES,GAAG,CAACN,CAAC,CAAC;IACxBpD,gBAAgB,CAACoB,GAAG,CAAC;IACrBtB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAM+D,IAAI,GAAIL,CAAC,IAAK;IAAA,IAAAM,qBAAA;IAClB,IAAI,CAACjE,SAAS,IAAIpB,WAAW,aAAXA,WAAW,gBAAAqF,qBAAA,GAAXrF,WAAW,CAAE+B,OAAO,cAAAsD,qBAAA,eAApBA,qBAAA,CAAsBrD,QAAQ,IAAI,CAACd,SAAS,CAACuB,OAAO,EAAE;IAExE,MAAMC,MAAM,GAAGxB,SAAS,CAACuB,OAAO;IAChC,MAAME,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IACnC,IAAI,CAACD,GAAG,EAAE;IAEV,MAAMsC,GAAG,GAAGf,WAAW,CAACxB,MAAM,EAAEqC,CAAC,CAAC;IAElCpC,GAAG,CAAC2C,MAAM,CAACL,GAAG,CAACT,CAAC,EAAES,GAAG,CAACN,CAAC,CAAC;IACxBhC,GAAG,CAAC4C,MAAM,CAAC,CAAC;EACd,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIpE,SAAS,IAAIF,SAAS,CAACuB,OAAO,EAAE;MAClC,MAAME,GAAG,GAAGzB,SAAS,CAACuB,OAAO,CAACG,UAAU,CAAC,IAAI,CAAC;MAC9C,IAAID,GAAG,EAAE;QACPA,GAAG,CAAC8C,SAAS,CAAC,CAAC;MACjB;MACApE,YAAY,CAAC,KAAK,CAAC;;MAEnB;MACA,MAAMqE,OAAO,GAAGxE,SAAS,CAACuB,OAAO,CAACkD,SAAS,CAAC,WAAW,CAAC;MACxD9E,iBAAiB,CAAC6E,OAAO,CAAC;IAC5B;EACF,CAAC;EAED,MAAME,WAAW,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IACxB,IAAI7F,WAAW,aAAXA,WAAW,gBAAA6F,qBAAA,GAAX7F,WAAW,CAAE+B,OAAO,cAAA8D,qBAAA,eAApBA,qBAAA,CAAsB7D,QAAQ,IAAI,CAACd,SAAS,CAACuB,OAAO,EAAE,OAAO,CAAC;;IAElE,MAAMC,MAAM,GAAGxB,SAAS,CAACuB,OAAO;IAChC,MAAME,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IACnC,IAAI,CAACD,GAAG,EAAE;;IAEV;IACAA,GAAG,CAACM,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEP,MAAM,CAACQ,KAAK,EAAER,MAAM,CAACS,MAAM,CAAC;;IAEhD;IACAR,GAAG,CAACS,SAAS,GAAG,OAAO;IACvBT,GAAG,CAACU,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEX,MAAM,CAACQ,KAAK,EAAER,MAAM,CAACS,MAAM,CAAC;;IAE/C;IACA9B,YAAY,CAAC,KAAK,CAAC;IACnBR,iBAAiB,CAAC,EAAE,CAAC;;IAErB;IACA8B,GAAG,CAACE,SAAS,GAAG,CAAC;IACjBF,GAAG,CAACG,OAAO,GAAG,OAAO;IACrBH,GAAG,CAACI,QAAQ,GAAG,OAAO;IACtBJ,GAAG,CAACK,WAAW,GAAG,OAAO;IACzBzB,gBAAgB,CAACoB,GAAG,CAAC;EACvB,CAAC;EAED,MAAMmD,2BAA2B,GAAIC,MAAM,IAAK;IAAA,IAAAC,qBAAA;IAC9C,IAAIhG,WAAW,aAAXA,WAAW,gBAAAgG,qBAAA,GAAXhG,WAAW,CAAE+B,OAAO,cAAAiE,qBAAA,eAApBA,qBAAA,CAAsBhE,QAAQ,EAAE,OAAO,CAAC;IAC5CvB,kBAAkB,CAACsF,MAAM,CAAC;EAC5B,CAAC;EAED,MAAME,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACF,IAAI,EAAE;;IAEX;IACA,IAAI,CAACA,IAAI,CAACG,IAAI,CAACrE,UAAU,CAAC,QAAQ,CAAC,EAAE;MACnC5B,QAAQ,CAAC,mCAAmC,CAAC;MAC7C;IACF;;IAEA;IACA,IAAI8F,IAAI,CAACI,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;MAC/BlG,QAAQ,CAAC,kCAAkC,CAAC;MAC5C;IACF;IAEA,MAAMmG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAAChD,MAAM,GAAIuB,CAAC,IAAK;MACrBhE,oBAAoB,CAACgE,CAAC,CAACqB,MAAM,CAACM,MAAM,CAAC;MACrCrG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC;IACDmG,MAAM,CAACG,aAAa,CAACR,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMS,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAAA,IAAAC,qBAAA;IACpC,IAAI7G,WAAW,aAAXA,WAAW,gBAAA6G,qBAAA,GAAX7G,WAAW,CAAE+B,OAAO,cAAA8E,qBAAA,eAApBA,qBAAA,CAAsB7E,QAAQ,EAAE;MAClC3B,QAAQ,CAAC,4CAA4C,CAAC;MACtD;IACF;;IAEA;IACA,IAAIG,eAAe,KAAK,MAAM,IAAI,CAACE,aAAa,CAACoG,IAAI,CAAC,CAAC,EAAE;MACvDzG,QAAQ,CAAC,gDAAgD,CAAC;MAC1D;IACF;IAEA,IAAIG,eAAe,KAAK,MAAM,IAAI,CAACI,cAAc,EAAE;MACjDP,QAAQ,CAAC,2CAA2C,CAAC;MACrD;IACF;IAEA,IAAIG,eAAe,KAAK,QAAQ,IAAI,CAACM,iBAAiB,EAAE;MACtDT,QAAQ,CAAC,kCAAkC,CAAC;MAC5C;IACF;IAEAY,eAAe,CAAC,IAAI,CAAC;IACrBZ,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMoB,QAAQ,GAAG,MAAMrD,KAAK,CAAC2I,GAAG,CAC9B,sCAAsCpH,UAAU,UAAU,EAC1D;QACEe,aAAa,EAAEF,eAAe,KAAK,MAAM,GAAGE,aAAa,GAAG,EAAE;QAC9DE,cAAc,EAAEJ,eAAe,KAAK,MAAM,GAAGI,cAAc,GAAGJ,eAAe,KAAK,QAAQ,GAAGM,iBAAiB,GAAG;MACnH,CAAC,EACD;QACEa,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAU/B,KAAK;QAAG;MAC9C,CACF,CAAC;;MAED;MACAI,cAAc,CAAC;QACb,GAAGD,WAAW;QACd+B,OAAO,EAAEN,QAAQ,CAACK,IAAI,CAACC;MACzB,CAAC,CAAC;MAEFxB,UAAU,CAAC,mCAAmC,CAAC;MAC/CqD,UAAU,CAAC,MAAMrD,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,CAAC,OAAO2B,GAAG,EAAE;MAAA,IAAA8E,cAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZ5E,OAAO,CAAClC,KAAK,CAAC,wBAAwB,EAAE,EAAA4G,cAAA,GAAA9E,GAAG,CAACT,QAAQ,cAAAuF,cAAA,uBAAZA,cAAA,CAAclF,IAAI,KAAII,GAAG,CAACK,OAAO,CAAC;MAC1ElC,QAAQ,CAAC,EAAA4G,cAAA,GAAA/E,GAAG,CAACT,QAAQ,cAAAwF,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcnF,IAAI,cAAAoF,mBAAA,uBAAlBA,mBAAA,CAAoB3E,OAAO,KAAI,6BAA6B,CAAC;IACxE,CAAC,SAAS;MACRtB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,IAAIf,OAAO,EAAE;IACX,oBACEvB,OAAA;MAAKwI,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvCzI,OAAA,CAACL,OAAO;QAAC+I,MAAM,EAAEvH,WAAY;QAACwH,SAAS,EAAEvH;MAAe;QAAAwH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3D/I,OAAA;QAAKwI,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnDzI,OAAA,CAACN,MAAM;UAACsJ,aAAa,EAAEA,CAAA,KAAM5H,cAAc,CAAC,CAACD,WAAW;QAAE;UAAAyH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7D/I,OAAA,CAACJ,UAAU;UAAAgJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACd/I,OAAA;UAAMwI,SAAS,EAAC,+EAA+E;UAAAC,QAAA,eAC7FzI,OAAA,CAACF,MAAM;YAAA8I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/I,OAAA;IAAKwI,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvCzI,OAAA,CAACL,OAAO;MAAC+I,MAAM,EAAEvH,WAAY;MAACwH,SAAS,EAAEvH;IAAe;MAAAwH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC3D/I,OAAA;MAAKwI,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDzI,OAAA,CAACN,MAAM;QAACsJ,aAAa,EAAEA,CAAA,KAAM5H,cAAc,CAAC,CAACD,WAAW;MAAE;QAAAyH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7D/I,OAAA,CAACJ,UAAU;QAAAgJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACd/I,OAAA;QAAMwI,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAC7FzI,OAAA;UAAKwI,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAC/BhH,KAAK,iBACJzB,OAAA,CAACf,MAAM,CAACgK,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBC,OAAO,EAAE;cAAED,OAAO,EAAE;YAAE,CAAE;YACxBX,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAExDhH;UAAK;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,EAEApH,OAAO,iBACN3B,OAAA,CAACf,MAAM,CAACgK,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBC,OAAO,EAAE;cAAED,OAAO,EAAE;YAAE,CAAE;YACxBX,SAAS,EAAC,oDAAoD;YAAAC,QAAA,EAE7D9G;UAAO;YAAAiH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACb,eAED/I,OAAA,CAACf,MAAM,CAACgK,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBC,OAAO,EAAE;cAAED,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9Bd,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAEzDzI,OAAA;cAAKwI,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3CzI,OAAA;gBAAKwI,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDzI,OAAA;kBAAAyI,QAAA,gBACEzI,OAAA;oBAAIwI,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAElD;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL/I,OAAA;oBAAGwI,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAC9BpH,WAAW,aAAXA,WAAW,gBAAAhB,qBAAA,GAAXgB,WAAW,CAAE+B,OAAO,cAAA/C,qBAAA,eAApBA,qBAAA,CAAsBgD,QAAQ,GAC3B,2DAA2D,GAC3D;kBAA8E;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN/I,OAAA;kBAAKwI,SAAS,EAAE,oBAAoBnH,WAAW,aAAXA,WAAW,gBAAAf,qBAAA,GAAXe,WAAW,CAAE+B,OAAO,cAAA9C,qBAAA,eAApBA,qBAAA,CAAsB+C,QAAQ,GAAG,iBAAiB,GAAG,iBAAiB,EAAG;kBAAAoF,QAAA,EAC1GpH,WAAW,aAAXA,WAAW,gBAAAd,qBAAA,GAAXc,WAAW,CAAE+B,OAAO,cAAA7C,qBAAA,eAApBA,qBAAA,CAAsB8C,QAAQ,gBAC7BrD,OAAA,CAACZ,OAAO;oBAACoJ,SAAS,EAAC;kBAAwB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAE9C/I,OAAA,CAACd,WAAW;oBAACsJ,SAAS,EAAC;kBAAwB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAClD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/I,OAAA;cAAKwI,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClBzI,OAAA;gBAAKwI,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BzI,OAAA;kBAAIwI,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAgC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAEhF/I,OAAA;kBAAGwI,SAAS,EAAC,MAAM;kBAAAC,QAAA,GAAC,KACf,EAAC,CAAApH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEkI,QAAQ,KAAI,gBAAgB,EAAC,yTAEhD;gBAAA;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAEJ/I,OAAA;kBAAGwI,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAEpB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAEJ/I,OAAA;kBAAIwI,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBACjCzI,OAAA;oBAAAyI,QAAA,EAAI;kBAAgD;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzD/I,OAAA;oBAAAyI,QAAA,EAAI;kBAAwC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjD/I,OAAA;oBAAAyI,QAAA,EAAI;kBAA8C;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvD/I,OAAA;oBAAAyI,QAAA,EAAI;kBAAsD;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/D/I,OAAA;oBAAAyI,QAAA,EAAI;kBAAwD;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjE/I,OAAA;oBAAAyI,QAAA,EAAI;kBAAgD;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eAEL/I,OAAA;kBAAGwI,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAEpB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAEJ/I,OAAA;kBAAGwI,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAEpB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAEJ/I,OAAA;kBAAGwI,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAEpB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAEJ/I,OAAA;kBAAGwI,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAEpB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAEJ/I,OAAA;kBAAGwI,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAEpB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAGJ/I,OAAA;kBAAKwI,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,gBAC5CzI,OAAA;oBAAKwI,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrDzI,OAAA;sBAAIwI,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAC3D,CAAA1H,WAAW,aAAXA,WAAW,wBAAAb,qBAAA,GAAXa,WAAW,CAAE+B,OAAO,cAAA5C,qBAAA,uBAApBA,qBAAA,CAAsB6C,QAAQ,kBAC7BrD,OAAA;sBAAKwI,SAAS,EAAC,sDAAsD;sBAAAC,QAAA,gBACnEzI,OAAA,CAACZ,OAAO;wBAACoJ,SAAS,EAAC;sBAAM;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAAW,EAAC,IAAIS,IAAI,CAACnI,WAAW,CAAC+B,OAAO,CAACqG,QAAQ,CAAC,CAACC,kBAAkB,CAAC,CAAC;oBAAA;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChG,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,EAEL,EAAC1H,WAAW,aAAXA,WAAW,gBAAAZ,sBAAA,GAAXY,WAAW,CAAE+B,OAAO,cAAA3C,sBAAA,eAApBA,sBAAA,CAAsB4C,QAAQ,kBAC9BrD,OAAA;oBAAKwI,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnBzI,OAAA;sBAAKwI,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,gBAClCzI,OAAA;wBACE2J,OAAO,EAAEA,CAAA,KAAMxC,2BAA2B,CAAC,MAAM,CAAE;wBACnDqB,SAAS,EAAE,0CACT3G,eAAe,KAAK,MAAM,GACtB,2DAA2D,GAC3D,oEAAoE,EACvE;wBAAA4G,QAAA,gBAEHzI,OAAA,CAACT,MAAM;0BAACiJ,SAAS,EAAC;wBAAM;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,mBAC7B;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACT/I,OAAA;wBACE2J,OAAO,EAAEA,CAAA,KAAMxC,2BAA2B,CAAC,MAAM,CAAE;wBACnDqB,SAAS,EAAE,0CACT3G,eAAe,KAAK,MAAM,GACtB,2DAA2D,GAC3D,oEAAoE,EACvE;wBAAA4G,QAAA,gBAEHzI,OAAA,CAACV,KAAK;0BAACkJ,SAAS,EAAC;wBAAM;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,mBAC5B;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EAGAlH,eAAe,KAAK,MAAM,iBACzB7B,OAAA;oBAAKwI,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBzI,OAAA;sBAAOwI,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAEhE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACR/I,OAAA;sBACE2H,IAAI,EAAC,MAAM;sBACXiC,KAAK,EAAE7H,aAAc;sBACrB8H,QAAQ,EAAGzD,CAAC,IAAKpE,gBAAgB,CAACoE,CAAC,CAACqB,MAAM,CAACmC,KAAK,CAAE;sBAClDE,QAAQ,EAAEzI,WAAW,aAAXA,WAAW,wBAAAX,sBAAA,GAAXW,WAAW,CAAE+B,OAAO,cAAA1C,sBAAA,uBAApBA,sBAAA,CAAsB2C,QAAS;sBACzC0G,WAAW,EAAC,0BAA0B;sBACtCvB,SAAS,EAAE,2BACTnH,WAAW,aAAXA,WAAW,gBAAAV,sBAAA,GAAXU,WAAW,CAAE+B,OAAO,cAAAzC,sBAAA,eAApBA,sBAAA,CAAsB0C,QAAQ,GAC1B,6BAA6B,GAC7B,0EAA0E;oBAClE;sBAAAuF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN,EAGAlH,eAAe,KAAK,MAAM,iBACzB7B,OAAA;oBAAKwI,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBzI,OAAA;sBAAOwI,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAEhE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACR/I,OAAA;sBAAKwI,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,eACzEzI,OAAA;wBACEgK,GAAG,EAAEzH,SAAU;wBACfgC,KAAK,EAAE,GAAI;wBACXC,MAAM,EAAE,GAAI;wBACZyF,WAAW,EAAE9D,YAAa;wBAC1B+D,WAAW,EAAEzD,IAAK;wBAClB0D,SAAS,EAAEtD,WAAY;wBACvBuD,YAAY,EAAEvD,WAAY;wBAC1BwD,YAAY,EAAGjE,CAAC,IAAK;0BACnBA,CAAC,CAACkE,cAAc,CAAC,CAAC,CAAC,CAAC;0BACpB,IAAIlE,CAAC,CAACmE,OAAO,IAAInE,CAAC,CAACmE,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;4BACrC,MAAMC,KAAK,GAAGrE,CAAC,CAACmE,OAAO,CAAC,CAAC,CAAC;4BAC1BpE,YAAY,CAAC;8BACXL,OAAO,EAAE2E,KAAK,CAAC3E,OAAO;8BACtBG,OAAO,EAAEwE,KAAK,CAACxE;4BACjB,CAAC,CAAC;0BACJ;wBACF,CAAE;wBACFyE,WAAW,EAAGtE,CAAC,IAAK;0BAClBA,CAAC,CAACkE,cAAc,CAAC,CAAC,CAAC,CAAC;0BACpB,IAAIlE,CAAC,CAACmE,OAAO,IAAInE,CAAC,CAACmE,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;4BACrC,MAAMC,KAAK,GAAGrE,CAAC,CAACmE,OAAO,CAAC,CAAC,CAAC;4BAC1B9D,IAAI,CAAC;8BACHX,OAAO,EAAE2E,KAAK,CAAC3E,OAAO;8BACtBG,OAAO,EAAEwE,KAAK,CAACxE;4BACjB,CAAC,CAAC;0BACJ;wBACF,CAAE;wBACF0E,UAAU,EAAE9D,WAAY;wBACxB2B,SAAS,EAAE,UAAUnH,WAAW,aAAXA,WAAW,gBAAAT,sBAAA,GAAXS,WAAW,CAAE+B,OAAO,cAAAxC,sBAAA,eAApBA,sBAAA,CAAsByC,QAAQ,GAAG,oBAAoB,GAAG,kBAAkB;sBAAG;wBAAAuF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,EACL,EAAC1H,WAAW,aAAXA,WAAW,gBAAAR,sBAAA,GAAXQ,WAAW,CAAE+B,OAAO,cAAAvC,sBAAA,eAApBA,sBAAA,CAAsBwC,QAAQ,kBAC9BrD,OAAA;sBACE2J,OAAO,EAAE1C,WAAY;sBACrBuB,SAAS,EAAC,4EAA4E;sBAAAC,QAAA,EACvF;oBAED;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN,eAGD/I,OAAA;oBAAKwI,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,gBAC7DzI,OAAA;sBAAKwI,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC3BzI,OAAA;wBAAGwI,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAa;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAClE/I,OAAA;wBAAGwI,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,EAC/C,CAAApH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEkI,QAAQ,KAAI;sBAAc;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eACN/I,OAAA;sBAAAyI,QAAA,gBACEzI,OAAA;wBAAGwI,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC1D/I,OAAA;wBAAGwI,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,EAC/CpH,WAAW,aAAXA,WAAW,gBAAAP,sBAAA,GAAXO,WAAW,CAAE+B,OAAO,cAAAtC,sBAAA,eAApBA,sBAAA,CAAsB2I,QAAQ,GAC3B,IAAID,IAAI,CAACnI,WAAW,CAAC+B,OAAO,CAACqG,QAAQ,CAAC,CAACC,kBAAkB,CAAC,CAAC,GAC3D,IAAIF,IAAI,CAAC,CAAC,CAACE,kBAAkB,CAAC;sBAAC;wBAAAd,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN/I,OAAA;cAAKwI,SAAS,EAAC,yCAAyC;cAAAC,QAAA,eACtDzI,OAAA;gBAAKwI,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC9B,EAACpH,WAAW,aAAXA,WAAW,gBAAAN,sBAAA,GAAXM,WAAW,CAAE+B,OAAO,cAAArC,sBAAA,eAApBA,sBAAA,CAAsBsC,QAAQ,kBAC9BrD,OAAA;kBACE2J,OAAO,EAAE1B,iBAAkB;kBAC3B6B,QAAQ,EAAEzH,YAAa;kBACvBmG,SAAS,EAAC,wNAAwN;kBAAAC,QAAA,EAEjOpG,YAAY,gBACXrC,OAAA,CAAAE,SAAA;oBAAAuI,QAAA,gBACEzI,OAAA;sBAAKwI,SAAS,EAAC,4CAA4C;sBAACoC,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAAArC,QAAA,gBAC5HzI,OAAA;wBAAQwI,SAAS,EAAC,YAAY;wBAACuC,EAAE,EAAC,IAAI;wBAACC,EAAE,EAAC,IAAI;wBAACC,CAAC,EAAC,IAAI;wBAACrE,MAAM,EAAC,cAAc;wBAACsE,WAAW,EAAC;sBAAG;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC,eACrG/I,OAAA;wBAAMwI,SAAS,EAAC,YAAY;wBAACqC,IAAI,EAAC,cAAc;wBAACM,CAAC,EAAC;sBAAiH;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzK,CAAC,iBAER;kBAAA,eAAE,CAAC,gBAEH/I,OAAA,CAAAE,SAAA;oBAAAuI,QAAA,gBACEzI,OAAA,CAACd,WAAW;sBAACsJ,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,qBAErC;kBAAA,eAAE;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK;cACT;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3I,EAAA,CArjBID,OAAO;EAAA,QACYnB,SAAS,EACRa,OAAO;AAAA;AAAAuL,EAAA,GAF3BjL,OAAO;AAujBb,eAAeA,OAAO;AAAC,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}