{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\student\\\\Reviews.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport Navbar from './Navbar';\nimport Sidebar from './Sidebar';\nimport Loader from '../components/Loader';\nimport { FaStar, FaUserMd, FaCalendarAlt, FaFilePdf, FaArrowLeft, FaCheckCircle, FaTimesCircle, FaHourglassHalf, FaFilter, FaSearch, FaClipboardCheck, FaChartBar } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Reviews = () => {\n  _s();\n  var _selectedReview$patie, _selectedReview$patie2, _selectedReview$super;\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [pendingReviews, setPendingReviews] = useState([]);\n  const [doneReviews, setDoneReviews] = useState([]);\n  const [selectedReview, setSelectedReview] = useState(null);\n  const [dayFilter, setDayFilter] = useState('all');\n  const [hourFilter, setHourFilter] = useState('');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [activeTab, setActiveTab] = useState('pending');\n  const [procedureTypeAnalytics, setProcedureTypeAnalytics] = useState({});\n  const [procedureFilter, setProcedureFilter] = useState('all');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [showReviewModal, setShowReviewModal] = useState(false);\n  useEffect(() => {\n    const fetchReviews = async () => {\n      if (!user || !token) {\n        setError('Please log in to view your reviews.');\n        navigate('/login');\n        return;\n      }\n      if (user.role !== 'student' || !user.studentId) {\n        setError('Access restricted to students.');\n        navigate('/login');\n        return;\n      }\n      try {\n        setLoading(true);\n        console.log('Fetching reviews for student:', user.studentId);\n        const config = {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        };\n        const response = await axios.get(`http://localhost:5000/api/reviews/student?studentId=${user.studentId}`, config);\n        const reviews = response.data;\n        console.log('Response received:', response);\n        if (!Array.isArray(reviews)) {\n          console.error('Invalid response format:', reviews);\n          throw new Error('Invalid response format: Reviews must be an array.');\n        }\n        console.log('Fetched reviews:', reviews);\n\n        // Filter out signature storage reviews and group by status\n        const filteredReviews = reviews.filter(r => {\n          var _r$patientId;\n          return r.procedureType !== 'Signature Storage' && ((_r$patientId = r.patientId) === null || _r$patientId === void 0 ? void 0 : _r$patientId.nationalId) !== 'signature-storage';\n        });\n        setPendingReviews(filteredReviews.filter(r => r.status === 'pending'));\n        setDoneReviews(filteredReviews.filter(r => r.status !== 'pending'));\n\n        // Group reviews by procedure type for analytics\n        const procedureTypes = {};\n        filteredReviews.forEach(review => {\n          const type = review.procedureType || 'Unknown';\n          if (!procedureTypes[type]) {\n            procedureTypes[type] = {\n              total: 0,\n              accepted: 0,\n              denied: 0,\n              pending: 0\n            };\n          }\n          procedureTypes[type].total++;\n          if (review.status === 'accepted') procedureTypes[type].accepted++;else if (review.status === 'denied') procedureTypes[type].denied++;else procedureTypes[type].pending++;\n        });\n\n        // Store procedure type analytics\n        setProcedureTypeAnalytics(procedureTypes);\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5;\n        console.error('Fetch reviews error:', err);\n        console.error('Response data:', (_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data);\n        console.error('Response status:', (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status);\n        if (((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401) {\n          setError('Session expired. Please log in again.');\n          navigate('/login');\n        } else if (((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403) {\n          setError('You do not have permission to view reviews.');\n        } else if (((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : _err$response5.status) === 404) {\n          // If student not found, show empty state but don't show error\n          console.log('Student not found, showing empty state');\n          setPendingReviews([]);\n          setDoneReviews([]);\n          setProcedureTypeAnalytics({});\n          setLoading(false);\n        } else {\n          var _err$response6, _err$response6$data;\n          setError(((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.message) || 'Failed to load reviews. Please try again later.');\n        }\n        setLoading(false);\n      }\n    };\n    fetchReviews();\n  }, [user, token, navigate]);\n  const filterReviews = reviews => {\n    if (!Array.isArray(reviews)) return [];\n    let filtered = reviews;\n\n    // Day filter\n    const today = new Date();\n    if (dayFilter === 'today') {\n      filtered = filtered.filter(r => {\n        const reviewDate = new Date(r.submittedDate);\n        return reviewDate.toDateString() === today.toDateString();\n      });\n    } else if (dayFilter === 'tomorrow') {\n      const tomorrow = new Date(today);\n      tomorrow.setDate(tomorrow.getDate() + 1);\n      filtered = filtered.filter(r => {\n        const reviewDate = new Date(r.submittedDate);\n        return reviewDate.toDateString() === tomorrow.toDateString();\n      });\n    } else if (dayFilter === 'week') {\n      const weekEnd = new Date(today);\n      weekEnd.setDate(weekEnd.getDate() + 7);\n      filtered = filtered.filter(r => {\n        const reviewDate = new Date(r.submittedDate);\n        return reviewDate >= today && reviewDate <= weekEnd;\n      });\n    } else if (dayFilter === 'month') {\n      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);\n      const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);\n      filtered = filtered.filter(r => {\n        const reviewDate = new Date(r.submittedDate);\n        return reviewDate >= monthStart && reviewDate < nextMonth;\n      });\n    }\n\n    // Hour filter\n    if (hourFilter) {\n      filtered = filtered.filter(r => {\n        const reviewDate = new Date(r.submittedDate);\n        const hours = reviewDate.getHours().toString().padStart(2, '0');\n        return `${hours}:00` === hourFilter;\n      });\n    }\n\n    // Procedure type filter\n    if (procedureFilter && procedureFilter !== 'all') {\n      filtered = filtered.filter(r => r.procedureType === procedureFilter);\n    }\n\n    // Status filter (only applies to the \"done\" tab)\n    if (activeTab === 'done' && statusFilter && statusFilter !== 'all') {\n      filtered = filtered.filter(r => r.status === statusFilter);\n    }\n\n    // Search by patient name or procedure type\n    if (searchQuery) {\n      filtered = filtered.filter(r => {\n        var _r$patientId2, _r$patientId2$fullNam, _r$procedureType;\n        return ((_r$patientId2 = r.patientId) === null || _r$patientId2 === void 0 ? void 0 : (_r$patientId2$fullNam = _r$patientId2.fullName) === null || _r$patientId2$fullNam === void 0 ? void 0 : _r$patientId2$fullNam.toLowerCase().includes(searchQuery.toLowerCase())) || ((_r$procedureType = r.procedureType) === null || _r$procedureType === void 0 ? void 0 : _r$procedureType.toLowerCase().includes(searchQuery.toLowerCase()));\n      });\n    }\n\n    // Sort by submission date (newest first)\n    return filtered.sort((a, b) => new Date(b.submittedDate) - new Date(a.submittedDate));\n  };\n\n  // Calculate basic review counts for display\n  const totalReviews = pendingReviews.length + doneReviews.length;\n  const acceptedReviews = doneReviews.filter(r => r.status === 'accepted').length;\n  const deniedReviews = doneReviews.filter(r => r.status === 'denied').length;\n  const pendingReviewsCount = pendingReviews.length;\n  const renderStars = rating => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex\",\n    children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(FaStar, {\n      className: `h-5 w-5 ${i < (rating || 0) ? 'text-yellow-400' : 'text-gray-300'}`\n    }, i, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 192,\n    columnNumber: 5\n  }, this);\n  const handleReviewClick = review => {\n    setSelectedReview(review);\n    setShowReviewModal(true);\n  };\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm transition-all duration-300\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 text-red-500 mr-3\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-700 font-medium\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\",\n                  children: \"My Reviews\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#333333]\",\n                  children: \"Track your performance feedback\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/dashboard\",\n                  className: \"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FaArrowLeft, {\n                    className: \"h-5 w-5 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 21\n                  }, this), \"Back to Dashboard\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              whileInView: \"show\",\n              viewport: {\n                once: true\n              },\n              className: \"mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[rgba(0,119,182,0.1)] w-10 h-10 rounded-full flex items-center justify-center mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaClipboardCheck, {\n                      className: \"text-[#0077B6] h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 278,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Review Summary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/analytics\",\n                    className: \"px-6 py-3 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg hover:shadow-lg transition-all duration-300 text-sm flex items-center font-medium\",\n                    children: [/*#__PURE__*/_jsxDEV(FaChartBar, {\n                      className: \"mr-2 h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 23\n                    }, this), \"View Full Analytics\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#0077B6] group\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[rgba(0,119,182,0.1)] w-12 h-12 rounded-lg flex items-center justify-center mr-4 group-hover:bg-[rgba(0,119,182,0.2)] transition-colors duration-300\",\n                      children: /*#__PURE__*/_jsxDEV(FaClipboardCheck, {\n                        className: \"h-6 w-6 text-[#0077B6]\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 301,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 300,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Total Reviews\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 304,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-[#0077B6]\",\n                        children: totalReviews\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 305,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 303,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#28A745] group\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-green-50 w-12 h-12 rounded-lg flex items-center justify-center mr-4 group-hover:bg-green-100 transition-colors duration-300\",\n                      children: /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                        className: \"h-6 w-6 text-green-600\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 316,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 315,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Accepted\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 319,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-green-600\",\n                        children: acceptedReviews\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 320,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#F59E0B] group\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-yellow-50 w-12 h-12 rounded-lg flex items-center justify-center mr-4 group-hover:bg-yellow-100 transition-colors duration-300\",\n                      children: /*#__PURE__*/_jsxDEV(FaHourglassHalf, {\n                        className: \"h-6 w-6 text-yellow-600\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 331,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 330,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Pending\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 334,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-yellow-600\",\n                        children: pendingReviewsCount\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 335,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#EF4444] group\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-red-50 w-12 h-12 rounded-lg flex items-center justify-center mr-4 group-hover:bg-red-100 transition-colors duration-300\",\n                      children: /*#__PURE__*/_jsxDEV(FaTimesCircle, {\n                        className: \"h-6 w-6 text-red-600\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 346,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Denied\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 349,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-red-600\",\n                        children: deniedReviews\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 350,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 348,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              className: \"bg-white rounded-xl shadow-sm overflow-hidden mb-8 border border-[rgba(0,119,182,0.1)]\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('pending'),\n                  className: `flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap transition-all duration-300 ${activeTab === 'pending' ? 'border-[#20B2AA] text-[#0077B6] bg-[rgba(0,119,182,0.05)]' : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)] hover:bg-gray-50'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaHourglassHalf, {\n                    className: \"mr-2 h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 21\n                  }, this), \"Pending Reviews\", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `ml-2 px-2 py-1 rounded-full text-xs font-medium ${activeTab === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-600'}`,\n                    children: pendingReviewsCount\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('done'),\n                  className: `flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap transition-all duration-300 ${activeTab === 'done' ? 'border-[#20B2AA] text-[#0077B6] bg-[rgba(0,119,182,0.05)]' : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)] hover:bg-gray-50'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                    className: \"mr-2 h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 21\n                  }, this), \"Done Reviews\", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `ml-2 px-2 py-1 rounded-full text-xs font-medium ${activeTab === 'done' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`,\n                    children: doneReviews.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.2\n              },\n              className: \"mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(FaFilter, {\n                  className: \"text-[#0077B6] mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-[#0077B6]\",\n                  children: \"Filters\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                    children: /*#__PURE__*/_jsxDEV(FaSearch, {\n                      className: \"h-5 w-5 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 419,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    placeholder: \"Search by patient name or procedure\",\n                    value: searchQuery,\n                    onChange: e => setSearchQuery(e.target.value),\n                    className: \"w-full pl-10 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 421,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: dayFilter,\n                    onChange: e => setDayFilter(e.target.value),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA]\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"all\",\n                      children: \"All Dates\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 437,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"today\",\n                      children: \"Today\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 438,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"tomorrow\",\n                      children: \"Tomorrow\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 439,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"week\",\n                      children: \"This Week\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 440,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"month\",\n                      children: \"This Month\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 441,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: hourFilter,\n                    onChange: e => setHourFilter(e.target.value),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA]\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"All Hours\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 452,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"09:00\",\n                      children: \"09:00 AM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 453,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"10:00\",\n                      children: \"10:00 AM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"11:00\",\n                      children: \"11:00 AM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 455,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"12:00\",\n                      children: \"12:00 PM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 456,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"13:00\",\n                      children: \"01:00 PM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 457,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"14:00\",\n                      children: \"02:00 PM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 458,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"15:00\",\n                      children: \"03:00 PM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 459,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: procedureFilter,\n                    onChange: e => setProcedureFilter(e.target.value),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA]\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"all\",\n                      children: \"All Procedures\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 470,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Operative\",\n                      children: \"Operative\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 471,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Fixed Prosthodontics\",\n                      children: \"Fixed Prosthodontics\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 472,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Removable Prosthodontics\",\n                      children: \"Removable Prosthodontics\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 473,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Endodontics\",\n                      children: \"Endodontics\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 474,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Periodontics\",\n                      children: \"Periodontics\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 19\n                }, this), activeTab === 'done' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: statusFilter,\n                    onChange: e => setStatusFilter(e.target.value),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA]\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"all\",\n                      children: \"All Statuses\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"accepted\",\n                      children: \"Accepted\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"denied\",\n                      children: \"Denied\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setSearchQuery('');\n                      setDayFilter('all');\n                      setHourFilter('');\n                      setProcedureFilter('all');\n                      setStatusFilter('all');\n                    },\n                    className: \"px-4 py-2 bg-[rgba(0,119,182,0.1)] hover:bg-[rgba(0,119,182,0.2)] text-[#0077B6] rounded-lg transition-all duration-300\",\n                    children: \"Clear Filters\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 496,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              whileInView: \"show\",\n              viewport: {\n                once: true\n              },\n              className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-bold text-[#0077B6] mb-6\",\n                  children: activeTab === 'pending' ? 'Pending Reviews' : 'Done Reviews'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"overflow-x-auto\",\n                  children: /*#__PURE__*/_jsxDEV(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                      className: \"bg-gray-50\",\n                      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: activeTab === 'pending' ? 'Submitted Date' : 'Reviewed Date'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 528,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Patient\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 531,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Procedure\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 532,\n                          columnNumber: 27\n                        }, this), activeTab === 'done' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"Status\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 535,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"Supervisor\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 536,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Actions\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 539,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 527,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 526,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                      className: \"bg-white divide-y divide-gray-200\",\n                      children: filterReviews(activeTab === 'pending' ? pendingReviews : doneReviews).length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: /*#__PURE__*/_jsxDEV(\"td\", {\n                          colSpan: activeTab === 'pending' ? 4 : 6,\n                          className: \"px-6 py-8 text-center\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-center justify-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                              className: \"h-12 w-12 text-gray-400 mb-4\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              stroke: \"currentColor\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 548,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 547,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                              className: \"text-lg font-medium text-gray-900\",\n                              children: [\"No \", activeTab === 'pending' ? 'pending' : 'completed', \" reviews\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 550,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"mt-1 text-gray-500\",\n                              children: \"No reviews match the selected filters.\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 553,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 546,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 545,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 544,\n                        columnNumber: 27\n                      }, this) : filterReviews(activeTab === 'pending' ? pendingReviews : doneReviews).map(review => {\n                        var _review$patientId;\n                        return /*#__PURE__*/_jsxDEV(motion.tr, {\n                          initial: {\n                            opacity: 0\n                          },\n                          animate: {\n                            opacity: 1\n                          },\n                          className: \"hover:bg-gray-50 cursor-pointer\",\n                          onClick: () => setSelectedReview(review),\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                            children: activeTab === 'pending' ? new Date(review.submittedDate).toLocaleDateString() : new Date(review.reviewedDate || review.submittedDate).toLocaleDateString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 566,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: ((_review$patientId = review.patientId) === null || _review$patientId === void 0 ? void 0 : _review$patientId.fullName) || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 572,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: review.procedureType || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 575,\n                            columnNumber: 31\n                          }, this), activeTab === 'done' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                              className: \"px-6 py-4 whitespace-nowrap\",\n                              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: `px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${review.status === 'accepted' ? 'bg-[rgba(40,167,69,0.1)] text-[#28A745]' : 'bg-red-100 text-red-800'}`,\n                                children: review.status === 'accepted' ? 'Accepted' : review.status === 'denied' ? 'Declined' : review.status\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 581,\n                                columnNumber: 37\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 580,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                              children: review.supervisorName || 'N/A'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 589,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-[#0077B6]\",\n                            children: /*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => setSelectedReview(review),\n                              className: \"hover:text-[#20B2AA] transition-all duration-300\",\n                              children: \"View\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 595,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 594,\n                            columnNumber: 31\n                          }, this)]\n                        }, review._id, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 559,\n                          columnNumber: 29\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 542,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this), selectedReview && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-[#0077B6]\",\n              children: \"Review Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedReview(null),\n              className: \"text-gray-400 hover:text-[#0077B6] transition-all duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 15\n          }, this), !selectedReview ? /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-700\",\n            children: \"Loading review details...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaUserMd, {\n                  className: \"h-5 w-5 mr-2 text-[#0077B6]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 634,\n                  columnNumber: 23\n                }, this), \"Patient Information\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: ((_selectedReview$patie = selectedReview.patientId) === null || _selectedReview$patie === void 0 ? void 0 : _selectedReview$patie.fullName) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"National ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 645,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: ((_selectedReview$patie2 = selectedReview.patientId) === null || _selectedReview$patie2 === void 0 ? void 0 : _selectedReview$patie2.nationalId) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 646,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                  className: \"h-5 w-5 mr-2 text-[#0077B6]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 23\n                }, this), \"Review Details\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Student\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 662,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-900 mt-1\",\n                      children: selectedReview.studentName || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 663,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 661,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Procedure Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 668,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-900 mt-1\",\n                      children: selectedReview.procedureType || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 669,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 667,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Submission Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 674,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-900 mt-1\",\n                      children: new Date(selectedReview.submittedDate).toLocaleString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 675,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 673,\n                    columnNumber: 25\n                  }, this), selectedReview.status !== 'pending' && selectedReview.reviewedDate && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Reviewed Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 681,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-900 mt-1\",\n                      children: new Date(selectedReview.reviewedDate).toLocaleString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 682,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 680,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 688,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full mt-1 ${selectedReview.status === 'accepted' ? 'bg-[rgba(40,167,69,0.1)] text-[#28A745]' : selectedReview.status === 'denied' ? 'bg-red-100 text-red-800' : 'bg-[rgba(0,119,182,0.1)] text-[#0077B6]'}`,\n                      children: selectedReview.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 689,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 687,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 660,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Review Steps\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 705,\n                    columnNumber: 25\n                  }, this), selectedReview.reviewSteps && selectedReview.reviewSteps.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-lg border border-gray-200 overflow-hidden\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"divide-y divide-gray-200\",\n                      children: selectedReview.reviewSteps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `p-3 flex items-center justify-between ${step.completed ? 'bg-orange-50' : 'bg-white'}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"inline-block w-6 text-center mr-2 text-blue-600 font-bold\",\n                            children: [index + 1, \".\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 717,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-medium text-gray-900\",\n                            children: step.description\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 718,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 716,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: step.completed ? /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-600\",\n                            children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                              className: \"h-3 w-3 mr-1\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 723,\n                              columnNumber: 41\n                            }, this), \"Pending\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 722,\n                            columnNumber: 39\n                          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600\",\n                            children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n                              className: \"h-3 w-3 mr-1\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 728,\n                              columnNumber: 41\n                            }, this), \"Not Done\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 727,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 720,\n                          columnNumber: 35\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 710,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 708,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 707,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"No review steps available\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 738,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 704,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Note\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 743,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1 p-3 bg-white rounded border border-gray-200\",\n                    children: selectedReview.note || 'No notes provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 744,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 742,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Comment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 750,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: selectedReview.comment || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 751,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 749,\n                  columnNumber: 23\n                }, this), selectedReview.chartId && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Dental Chart\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 758,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: `/api/dental/chart/${selectedReview.chartId}/pdf`,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"text-sm text-blue-600 flex items-center mt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(FaFilePdf, {\n                      className: \"h-5 w-5 mr-2 text-red-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 765,\n                      columnNumber: 29\n                    }, this), \"View Chart PDF\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 759,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 757,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-[#0077B6] mb-4\",\n                children: \"Supervisor Feedback\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 21\n              }, this), selectedReview.status === 'pending' ? /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-700\",\n                children: \"Awaiting supervisor review...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 777,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Supervisor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 781,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: [selectedReview.supervisorName || 'N/A', \" (ID: \", ((_selectedReview$super = selectedReview.supervisorId) === null || _selectedReview$super === void 0 ? void 0 : _selectedReview$super._id) || 'N/A', \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 782,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 787,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1 capitalize\",\n                    children: selectedReview.status === 'accepted' ? 'Accepted' : selectedReview.status === 'denied' ? 'Declined' : selectedReview.status || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 788,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 786,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Procedure Quality\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 795,\n                    columnNumber: 27\n                  }, this), renderStars(selectedReview.procedureQuality)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 794,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Patient Interaction\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 799,\n                    columnNumber: 27\n                  }, this), renderStars(selectedReview.patientInteraction)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 798,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Supervisor Note\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 803,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: selectedReview.note || 'No notes provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 25\n                }, this), selectedReview.supervisorSignature && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 pt-4 border-t border-gray-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Supervisor Signature\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 811,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"border border-gray-300 rounded-lg bg-white p-4 flex justify-center\",\n                    children: selectedReview.supervisorSignature.startsWith('data:image') ? /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: selectedReview.supervisorSignature,\n                      alt: \"Signature\",\n                      className: \"max-h-20\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 814,\n                      columnNumber: 33\n                    }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-signature text-lg\",\n                      children: selectedReview.supervisorSignature\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 816,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 812,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 810,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 779,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 774,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 612,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 611,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 5\n  }, this);\n};\n_s(Reviews, \"DkstHsJRLxDcO+bXopoFxQWrZSM=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Reviews;\nexport default Reviews;\nvar _c;\n$RefreshReg$(_c, \"Reviews\");", "map": {"version": 3, "names": ["useState", "useEffect", "Link", "useNavigate", "motion", "axios", "useAuth", "<PERSON><PERSON><PERSON>", "Sidebar", "Loader", "FaStar", "FaUserMd", "FaCalendarAlt", "FaFilePdf", "FaArrowLeft", "FaCheckCircle", "FaTimesCircle", "FaHourglassHalf", "FaFilter", "FaSearch", "FaClipboardCheck", "FaChartBar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Reviews", "_s", "_selectedReview$patie", "_selectedReview$patie2", "_selectedReview$super", "navigate", "user", "token", "sidebarOpen", "setSidebarOpen", "loading", "setLoading", "error", "setError", "pendingReviews", "setPendingReviews", "doneReviews", "setDoneReviews", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedReview", "dayFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hourFilter", "setHour<PERSON><PERSON>er", "searchQuery", "setSearch<PERSON>uery", "activeTab", "setActiveTab", "procedureTypeAnalytics", "setProcedureTypeAnalytics", "procedureFilter", "setProcedureFilter", "statusFilter", "setStatus<PERSON>ilter", "showReviewModal", "setShowReviewModal", "fetchReviews", "role", "studentId", "console", "log", "config", "headers", "Authorization", "response", "get", "reviews", "data", "Array", "isArray", "Error", "filteredReviews", "filter", "r", "_r$patientId", "procedureType", "patientId", "nationalId", "status", "procedureTypes", "for<PERSON>ach", "review", "type", "total", "accepted", "denied", "pending", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response6", "_err$response6$data", "message", "filterReviews", "filtered", "today", "Date", "reviewDate", "submittedDate", "toDateString", "tomorrow", "setDate", "getDate", "weekEnd", "monthStart", "getFullYear", "getMonth", "nextMonth", "hours", "getHours", "toString", "padStart", "_r$patientId2", "_r$patientId2$fullNam", "_r$procedureType", "fullName", "toLowerCase", "includes", "sort", "a", "b", "totalReviews", "length", "acceptedReviews", "deniedReviews", "pendingReviewsCount", "renderStars", "rating", "className", "children", "map", "_", "i", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleReviewClick", "container", "hidden", "opacity", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "item", "y", "isOpen", "setIsOpen", "toggleSidebar", "div", "initial", "animate", "fill", "viewBox", "fillRule", "d", "clipRule", "duration", "button", "whileHover", "scale", "whileTap", "to", "variants", "whileInView", "viewport", "once", "onClick", "delay", "placeholder", "value", "onChange", "e", "target", "colSpan", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "_review$patientId", "tr", "toLocaleDateString", "reviewedDate", "<PERSON><PERSON><PERSON>", "_id", "studentName", "toLocaleString", "reviewSteps", "step", "index", "completed", "description", "note", "comment", "chartId", "href", "rel", "supervisorId", "procedureQuality", "patientInteraction", "supervisorSignature", "startsWith", "src", "alt", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/student/Reviews.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { motion } from 'framer-motion';\r\nimport axios from 'axios';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport Navbar from './Navbar';\r\nimport Sidebar from './Sidebar';\r\nimport Loader from '../components/Loader';\r\nimport { FaStar,FaUserMd, FaCalendarAlt, FaFilePdf, FaArrowLeft, FaCheckCircle, FaTimesCircle, FaHourglassHalf,\r\n  FaFilter, FaSearch, FaClipboardCheck, FaChartBar } from 'react-icons/fa';\r\n\r\nconst Reviews = () => {\r\n  const navigate = useNavigate();\r\n  const { user, token } = useAuth();\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [pendingReviews, setPendingReviews] = useState([]);\r\n  const [doneReviews, setDoneReviews] = useState([]);\r\n  const [selectedReview, setSelectedReview] = useState(null);\r\n  const [dayFilter, setDayFilter] = useState('all');\r\n  const [hourFilter, setHourFilter] = useState('');\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [activeTab, setActiveTab] = useState('pending');\r\n  const [procedureTypeAnalytics, setProcedureTypeAnalytics] = useState({});\r\n  const [procedureFilter, setProcedureFilter] = useState('all');\r\n  const [statusFilter, setStatusFilter] = useState('all');\r\n  const [showReviewModal, setShowReviewModal] = useState(false);\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    const fetchReviews = async () => {\r\n      if (!user || !token) {\r\n        setError('Please log in to view your reviews.');\r\n        navigate('/login');\r\n        return;\r\n      }\r\n      if (user.role !== 'student' || !user.studentId) {\r\n        setError('Access restricted to students.');\r\n        navigate('/login');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        setLoading(true);\r\n        console.log('Fetching reviews for student:', user.studentId);\r\n\r\n        const config = { headers: { Authorization: `Bearer ${token}` } };\r\n        const response = await axios.get(`http://localhost:5000/api/reviews/student?studentId=${user.studentId}`, config);\r\n        const reviews = response.data;\r\n\r\n        console.log('Response received:', response);\r\n\r\n        if (!Array.isArray(reviews)) {\r\n          console.error('Invalid response format:', reviews);\r\n          throw new Error('Invalid response format: Reviews must be an array.');\r\n        }\r\n\r\n        console.log('Fetched reviews:', reviews);\r\n\r\n        // Filter out signature storage reviews and group by status\r\n        const filteredReviews = reviews.filter(r =>\r\n          r.procedureType !== 'Signature Storage' &&\r\n          r.patientId?.nationalId !== 'signature-storage'\r\n        );\r\n\r\n        setPendingReviews(filteredReviews.filter(r => r.status === 'pending'));\r\n        setDoneReviews(filteredReviews.filter(r => r.status !== 'pending'));\r\n\r\n        // Group reviews by procedure type for analytics\r\n        const procedureTypes = {};\r\n        filteredReviews.forEach(review => {\r\n          const type = review.procedureType || 'Unknown';\r\n          if (!procedureTypes[type]) {\r\n            procedureTypes[type] = {\r\n              total: 0,\r\n              accepted: 0,\r\n              denied: 0,\r\n              pending: 0\r\n            };\r\n          }\r\n          procedureTypes[type].total++;\r\n          if (review.status === 'accepted') procedureTypes[type].accepted++;\r\n          else if (review.status === 'denied') procedureTypes[type].denied++;\r\n          else procedureTypes[type].pending++;\r\n        });\r\n\r\n        // Store procedure type analytics\r\n        setProcedureTypeAnalytics(procedureTypes);\r\n\r\n        setLoading(false);\r\n      } catch (err) {\r\n        console.error('Fetch reviews error:', err);\r\n        console.error('Response data:', err.response?.data);\r\n        console.error('Response status:', err.response?.status);\r\n\r\n        if (err.response?.status === 401) {\r\n          setError('Session expired. Please log in again.');\r\n          navigate('/login');\r\n        } else if (err.response?.status === 403) {\r\n          setError('You do not have permission to view reviews.');\r\n        } else if (err.response?.status === 404) {\r\n          // If student not found, show empty state but don't show error\r\n          console.log('Student not found, showing empty state');\r\n          setPendingReviews([]);\r\n          setDoneReviews([]);\r\n          setProcedureTypeAnalytics({});\r\n          setLoading(false);\r\n        } else {\r\n          setError(err.response?.data?.message || 'Failed to load reviews. Please try again later.');\r\n        }\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchReviews();\r\n  }, [user, token, navigate]);\r\n\r\n  const filterReviews = (reviews) => {\r\n    if (!Array.isArray(reviews)) return [];\r\n\r\n    let filtered = reviews;\r\n\r\n    // Day filter\r\n    const today = new Date();\r\n    if (dayFilter === 'today') {\r\n      filtered = filtered.filter(r => {\r\n        const reviewDate = new Date(r.submittedDate);\r\n        return reviewDate.toDateString() === today.toDateString();\r\n      });\r\n    } else if (dayFilter === 'tomorrow') {\r\n      const tomorrow = new Date(today);\r\n      tomorrow.setDate(tomorrow.getDate() + 1);\r\n      filtered = filtered.filter(r => {\r\n        const reviewDate = new Date(r.submittedDate);\r\n        return reviewDate.toDateString() === tomorrow.toDateString();\r\n      });\r\n    } else if (dayFilter === 'week') {\r\n      const weekEnd = new Date(today);\r\n      weekEnd.setDate(weekEnd.getDate() + 7);\r\n      filtered = filtered.filter(r => {\r\n        const reviewDate = new Date(r.submittedDate);\r\n        return reviewDate >= today && reviewDate <= weekEnd;\r\n      });\r\n    } else if (dayFilter === 'month') {\r\n      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);\r\n      const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);\r\n      filtered = filtered.filter(r => {\r\n        const reviewDate = new Date(r.submittedDate);\r\n        return reviewDate >= monthStart && reviewDate < nextMonth;\r\n      });\r\n    }\r\n\r\n    // Hour filter\r\n    if (hourFilter) {\r\n      filtered = filtered.filter(r => {\r\n        const reviewDate = new Date(r.submittedDate);\r\n        const hours = reviewDate.getHours().toString().padStart(2, '0');\r\n        return `${hours}:00` === hourFilter;\r\n      });\r\n    }\r\n\r\n    // Procedure type filter\r\n    if (procedureFilter && procedureFilter !== 'all') {\r\n      filtered = filtered.filter(r => r.procedureType === procedureFilter);\r\n    }\r\n\r\n    // Status filter (only applies to the \"done\" tab)\r\n    if (activeTab === 'done' && statusFilter && statusFilter !== 'all') {\r\n      filtered = filtered.filter(r => r.status === statusFilter);\r\n    }\r\n\r\n    // Search by patient name or procedure type\r\n    if (searchQuery) {\r\n      filtered = filtered.filter(r =>\r\n        (r.patientId?.fullName?.toLowerCase().includes(searchQuery.toLowerCase())) ||\r\n        (r.procedureType?.toLowerCase().includes(searchQuery.toLowerCase()))\r\n      );\r\n    }\r\n\r\n    // Sort by submission date (newest first)\r\n    return filtered.sort((a, b) => new Date(b.submittedDate) - new Date(a.submittedDate));\r\n  };\r\n\r\n  // Calculate basic review counts for display\r\n  const totalReviews = pendingReviews.length + doneReviews.length;\r\n  const acceptedReviews = doneReviews.filter(r => r.status === 'accepted').length;\r\n  const deniedReviews = doneReviews.filter(r => r.status === 'denied').length;\r\n  const pendingReviewsCount = pendingReviews.length;\r\n\r\n  const renderStars = (rating) => (\r\n    <div className=\"flex\">\r\n      {[...Array(5)].map((_, i) => (\r\n        <FaStar\r\n          key={i}\r\n          className={`h-5 w-5 ${i < (rating || 0) ? 'text-yellow-400' : 'text-gray-300'}`}\r\n        />\r\n      ))}\r\n    </div>\r\n  );\r\n\r\n  const handleReviewClick = (review) => {\r\n    setSelectedReview(review);\r\n    setShowReviewModal(true);\r\n  };\r\n\r\n  const container = {\r\n    hidden: { opacity: 0 },\r\n    show: {\r\n      opacity: 1,\r\n      transition: { staggerChildren: 0.1 },\r\n    },\r\n  };\r\n\r\n  const item = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    show: { opacity: 1, y: 0 },\r\n  };\r\n\r\n  if (loading) {\r\n    return <Loader />;\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50\">\r\n      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n        <main className=\"flex-1 overflow-y-auto p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\">\r\n          <div className=\"max-w-7xl mx-auto\">\r\n            {error && (\r\n              <motion.div\r\n                initial={{ opacity: 0, y: -20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                className=\"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm transition-all duration-300\"\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  <svg className=\"w-5 h-5 text-red-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path\r\n                      fillRule=\"evenodd\"\r\n                      d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\"\r\n                      clipRule=\"evenodd\"\r\n                    />\r\n                  </svg>\r\n                  <p className=\"text-red-700 font-medium\">{error}</p>\r\n                </div>\r\n              </motion.div>\r\n            )}\r\n\r\n            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>\r\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\r\n                <div>\r\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\">My Reviews</h1>\r\n                  <p className=\"text-[#333333]\">Track your performance feedback</p>\r\n                </div>\r\n                <motion.button whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\r\n                  <Link\r\n                    to=\"/dashboard\"\r\n                    className=\"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\"\r\n                  >\r\n                    <FaArrowLeft className=\"h-5 w-5 mr-2\" />\r\n                    Back to Dashboard\r\n                  </Link>\r\n                </motion.button>\r\n              </div>\r\n\r\n              {/* Summary Stats */}\r\n              <motion.div\r\n                variants={container}\r\n                initial=\"hidden\"\r\n                whileInView=\"show\"\r\n                viewport={{ once: true }}\r\n                className=\"mb-8\"\r\n              >\r\n                <div className=\"flex items-center justify-between mb-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"bg-[rgba(0,119,182,0.1)] w-10 h-10 rounded-full flex items-center justify-center mr-3\">\r\n                      <FaClipboardCheck className=\"text-[#0077B6] h-5 w-5\" />\r\n                    </div>\r\n                    <h3 className=\"text-xl font-bold text-[#0077B6]\">Review Summary</h3>\r\n                  </div>\r\n                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\r\n                    <Link\r\n                      to=\"/analytics\"\r\n                      className=\"px-6 py-3 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg hover:shadow-lg transition-all duration-300 text-sm flex items-center font-medium\"\r\n                    >\r\n                      <FaChartBar className=\"mr-2 h-4 w-4\" />\r\n                      View Full Analytics\r\n                    </Link>\r\n                  </motion.div>\r\n                </div>\r\n\r\n                {/* Enhanced Stats Cards */}\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6\">\r\n                  <motion.div\r\n                    variants={item}\r\n                    className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#0077B6] group\"\r\n                  >\r\n                    <div className=\"flex items-center\">\r\n                      <div className=\"bg-[rgba(0,119,182,0.1)] w-12 h-12 rounded-lg flex items-center justify-center mr-4 group-hover:bg-[rgba(0,119,182,0.2)] transition-colors duration-300\">\r\n                        <FaClipboardCheck className=\"h-6 w-6 text-[#0077B6]\" />\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-sm font-medium text-gray-500\">Total Reviews</p>\r\n                        <p className=\"text-2xl font-bold text-[#0077B6]\">{totalReviews}</p>\r\n                      </div>\r\n                    </div>\r\n                  </motion.div>\r\n\r\n                  <motion.div\r\n                    variants={item}\r\n                    className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#28A745] group\"\r\n                  >\r\n                    <div className=\"flex items-center\">\r\n                      <div className=\"bg-green-50 w-12 h-12 rounded-lg flex items-center justify-center mr-4 group-hover:bg-green-100 transition-colors duration-300\">\r\n                        <FaCheckCircle className=\"h-6 w-6 text-green-600\" />\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-sm font-medium text-gray-500\">Accepted</p>\r\n                        <p className=\"text-2xl font-bold text-green-600\">{acceptedReviews}</p>\r\n                      </div>\r\n                    </div>\r\n                  </motion.div>\r\n\r\n                  <motion.div\r\n                    variants={item}\r\n                    className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#F59E0B] group\"\r\n                  >\r\n                    <div className=\"flex items-center\">\r\n                      <div className=\"bg-yellow-50 w-12 h-12 rounded-lg flex items-center justify-center mr-4 group-hover:bg-yellow-100 transition-colors duration-300\">\r\n                        <FaHourglassHalf className=\"h-6 w-6 text-yellow-600\" />\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-sm font-medium text-gray-500\">Pending</p>\r\n                        <p className=\"text-2xl font-bold text-yellow-600\">{pendingReviewsCount}</p>\r\n                      </div>\r\n                    </div>\r\n                  </motion.div>\r\n\r\n                  <motion.div\r\n                    variants={item}\r\n                    className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#EF4444] group\"\r\n                  >\r\n                    <div className=\"flex items-center\">\r\n                      <div className=\"bg-red-50 w-12 h-12 rounded-lg flex items-center justify-center mr-4 group-hover:bg-red-100 transition-colors duration-300\">\r\n                        <FaTimesCircle className=\"h-6 w-6 text-red-600\" />\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-sm font-medium text-gray-500\">Denied</p>\r\n                        <p className=\"text-2xl font-bold text-red-600\">{deniedReviews}</p>\r\n                      </div>\r\n                    </div>\r\n                  </motion.div>\r\n                </div>\r\n              </motion.div>\r\n\r\n              {/* Enhanced Tabs */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                className=\"bg-white rounded-xl shadow-sm overflow-hidden mb-8 border border-[rgba(0,119,182,0.1)]\"\r\n              >\r\n                <div className=\"flex\">\r\n                  <button\r\n                    onClick={() => setActiveTab('pending')}\r\n                    className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap transition-all duration-300 ${\r\n                      activeTab === 'pending'\r\n                        ? 'border-[#20B2AA] text-[#0077B6] bg-[rgba(0,119,182,0.05)]'\r\n                        : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)] hover:bg-gray-50'\r\n                    }`}\r\n                  >\r\n                    <FaHourglassHalf className=\"mr-2 h-4 w-4\" />\r\n                    Pending Reviews\r\n                    <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${\r\n                      activeTab === 'pending'\r\n                        ? 'bg-yellow-100 text-yellow-800'\r\n                        : 'bg-gray-100 text-gray-600'\r\n                    }`}>\r\n                      {pendingReviewsCount}\r\n                    </span>\r\n                  </button>\r\n                  <button\r\n                    onClick={() => setActiveTab('done')}\r\n                    className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap transition-all duration-300 ${\r\n                      activeTab === 'done'\r\n                        ? 'border-[#20B2AA] text-[#0077B6] bg-[rgba(0,119,182,0.05)]'\r\n                        : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)] hover:bg-gray-50'\r\n                    }`}\r\n                  >\r\n                    <FaCheckCircle className=\"mr-2 h-4 w-4\" />\r\n                    Done Reviews\r\n                    <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${\r\n                      activeTab === 'done'\r\n                        ? 'bg-green-100 text-green-800'\r\n                        : 'bg-gray-100 text-gray-600'\r\n                    }`}>\r\n                      {doneReviews.length}\r\n                    </span>\r\n                  </button>\r\n                </div>\r\n              </motion.div>\r\n\r\n              {/* Filters and Search */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.2 }}\r\n                className=\"mb-8\"\r\n              >\r\n                <div className=\"flex items-center mb-4\">\r\n                  <FaFilter className=\"text-[#0077B6] mr-2\" />\r\n                  <h3 className=\"text-lg font-semibold text-[#0077B6]\">Filters</h3>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n                  {/* Search */}\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                      <FaSearch className=\"h-5 w-5 text-gray-400\" />\r\n                    </div>\r\n                    <input\r\n                      type=\"text\"\r\n                      placeholder=\"Search by patient name or procedure\"\r\n                      value={searchQuery}\r\n                      onChange={(e) => setSearchQuery(e.target.value)}\r\n                      className=\"w-full pl-10 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA]\"\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Date Filter */}\r\n                  <div>\r\n                    <select\r\n                      value={dayFilter}\r\n                      onChange={(e) => setDayFilter(e.target.value)}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA]\"\r\n                    >\r\n                      <option value=\"all\">All Dates</option>\r\n                      <option value=\"today\">Today</option>\r\n                      <option value=\"tomorrow\">Tomorrow</option>\r\n                      <option value=\"week\">This Week</option>\r\n                      <option value=\"month\">This Month</option>\r\n                    </select>\r\n                  </div>\r\n\r\n                  {/* Hour Filter */}\r\n                  <div>\r\n                    <select\r\n                      value={hourFilter}\r\n                      onChange={(e) => setHourFilter(e.target.value)}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA]\"\r\n                    >\r\n                      <option value=\"\">All Hours</option>\r\n                      <option value=\"09:00\">09:00 AM</option>\r\n                      <option value=\"10:00\">10:00 AM</option>\r\n                      <option value=\"11:00\">11:00 AM</option>\r\n                      <option value=\"12:00\">12:00 PM</option>\r\n                      <option value=\"13:00\">01:00 PM</option>\r\n                      <option value=\"14:00\">02:00 PM</option>\r\n                      <option value=\"15:00\">03:00 PM</option>\r\n                    </select>\r\n                  </div>\r\n\r\n                  {/* Procedure Type Filter */}\r\n                  <div>\r\n                    <select\r\n                      value={procedureFilter}\r\n                      onChange={(e) => setProcedureFilter(e.target.value)}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA]\"\r\n                    >\r\n                      <option value=\"all\">All Procedures</option>\r\n                      <option value=\"Operative\">Operative</option>\r\n                      <option value=\"Fixed Prosthodontics\">Fixed Prosthodontics</option>\r\n                      <option value=\"Removable Prosthodontics\">Removable Prosthodontics</option>\r\n                      <option value=\"Endodontics\">Endodontics</option>\r\n                      <option value=\"Periodontics\">Periodontics</option>\r\n                    </select>\r\n                  </div>\r\n\r\n                  {/* Status Filter (only for done tab) */}\r\n                  {activeTab === 'done' && (\r\n                    <div>\r\n                      <select\r\n                        value={statusFilter}\r\n                        onChange={(e) => setStatusFilter(e.target.value)}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA]\"\r\n                      >\r\n                        <option value=\"all\">All Statuses</option>\r\n                        <option value=\"accepted\">Accepted</option>\r\n                        <option value=\"denied\">Denied</option>\r\n                      </select>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Clear Filters Button */}\r\n                  <div className=\"flex items-center\">\r\n                    <button\r\n                      onClick={() => {\r\n                        setSearchQuery('');\r\n                        setDayFilter('all');\r\n                        setHourFilter('');\r\n                        setProcedureFilter('all');\r\n                        setStatusFilter('all');\r\n                      }}\r\n                      className=\"px-4 py-2 bg-[rgba(0,119,182,0.1)] hover:bg-[rgba(0,119,182,0.2)] text-[#0077B6] rounded-lg transition-all duration-300\"\r\n                    >\r\n                      Clear Filters\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n\r\n              {/* Reviews Table */}\r\n              <motion.div\r\n                variants={container}\r\n                initial=\"hidden\"\r\n                whileInView=\"show\"\r\n                viewport={{ once: true }}\r\n                className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden\"\r\n              >\r\n                <div className=\"p-6\">\r\n                  <h2 className=\"text-xl font-bold text-[#0077B6] mb-6\">\r\n                    {activeTab === 'pending' ? 'Pending Reviews' : 'Done Reviews'}\r\n                  </h2>\r\n                  <div className=\"overflow-x-auto\">\r\n                    <table className=\"min-w-full divide-y divide-gray-200\">\r\n                      <thead className=\"bg-gray-50\">\r\n                        <tr>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                            {activeTab === 'pending' ? 'Submitted Date' : 'Reviewed Date'}\r\n                          </th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Patient</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Procedure</th>\r\n                          {activeTab === 'done' && (\r\n                            <>\r\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Status</th>\r\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Supervisor</th>\r\n                            </>\r\n                          )}\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                        {filterReviews(activeTab === 'pending' ? pendingReviews : doneReviews).length === 0 ? (\r\n                          <tr>\r\n                            <td colSpan={activeTab === 'pending' ? 4 : 6} className=\"px-6 py-8 text-center\">\r\n                              <div className=\"flex flex-col items-center justify-center\">\r\n                                <svg className=\"h-12 w-12 text-gray-400 mb-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\r\n                                </svg>\r\n                                <h3 className=\"text-lg font-medium text-gray-900\">\r\n                                  No {activeTab === 'pending' ? 'pending' : 'completed'} reviews\r\n                                </h3>\r\n                                <p className=\"mt-1 text-gray-500\">No reviews match the selected filters.</p>\r\n                              </div>\r\n                            </td>\r\n                          </tr>\r\n                        ) : (\r\n                          filterReviews(activeTab === 'pending' ? pendingReviews : doneReviews).map((review) => (\r\n                            <motion.tr\r\n                              key={review._id}\r\n                              initial={{ opacity: 0 }}\r\n                              animate={{ opacity: 1 }}\r\n                              className=\"hover:bg-gray-50 cursor-pointer\"\r\n                              onClick={() => setSelectedReview(review)}\r\n                            >\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\r\n                                {activeTab === 'pending'\r\n                                  ? new Date(review.submittedDate).toLocaleDateString()\r\n                                  : new Date(review.reviewedDate || review.submittedDate).toLocaleDateString()\r\n                                }\r\n                              </td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                {review.patientId?.fullName || 'N/A'}\r\n                              </td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                {review.procedureType || 'N/A'}\r\n                              </td>\r\n                              {activeTab === 'done' && (\r\n                                <>\r\n                                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                    <span\r\n                                      className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${\r\n                                        review.status === 'accepted' ? 'bg-[rgba(40,167,69,0.1)] text-[#28A745]' : 'bg-red-100 text-red-800'\r\n                                      }`}\r\n                                    >\r\n                                      {review.status === 'accepted' ? 'Accepted' : review.status === 'denied' ? 'Declined' : review.status}\r\n                                    </span>\r\n                                  </td>\r\n                                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                    {review.supervisorName || 'N/A'}\r\n                                  </td>\r\n                                </>\r\n                              )}\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-[#0077B6]\">\r\n                                <button onClick={() => setSelectedReview(review)} className=\"hover:text-[#20B2AA] transition-all duration-300\">View</button>\r\n                              </td>\r\n                            </motion.tr>\r\n                          ))\r\n                        )}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n\r\n      {selectedReview && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n          <motion.div\r\n            initial={{ scale: 0.9, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            className=\"bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto\"\r\n          >\r\n            <div className=\"p-6\">\r\n              <div className=\"flex justify-between items-center mb-6\">\r\n                <h2 className=\"text-2xl font-bold text-[#0077B6]\">Review Details</h2>\r\n                <button onClick={() => setSelectedReview(null)} className=\"text-gray-400 hover:text-[#0077B6] transition-all duration-300\">\r\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n\r\n              {!selectedReview ? (\r\n                <p className=\"text-sm text-gray-700\">Loading review details...</p>\r\n              ) : (\r\n                <div className=\"space-y-6\">\r\n                  {/* Patient Information */}\r\n                  <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\">\r\n                    <h3 className=\"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\">\r\n                      <FaUserMd className=\"h-5 w-5 mr-2 text-[#0077B6]\" />\r\n                      Patient Information\r\n                    </h3>\r\n                    <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500\">Name</h4>\r\n                        <p className=\"text-sm text-gray-900 mt-1\">\r\n                          {selectedReview.patientId?.fullName || 'N/A'}\r\n                        </p>\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500\">National ID</h4>\r\n                        <p className=\"text-sm text-gray-900 mt-1\">\r\n                          {selectedReview.patientId?.nationalId || 'N/A'}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Review Details */}\r\n                  <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\">\r\n                    <h3 className=\"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\">\r\n                      <FaCalendarAlt className=\"h-5 w-5 mr-2 text-[#0077B6]\" />\r\n                      Review Details\r\n                    </h3>\r\n                    <div className=\"space-y-4\">\r\n                      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n                        <div>\r\n                          <h4 className=\"text-sm font-medium text-gray-500\">Student</h4>\r\n                          <p className=\"text-sm text-gray-900 mt-1\">\r\n                            {selectedReview.studentName || 'N/A'}\r\n                          </p>\r\n                        </div>\r\n                        <div>\r\n                          <h4 className=\"text-sm font-medium text-gray-500\">Procedure Type</h4>\r\n                          <p className=\"text-sm text-gray-900 mt-1\">\r\n                            {selectedReview.procedureType || 'N/A'}\r\n                          </p>\r\n                        </div>\r\n                        <div>\r\n                          <h4 className=\"text-sm font-medium text-gray-500\">Submission Date</h4>\r\n                          <p className=\"text-sm text-gray-900 mt-1\">\r\n                            {new Date(selectedReview.submittedDate).toLocaleString()}\r\n                          </p>\r\n                        </div>\r\n                        {selectedReview.status !== 'pending' && selectedReview.reviewedDate && (\r\n                          <div>\r\n                            <h4 className=\"text-sm font-medium text-gray-500\">Reviewed Date</h4>\r\n                            <p className=\"text-sm text-gray-900 mt-1\">\r\n                              {new Date(selectedReview.reviewedDate).toLocaleString()}\r\n                            </p>\r\n                          </div>\r\n                        )}\r\n                        <div>\r\n                          <h4 className=\"text-sm font-medium text-gray-500\">Status</h4>\r\n                          <span\r\n                            className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full mt-1 ${\r\n                              selectedReview.status === 'accepted'\r\n                                ? 'bg-[rgba(40,167,69,0.1)] text-[#28A745]'\r\n                                : selectedReview.status === 'denied'\r\n                                ? 'bg-red-100 text-red-800'\r\n                                : 'bg-[rgba(0,119,182,0.1)] text-[#0077B6]'\r\n                            }`}\r\n                          >\r\n                            {selectedReview.status}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Review Steps */}\r\n                      <div className=\"mt-6\">\r\n                        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Review Steps</h4>\r\n                        {selectedReview.reviewSteps && selectedReview.reviewSteps.length > 0 ? (\r\n                          <div className=\"bg-white rounded-lg border border-gray-200 overflow-hidden\">\r\n                            <div className=\"divide-y divide-gray-200\">\r\n                              {selectedReview.reviewSteps.map((step, index) => (\r\n                                <div\r\n                                  key={index}\r\n                                  className={`p-3 flex items-center justify-between ${\r\n                                    step.completed ? 'bg-orange-50' : 'bg-white'\r\n                                  }`}\r\n                                >\r\n                                  <div className=\"flex items-center\">\r\n                                    <span className=\"inline-block w-6 text-center mr-2 text-blue-600 font-bold\">{index + 1}.</span>\r\n                                    <span className=\"text-sm font-medium text-gray-900\">{step.description}</span>\r\n                                  </div>\r\n                                  <div>\r\n                                    {step.completed ? (\r\n                                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-600\">\r\n                                        <FaCheckCircle className=\"h-3 w-3 mr-1\" />\r\n                                        Pending\r\n                                      </span>\r\n                                    ) : (\r\n                                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600\">\r\n                                        <FaTimesCircle className=\"h-3 w-3 mr-1\" />\r\n                                        Not Done\r\n                                      </span>\r\n                                    )}\r\n                                  </div>\r\n                                </div>\r\n                              ))}\r\n                            </div>\r\n                          </div>\r\n                        ) : (\r\n                          <p className=\"text-sm text-gray-500\">No review steps available</p>\r\n                        )}\r\n                      </div>\r\n\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500\">Note</h4>\r\n                        <p className=\"text-sm text-gray-900 mt-1 p-3 bg-white rounded border border-gray-200\">\r\n                          {selectedReview.note || 'No notes provided'}\r\n                        </p>\r\n                      </div>\r\n\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500\">Comment</h4>\r\n                        <p className=\"text-sm text-gray-900 mt-1\">\r\n                          {selectedReview.comment || 'N/A'}\r\n                        </p>\r\n                      </div>\r\n\r\n                      {selectedReview.chartId && (\r\n                        <div>\r\n                          <h4 className=\"text-sm font-medium text-gray-500\">Dental Chart</h4>\r\n                          <a\r\n                            href={`/api/dental/chart/${selectedReview.chartId}/pdf`}\r\n                            target=\"_blank\"\r\n                            rel=\"noopener noreferrer\"\r\n                            className=\"text-sm text-blue-600 flex items-center mt-1\"\r\n                          >\r\n                            <FaFilePdf className=\"h-5 w-5 mr-2 text-red-500\" />\r\n                            View Chart PDF\r\n                          </a>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Supervisor Feedback */}\r\n                  <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\">\r\n                    <h3 className=\"text-lg font-semibold text-[#0077B6] mb-4\">Supervisor Feedback</h3>\r\n                    {selectedReview.status === 'pending' ? (\r\n                      <p className=\"text-sm text-gray-700\">Awaiting supervisor review...</p>\r\n                    ) : (\r\n                      <div className=\"space-y-4\">\r\n                        <div>\r\n                          <h4 className=\"text-sm font-medium text-gray-500\">Supervisor</h4>\r\n                          <p className=\"text-sm text-gray-900 mt-1\">\r\n                            {selectedReview.supervisorName || 'N/A'} (ID: {selectedReview.supervisorId?._id || 'N/A'})\r\n                          </p>\r\n                        </div>\r\n                        <div>\r\n                          <h4 className=\"text-sm font-medium text-gray-500\">Status</h4>\r\n                          <p className=\"text-sm text-gray-900 mt-1 capitalize\">\r\n                            {selectedReview.status === 'accepted' ? 'Accepted' :\r\n                             selectedReview.status === 'denied' ? 'Declined' :\r\n                             selectedReview.status || 'N/A'}\r\n                          </p>\r\n                        </div>\r\n                        <div>\r\n                          <h4 className=\"text-sm font-medium text-gray-500\">Procedure Quality</h4>\r\n                          {renderStars(selectedReview.procedureQuality)}\r\n                        </div>\r\n                        <div>\r\n                          <h4 className=\"text-sm font-medium text-gray-500\">Patient Interaction</h4>\r\n                          {renderStars(selectedReview.patientInteraction)}\r\n                        </div>\r\n                        <div>\r\n                          <h4 className=\"text-sm font-medium text-gray-500\">Supervisor Note</h4>\r\n                          <p className=\"text-sm text-gray-900 mt-1\">\r\n                            {selectedReview.note || 'No notes provided'}\r\n                          </p>\r\n                        </div>\r\n                        {/* Supervisor Signature */}\r\n                        {selectedReview.supervisorSignature && (\r\n                          <div className=\"mt-4 pt-4 border-t border-gray-200\">\r\n                            <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Supervisor Signature</h4>\r\n                            <div className=\"border border-gray-300 rounded-lg bg-white p-4 flex justify-center\">\r\n                              {selectedReview.supervisorSignature.startsWith('data:image') ? (\r\n                                <img src={selectedReview.supervisorSignature} alt=\"Signature\" className=\"max-h-20\" />\r\n                              ) : (\r\n                                <p className=\"font-signature text-lg\">{selectedReview.supervisorSignature}</p>\r\n                              )}\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Reviews;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,MAAM,EAACC,QAAQ,EAAEC,aAAa,EAAEC,SAAS,EAAEC,WAAW,EAAEC,aAAa,EAAEC,aAAa,EAAEC,eAAe,EAC5GC,QAAQ,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3E,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EACpB,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6B,IAAI;IAAEC;EAAM,CAAC,GAAG3B,OAAO,CAAC,CAAC;EACjC,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4C,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoD,SAAS,EAAEC,YAAY,CAAC,GAAGrD,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACsD,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACwD,eAAe,EAAEC,kBAAkB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0D,YAAY,EAAEC,eAAe,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4D,eAAe,EAAEC,kBAAkB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAI7DC,SAAS,CAAC,MAAM;IACd,MAAM6D,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI,CAAC9B,IAAI,IAAI,CAACC,KAAK,EAAE;QACnBM,QAAQ,CAAC,qCAAqC,CAAC;QAC/CR,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MACA,IAAIC,IAAI,CAAC+B,IAAI,KAAK,SAAS,IAAI,CAAC/B,IAAI,CAACgC,SAAS,EAAE;QAC9CzB,QAAQ,CAAC,gCAAgC,CAAC;QAC1CR,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEA,IAAI;QACFM,UAAU,CAAC,IAAI,CAAC;QAChB4B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAElC,IAAI,CAACgC,SAAS,CAAC;QAE5D,MAAMG,MAAM,GAAG;UAAEC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUpC,KAAK;UAAG;QAAE,CAAC;QAChE,MAAMqC,QAAQ,GAAG,MAAMjE,KAAK,CAACkE,GAAG,CAAC,uDAAuDvC,IAAI,CAACgC,SAAS,EAAE,EAAEG,MAAM,CAAC;QACjH,MAAMK,OAAO,GAAGF,QAAQ,CAACG,IAAI;QAE7BR,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEI,QAAQ,CAAC;QAE3C,IAAI,CAACI,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,EAAE;UAC3BP,OAAO,CAAC3B,KAAK,CAAC,0BAA0B,EAAEkC,OAAO,CAAC;UAClD,MAAM,IAAII,KAAK,CAAC,oDAAoD,CAAC;QACvE;QAEAX,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEM,OAAO,CAAC;;QAExC;QACA,MAAMK,eAAe,GAAGL,OAAO,CAACM,MAAM,CAACC,CAAC;UAAA,IAAAC,YAAA;UAAA,OACtCD,CAAC,CAACE,aAAa,KAAK,mBAAmB,IACvC,EAAAD,YAAA,GAAAD,CAAC,CAACG,SAAS,cAAAF,YAAA,uBAAXA,YAAA,CAAaG,UAAU,MAAK,mBAAmB;QAAA,CACjD,CAAC;QAED1C,iBAAiB,CAACoC,eAAe,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACK,MAAM,KAAK,SAAS,CAAC,CAAC;QACtEzC,cAAc,CAACkC,eAAe,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACK,MAAM,KAAK,SAAS,CAAC,CAAC;;QAEnE;QACA,MAAMC,cAAc,GAAG,CAAC,CAAC;QACzBR,eAAe,CAACS,OAAO,CAACC,MAAM,IAAI;UAChC,MAAMC,IAAI,GAAGD,MAAM,CAACN,aAAa,IAAI,SAAS;UAC9C,IAAI,CAACI,cAAc,CAACG,IAAI,CAAC,EAAE;YACzBH,cAAc,CAACG,IAAI,CAAC,GAAG;cACrBC,KAAK,EAAE,CAAC;cACRC,QAAQ,EAAE,CAAC;cACXC,MAAM,EAAE,CAAC;cACTC,OAAO,EAAE;YACX,CAAC;UACH;UACAP,cAAc,CAACG,IAAI,CAAC,CAACC,KAAK,EAAE;UAC5B,IAAIF,MAAM,CAACH,MAAM,KAAK,UAAU,EAAEC,cAAc,CAACG,IAAI,CAAC,CAACE,QAAQ,EAAE,CAAC,KAC7D,IAAIH,MAAM,CAACH,MAAM,KAAK,QAAQ,EAAEC,cAAc,CAACG,IAAI,CAAC,CAACG,MAAM,EAAE,CAAC,KAC9DN,cAAc,CAACG,IAAI,CAAC,CAACI,OAAO,EAAE;QACrC,CAAC,CAAC;;QAEF;QACArC,yBAAyB,CAAC8B,cAAc,CAAC;QAEzChD,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOwD,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;QACZjC,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,EAAEuD,GAAG,CAAC;QAC1C5B,OAAO,CAAC3B,KAAK,CAAC,gBAAgB,GAAAwD,aAAA,GAAED,GAAG,CAACvB,QAAQ,cAAAwB,aAAA,uBAAZA,aAAA,CAAcrB,IAAI,CAAC;QACnDR,OAAO,CAAC3B,KAAK,CAAC,kBAAkB,GAAAyD,cAAA,GAAEF,GAAG,CAACvB,QAAQ,cAAAyB,cAAA,uBAAZA,cAAA,CAAcX,MAAM,CAAC;QAEvD,IAAI,EAAAY,cAAA,GAAAH,GAAG,CAACvB,QAAQ,cAAA0B,cAAA,uBAAZA,cAAA,CAAcZ,MAAM,MAAK,GAAG,EAAE;UAChC7C,QAAQ,CAAC,uCAAuC,CAAC;UACjDR,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,MAAM,IAAI,EAAAkE,cAAA,GAAAJ,GAAG,CAACvB,QAAQ,cAAA2B,cAAA,uBAAZA,cAAA,CAAcb,MAAM,MAAK,GAAG,EAAE;UACvC7C,QAAQ,CAAC,6CAA6C,CAAC;QACzD,CAAC,MAAM,IAAI,EAAA2D,cAAA,GAAAL,GAAG,CAACvB,QAAQ,cAAA4B,cAAA,uBAAZA,cAAA,CAAcd,MAAM,MAAK,GAAG,EAAE;UACvC;UACAnB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrDzB,iBAAiB,CAAC,EAAE,CAAC;UACrBE,cAAc,CAAC,EAAE,CAAC;UAClBY,yBAAyB,CAAC,CAAC,CAAC,CAAC;UAC7BlB,UAAU,CAAC,KAAK,CAAC;QACnB,CAAC,MAAM;UAAA,IAAA8D,cAAA,EAAAC,mBAAA;UACL7D,QAAQ,CAAC,EAAA4D,cAAA,GAAAN,GAAG,CAACvB,QAAQ,cAAA6B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc1B,IAAI,cAAA2B,mBAAA,uBAAlBA,mBAAA,CAAoBC,OAAO,KAAI,iDAAiD,CAAC;QAC5F;QACAhE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDyB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAAC9B,IAAI,EAAEC,KAAK,EAAEF,QAAQ,CAAC,CAAC;EAE3B,MAAMuE,aAAa,GAAI9B,OAAO,IAAK;IACjC,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,EAAE,OAAO,EAAE;IAEtC,IAAI+B,QAAQ,GAAG/B,OAAO;;IAEtB;IACA,MAAMgC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,IAAI3D,SAAS,KAAK,OAAO,EAAE;MACzByD,QAAQ,GAAGA,QAAQ,CAACzB,MAAM,CAACC,CAAC,IAAI;QAC9B,MAAM2B,UAAU,GAAG,IAAID,IAAI,CAAC1B,CAAC,CAAC4B,aAAa,CAAC;QAC5C,OAAOD,UAAU,CAACE,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC;MAC3D,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI9D,SAAS,KAAK,UAAU,EAAE;MACnC,MAAM+D,QAAQ,GAAG,IAAIJ,IAAI,CAACD,KAAK,CAAC;MAChCK,QAAQ,CAACC,OAAO,CAACD,QAAQ,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MACxCR,QAAQ,GAAGA,QAAQ,CAACzB,MAAM,CAACC,CAAC,IAAI;QAC9B,MAAM2B,UAAU,GAAG,IAAID,IAAI,CAAC1B,CAAC,CAAC4B,aAAa,CAAC;QAC5C,OAAOD,UAAU,CAACE,YAAY,CAAC,CAAC,KAAKC,QAAQ,CAACD,YAAY,CAAC,CAAC;MAC9D,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI9D,SAAS,KAAK,MAAM,EAAE;MAC/B,MAAMkE,OAAO,GAAG,IAAIP,IAAI,CAACD,KAAK,CAAC;MAC/BQ,OAAO,CAACF,OAAO,CAACE,OAAO,CAACD,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MACtCR,QAAQ,GAAGA,QAAQ,CAACzB,MAAM,CAACC,CAAC,IAAI;QAC9B,MAAM2B,UAAU,GAAG,IAAID,IAAI,CAAC1B,CAAC,CAAC4B,aAAa,CAAC;QAC5C,OAAOD,UAAU,IAAIF,KAAK,IAAIE,UAAU,IAAIM,OAAO;MACrD,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIlE,SAAS,KAAK,OAAO,EAAE;MAChC,MAAMmE,UAAU,GAAG,IAAIR,IAAI,CAACD,KAAK,CAACU,WAAW,CAAC,CAAC,EAAEV,KAAK,CAACW,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACrE,MAAMC,SAAS,GAAG,IAAIX,IAAI,CAACD,KAAK,CAACU,WAAW,CAAC,CAAC,EAAEV,KAAK,CAACW,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;MACxEZ,QAAQ,GAAGA,QAAQ,CAACzB,MAAM,CAACC,CAAC,IAAI;QAC9B,MAAM2B,UAAU,GAAG,IAAID,IAAI,CAAC1B,CAAC,CAAC4B,aAAa,CAAC;QAC5C,OAAOD,UAAU,IAAIO,UAAU,IAAIP,UAAU,GAAGU,SAAS;MAC3D,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIpE,UAAU,EAAE;MACduD,QAAQ,GAAGA,QAAQ,CAACzB,MAAM,CAACC,CAAC,IAAI;QAC9B,MAAM2B,UAAU,GAAG,IAAID,IAAI,CAAC1B,CAAC,CAAC4B,aAAa,CAAC;QAC5C,MAAMU,KAAK,GAAGX,UAAU,CAACY,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAC/D,OAAO,GAAGH,KAAK,KAAK,KAAKrE,UAAU;MACrC,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIQ,eAAe,IAAIA,eAAe,KAAK,KAAK,EAAE;MAChD+C,QAAQ,GAAGA,QAAQ,CAACzB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACE,aAAa,KAAKzB,eAAe,CAAC;IACtE;;IAEA;IACA,IAAIJ,SAAS,KAAK,MAAM,IAAIM,YAAY,IAAIA,YAAY,KAAK,KAAK,EAAE;MAClE6C,QAAQ,GAAGA,QAAQ,CAACzB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACK,MAAM,KAAK1B,YAAY,CAAC;IAC5D;;IAEA;IACA,IAAIR,WAAW,EAAE;MACfqD,QAAQ,GAAGA,QAAQ,CAACzB,MAAM,CAACC,CAAC;QAAA,IAAA0C,aAAA,EAAAC,qBAAA,EAAAC,gBAAA;QAAA,OAC1B,EAAAF,aAAA,GAAC1C,CAAC,CAACG,SAAS,cAAAuC,aAAA,wBAAAC,qBAAA,GAAXD,aAAA,CAAaG,QAAQ,cAAAF,qBAAA,uBAArBA,qBAAA,CAAuBG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5E,WAAW,CAAC2E,WAAW,CAAC,CAAC,CAAC,OAAAF,gBAAA,GACxE5C,CAAC,CAACE,aAAa,cAAA0C,gBAAA,uBAAfA,gBAAA,CAAiBE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5E,WAAW,CAAC2E,WAAW,CAAC,CAAC,CAAC,CAAC;MAAA,CACtE,CAAC;IACH;;IAEA;IACA,OAAOtB,QAAQ,CAACwB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIxB,IAAI,CAACwB,CAAC,CAACtB,aAAa,CAAC,GAAG,IAAIF,IAAI,CAACuB,CAAC,CAACrB,aAAa,CAAC,CAAC;EACvF,CAAC;;EAED;EACA,MAAMuB,YAAY,GAAG1F,cAAc,CAAC2F,MAAM,GAAGzF,WAAW,CAACyF,MAAM;EAC/D,MAAMC,eAAe,GAAG1F,WAAW,CAACoC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACK,MAAM,KAAK,UAAU,CAAC,CAAC+C,MAAM;EAC/E,MAAME,aAAa,GAAG3F,WAAW,CAACoC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACK,MAAM,KAAK,QAAQ,CAAC,CAAC+C,MAAM;EAC3E,MAAMG,mBAAmB,GAAG9F,cAAc,CAAC2F,MAAM;EAEjD,MAAMI,WAAW,GAAIC,MAAM,iBACzBjH,OAAA;IAAKkH,SAAS,EAAC,MAAM;IAAAC,QAAA,EAClB,CAAC,GAAGhE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACiE,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtBtH,OAAA,CAACb,MAAM;MAEL+H,SAAS,EAAE,WAAWI,CAAC,IAAIL,MAAM,IAAI,CAAC,CAAC,GAAG,iBAAiB,GAAG,eAAe;IAAG,GAD3EK,CAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEP,CACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,MAAMC,iBAAiB,GAAI3D,MAAM,IAAK;IACpC1C,iBAAiB,CAAC0C,MAAM,CAAC;IACzB1B,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMsF,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MACJD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QAAEC,eAAe,EAAE;MAAI;IACrC;EACF,CAAC;EAED,MAAMC,IAAI,GAAG;IACXL,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAG,CAAC;IAC7BJ,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAItH,OAAO,EAAE;IACX,oBAAOb,OAAA,CAACd,MAAM;MAAAqI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;EAEA,oBACE1H,OAAA;IAAKkH,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvCnH,OAAA,CAACf,OAAO;MAACmJ,MAAM,EAAEzH,WAAY;MAAC0H,SAAS,EAAEzH;IAAe;MAAA2G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC3D1H,OAAA;MAAKkH,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDnH,OAAA,CAAChB,MAAM;QAACsJ,aAAa,EAAEA,CAAA,KAAM1H,cAAc,CAAC,CAACD,WAAW;MAAE;QAAA4G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7D1H,OAAA;QAAMkH,SAAS,EAAC,mFAAmF;QAAAC,QAAA,eACjGnH,OAAA;UAAKkH,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAC/BpG,KAAK,iBACJf,OAAA,CAACnB,MAAM,CAAC0J,GAAG;YACTC,OAAO,EAAE;cAAEV,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCM,OAAO,EAAE;cAAEX,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE;YAAE,CAAE;YAC9BjB,SAAS,EAAC,+FAA+F;YAAAC,QAAA,eAEzGnH,OAAA;cAAKkH,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCnH,OAAA;gBAAKkH,SAAS,EAAC,2BAA2B;gBAACwB,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAxB,QAAA,eAChFnH,OAAA;kBACE4I,QAAQ,EAAC,SAAS;kBAClBC,CAAC,EAAC,yNAAyN;kBAC3NC,QAAQ,EAAC;gBAAS;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1H,OAAA;gBAAGkH,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAEpG;cAAK;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAED1H,OAAA,CAACnB,MAAM,CAAC0J,GAAG;YAACC,OAAO,EAAE;cAAEV,OAAO,EAAE;YAAE,CAAE;YAACW,OAAO,EAAE;cAAEX,OAAO,EAAE;YAAE,CAAE;YAACE,UAAU,EAAE;cAAEe,QAAQ,EAAE;YAAI,CAAE;YAAA5B,QAAA,gBAC1FnH,OAAA;cAAKkH,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/FnH,OAAA;gBAAAmH,QAAA,gBACEnH,OAAA;kBAAIkH,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAAU;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClF1H,OAAA;kBAAGkH,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAA+B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACN1H,OAAA,CAACnB,MAAM,CAACmK,MAAM;gBAACC,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAACC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAA/B,QAAA,eACpEnH,OAAA,CAACrB,IAAI;kBACHyK,EAAE,EAAC,YAAY;kBACflC,SAAS,EAAC,oMAAoM;kBAAAC,QAAA,gBAE9MnH,OAAA,CAACT,WAAW;oBAAC2H,SAAS,EAAC;kBAAc;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,qBAE1C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eAGN1H,OAAA,CAACnB,MAAM,CAAC0J,GAAG;cACTc,QAAQ,EAAEzB,SAAU;cACpBY,OAAO,EAAC,QAAQ;cAChBc,WAAW,EAAC,MAAM;cAClBC,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBtC,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAEhBnH,OAAA;gBAAKkH,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDnH,OAAA;kBAAKkH,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCnH,OAAA;oBAAKkH,SAAS,EAAC,uFAAuF;oBAAAC,QAAA,eACpGnH,OAAA,CAACH,gBAAgB;sBAACqH,SAAS,EAAC;oBAAwB;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACN1H,OAAA;oBAAIkH,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAc;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CAAC,eACN1H,OAAA,CAACnB,MAAM,CAAC0J,GAAG;kBAACU,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAACC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAAA/B,QAAA,eACjEnH,OAAA,CAACrB,IAAI;oBACHyK,EAAE,EAAC,YAAY;oBACflC,SAAS,EAAC,gKAAgK;oBAAAC,QAAA,gBAE1KnH,OAAA,CAACF,UAAU;sBAACoH,SAAS,EAAC;oBAAc;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,uBAEzC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGN1H,OAAA;gBAAKkH,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,gBACxEnH,OAAA,CAACnB,MAAM,CAAC0J,GAAG;kBACTc,QAAQ,EAAEnB,IAAK;kBACfhB,SAAS,EAAC,gJAAgJ;kBAAAC,QAAA,eAE1JnH,OAAA;oBAAKkH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChCnH,OAAA;sBAAKkH,SAAS,EAAC,yJAAyJ;sBAAAC,QAAA,eACtKnH,OAAA,CAACH,gBAAgB;wBAACqH,SAAS,EAAC;sBAAwB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACN1H,OAAA;sBAAAmH,QAAA,gBACEnH,OAAA;wBAAGkH,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAa;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAClE1H,OAAA;wBAAGkH,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAER;sBAAY;wBAAAY,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEb1H,OAAA,CAACnB,MAAM,CAAC0J,GAAG;kBACTc,QAAQ,EAAEnB,IAAK;kBACfhB,SAAS,EAAC,gJAAgJ;kBAAAC,QAAA,eAE1JnH,OAAA;oBAAKkH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChCnH,OAAA;sBAAKkH,SAAS,EAAC,gIAAgI;sBAAAC,QAAA,eAC7InH,OAAA,CAACR,aAAa;wBAAC0H,SAAS,EAAC;sBAAwB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC,eACN1H,OAAA;sBAAAmH,QAAA,gBACEnH,OAAA;wBAAGkH,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC7D1H,OAAA;wBAAGkH,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAEN;sBAAe;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEb1H,OAAA,CAACnB,MAAM,CAAC0J,GAAG;kBACTc,QAAQ,EAAEnB,IAAK;kBACfhB,SAAS,EAAC,gJAAgJ;kBAAAC,QAAA,eAE1JnH,OAAA;oBAAKkH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChCnH,OAAA;sBAAKkH,SAAS,EAAC,kIAAkI;sBAAAC,QAAA,eAC/InH,OAAA,CAACN,eAAe;wBAACwH,SAAS,EAAC;sBAAyB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACN1H,OAAA;sBAAAmH,QAAA,gBACEnH,OAAA;wBAAGkH,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAO;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC5D1H,OAAA;wBAAGkH,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,EAAEJ;sBAAmB;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEb1H,OAAA,CAACnB,MAAM,CAAC0J,GAAG;kBACTc,QAAQ,EAAEnB,IAAK;kBACfhB,SAAS,EAAC,gJAAgJ;kBAAAC,QAAA,eAE1JnH,OAAA;oBAAKkH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChCnH,OAAA;sBAAKkH,SAAS,EAAC,4HAA4H;sBAAAC,QAAA,eACzInH,OAAA,CAACP,aAAa;wBAACyH,SAAS,EAAC;sBAAsB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACN1H,OAAA;sBAAAmH,QAAA,gBACEnH,OAAA;wBAAGkH,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAM;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC3D1H,OAAA;wBAAGkH,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,EAAEL;sBAAa;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGb1H,OAAA,CAACnB,MAAM,CAAC0J,GAAG;cACTC,OAAO,EAAE;gBAAEV,OAAO,EAAE,CAAC;gBAAEK,CAAC,EAAE;cAAG,CAAE;cAC/BM,OAAO,EAAE;gBAAEX,OAAO,EAAE,CAAC;gBAAEK,CAAC,EAAE;cAAE,CAAE;cAC9BjB,SAAS,EAAC,wFAAwF;cAAAC,QAAA,eAElGnH,OAAA;gBAAKkH,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBnH,OAAA;kBACEyJ,OAAO,EAAEA,CAAA,KAAM3H,YAAY,CAAC,SAAS,CAAE;kBACvCoF,SAAS,EAAE,4GACTrF,SAAS,KAAK,SAAS,GACnB,2DAA2D,GAC3D,4GAA4G,EAC/G;kBAAAsF,QAAA,gBAEHnH,OAAA,CAACN,eAAe;oBAACwH,SAAS,EAAC;kBAAc;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,mBAE5C,eAAA1H,OAAA;oBAAMkH,SAAS,EAAE,mDACfrF,SAAS,KAAK,SAAS,GACnB,+BAA+B,GAC/B,2BAA2B,EAC9B;oBAAAsF,QAAA,EACAJ;kBAAmB;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACT1H,OAAA;kBACEyJ,OAAO,EAAEA,CAAA,KAAM3H,YAAY,CAAC,MAAM,CAAE;kBACpCoF,SAAS,EAAE,4GACTrF,SAAS,KAAK,MAAM,GAChB,2DAA2D,GAC3D,4GAA4G,EAC/G;kBAAAsF,QAAA,gBAEHnH,OAAA,CAACR,aAAa;oBAAC0H,SAAS,EAAC;kBAAc;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAE1C,eAAA1H,OAAA;oBAAMkH,SAAS,EAAE,mDACfrF,SAAS,KAAK,MAAM,GAChB,6BAA6B,GAC7B,2BAA2B,EAC9B;oBAAAsF,QAAA,EACAhG,WAAW,CAACyF;kBAAM;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGb1H,OAAA,CAACnB,MAAM,CAAC0J,GAAG;cACTC,OAAO,EAAE;gBAAEV,OAAO,EAAE,CAAC;gBAAEK,CAAC,EAAE;cAAG,CAAE;cAC/BM,OAAO,EAAE;gBAAEX,OAAO,EAAE,CAAC;gBAAEK,CAAC,EAAE;cAAE,CAAE;cAC9BH,UAAU,EAAE;gBAAE0B,KAAK,EAAE;cAAI,CAAE;cAC3BxC,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAEhBnH,OAAA;gBAAKkH,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCnH,OAAA,CAACL,QAAQ;kBAACuH,SAAS,EAAC;gBAAqB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5C1H,OAAA;kBAAIkH,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAO;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eAEN1H,OAAA;gBAAKkH,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBAEnEnH,OAAA;kBAAKkH,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBnH,OAAA;oBAAKkH,SAAS,EAAC,sEAAsE;oBAAAC,QAAA,eACnFnH,OAAA,CAACJ,QAAQ;sBAACsH,SAAS,EAAC;oBAAuB;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACN1H,OAAA;oBACEiE,IAAI,EAAC,MAAM;oBACX0F,WAAW,EAAC,qCAAqC;oBACjDC,KAAK,EAAEjI,WAAY;oBACnBkI,QAAQ,EAAGC,CAAC,IAAKlI,cAAc,CAACkI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAChD1C,SAAS,EAAC;kBAA4F;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGN1H,OAAA;kBAAAmH,QAAA,eACEnH,OAAA;oBACE4J,KAAK,EAAErI,SAAU;oBACjBsI,QAAQ,EAAGC,CAAC,IAAKtI,YAAY,CAACsI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC9C1C,SAAS,EAAC,sFAAsF;oBAAAC,QAAA,gBAEhGnH,OAAA;sBAAQ4J,KAAK,EAAC,KAAK;sBAAAzC,QAAA,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtC1H,OAAA;sBAAQ4J,KAAK,EAAC,OAAO;sBAAAzC,QAAA,EAAC;oBAAK;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpC1H,OAAA;sBAAQ4J,KAAK,EAAC,UAAU;sBAAAzC,QAAA,EAAC;oBAAQ;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC1C1H,OAAA;sBAAQ4J,KAAK,EAAC,MAAM;sBAAAzC,QAAA,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvC1H,OAAA;sBAAQ4J,KAAK,EAAC,OAAO;sBAAAzC,QAAA,EAAC;oBAAU;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAGN1H,OAAA;kBAAAmH,QAAA,eACEnH,OAAA;oBACE4J,KAAK,EAAEnI,UAAW;oBAClBoI,QAAQ,EAAGC,CAAC,IAAKpI,aAAa,CAACoI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC/C1C,SAAS,EAAC,sFAAsF;oBAAAC,QAAA,gBAEhGnH,OAAA;sBAAQ4J,KAAK,EAAC,EAAE;sBAAAzC,QAAA,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACnC1H,OAAA;sBAAQ4J,KAAK,EAAC,OAAO;sBAAAzC,QAAA,EAAC;oBAAQ;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvC1H,OAAA;sBAAQ4J,KAAK,EAAC,OAAO;sBAAAzC,QAAA,EAAC;oBAAQ;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvC1H,OAAA;sBAAQ4J,KAAK,EAAC,OAAO;sBAAAzC,QAAA,EAAC;oBAAQ;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvC1H,OAAA;sBAAQ4J,KAAK,EAAC,OAAO;sBAAAzC,QAAA,EAAC;oBAAQ;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvC1H,OAAA;sBAAQ4J,KAAK,EAAC,OAAO;sBAAAzC,QAAA,EAAC;oBAAQ;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvC1H,OAAA;sBAAQ4J,KAAK,EAAC,OAAO;sBAAAzC,QAAA,EAAC;oBAAQ;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvC1H,OAAA;sBAAQ4J,KAAK,EAAC,OAAO;sBAAAzC,QAAA,EAAC;oBAAQ;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAGN1H,OAAA;kBAAAmH,QAAA,eACEnH,OAAA;oBACE4J,KAAK,EAAE3H,eAAgB;oBACvB4H,QAAQ,EAAGC,CAAC,IAAK5H,kBAAkB,CAAC4H,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBACpD1C,SAAS,EAAC,sFAAsF;oBAAAC,QAAA,gBAEhGnH,OAAA;sBAAQ4J,KAAK,EAAC,KAAK;sBAAAzC,QAAA,EAAC;oBAAc;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC3C1H,OAAA;sBAAQ4J,KAAK,EAAC,WAAW;sBAAAzC,QAAA,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5C1H,OAAA;sBAAQ4J,KAAK,EAAC,sBAAsB;sBAAAzC,QAAA,EAAC;oBAAoB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAClE1H,OAAA;sBAAQ4J,KAAK,EAAC,0BAA0B;sBAAAzC,QAAA,EAAC;oBAAwB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC1E1H,OAAA;sBAAQ4J,KAAK,EAAC,aAAa;sBAAAzC,QAAA,EAAC;oBAAW;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAChD1H,OAAA;sBAAQ4J,KAAK,EAAC,cAAc;sBAAAzC,QAAA,EAAC;oBAAY;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAGL7F,SAAS,KAAK,MAAM,iBACnB7B,OAAA;kBAAAmH,QAAA,eACEnH,OAAA;oBACE4J,KAAK,EAAEzH,YAAa;oBACpB0H,QAAQ,EAAGC,CAAC,IAAK1H,eAAe,CAAC0H,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBACjD1C,SAAS,EAAC,sFAAsF;oBAAAC,QAAA,gBAEhGnH,OAAA;sBAAQ4J,KAAK,EAAC,KAAK;sBAAAzC,QAAA,EAAC;oBAAY;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACzC1H,OAAA;sBAAQ4J,KAAK,EAAC,UAAU;sBAAAzC,QAAA,EAAC;oBAAQ;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC1C1H,OAAA;sBAAQ4J,KAAK,EAAC,QAAQ;sBAAAzC,QAAA,EAAC;oBAAM;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACN,eAGD1H,OAAA;kBAAKkH,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,eAChCnH,OAAA;oBACEyJ,OAAO,EAAEA,CAAA,KAAM;sBACb7H,cAAc,CAAC,EAAE,CAAC;sBAClBJ,YAAY,CAAC,KAAK,CAAC;sBACnBE,aAAa,CAAC,EAAE,CAAC;sBACjBQ,kBAAkB,CAAC,KAAK,CAAC;sBACzBE,eAAe,CAAC,KAAK,CAAC;oBACxB,CAAE;oBACF8E,SAAS,EAAC,yHAAyH;oBAAAC,QAAA,EACpI;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGb1H,OAAA,CAACnB,MAAM,CAAC0J,GAAG;cACTc,QAAQ,EAAEzB,SAAU;cACpBY,OAAO,EAAC,QAAQ;cAChBc,WAAW,EAAC,MAAM;cAClBC,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBtC,SAAS,EAAC,kHAAkH;cAAAC,QAAA,eAE5HnH,OAAA;gBAAKkH,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBnH,OAAA;kBAAIkH,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAClDtF,SAAS,KAAK,SAAS,GAAG,iBAAiB,GAAG;gBAAc;kBAAA0F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACL1H,OAAA;kBAAKkH,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9BnH,OAAA;oBAAOkH,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,gBACpDnH,OAAA;sBAAOkH,SAAS,EAAC,YAAY;sBAAAC,QAAA,eAC3BnH,OAAA;wBAAAmH,QAAA,gBACEnH,OAAA;0BAAIkH,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC3FtF,SAAS,KAAK,SAAS,GAAG,gBAAgB,GAAG;wBAAe;0BAAA0F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3D,CAAC,eACL1H,OAAA;0BAAIkH,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAO;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3G1H,OAAA;0BAAIkH,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAS;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,EAC5G7F,SAAS,KAAK,MAAM,iBACnB7B,OAAA,CAAAE,SAAA;0BAAAiH,QAAA,gBACEnH,OAAA;4BAAIkH,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAAM;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAC1G1H,OAAA;4BAAIkH,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAAU;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA,eAC9G,CACH,eACD1H,OAAA;0BAAIkH,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAO;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACR1H,OAAA;sBAAOkH,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EACjDpC,aAAa,CAAClD,SAAS,KAAK,SAAS,GAAGZ,cAAc,GAAGE,WAAW,CAAC,CAACyF,MAAM,KAAK,CAAC,gBACjF5G,OAAA;wBAAAmH,QAAA,eACEnH,OAAA;0BAAIgK,OAAO,EAAEnI,SAAS,KAAK,SAAS,GAAG,CAAC,GAAG,CAAE;0BAACqF,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,eAC7EnH,OAAA;4BAAKkH,SAAS,EAAC,2CAA2C;4BAAAC,QAAA,gBACxDnH,OAAA;8BAAKkH,SAAS,EAAC,8BAA8B;8BAACwB,IAAI,EAAC,MAAM;8BAACC,OAAO,EAAC,WAAW;8BAACsB,MAAM,EAAC,cAAc;8BAAA9C,QAAA,eACjGnH,OAAA;gCAAMkK,aAAa,EAAC,OAAO;gCAACC,cAAc,EAAC,OAAO;gCAACC,WAAW,EAAE,CAAE;gCAACvB,CAAC,EAAC;8BAAiI;gCAAAtB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACtM,CAAC,eACN1H,OAAA;8BAAIkH,SAAS,EAAC,mCAAmC;8BAAAC,QAAA,GAAC,KAC7C,EAACtF,SAAS,KAAK,SAAS,GAAG,SAAS,GAAG,WAAW,EAAC,UACxD;4BAAA;8BAAA0F,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACL1H,OAAA;8BAAGkH,SAAS,EAAC,oBAAoB;8BAAAC,QAAA,EAAC;4BAAsC;8BAAAI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,GAEL3C,aAAa,CAAClD,SAAS,KAAK,SAAS,GAAGZ,cAAc,GAAGE,WAAW,CAAC,CAACiG,GAAG,CAAEpD,MAAM;wBAAA,IAAAqG,iBAAA;wBAAA,oBAC/ErK,OAAA,CAACnB,MAAM,CAACyL,EAAE;0BAER9B,OAAO,EAAE;4BAAEV,OAAO,EAAE;0BAAE,CAAE;0BACxBW,OAAO,EAAE;4BAAEX,OAAO,EAAE;0BAAE,CAAE;0BACxBZ,SAAS,EAAC,iCAAiC;0BAC3CuC,OAAO,EAAEA,CAAA,KAAMnI,iBAAiB,CAAC0C,MAAM,CAAE;0BAAAmD,QAAA,gBAEzCnH,OAAA;4BAAIkH,SAAS,EAAC,+DAA+D;4BAAAC,QAAA,EAC1EtF,SAAS,KAAK,SAAS,GACpB,IAAIqD,IAAI,CAAClB,MAAM,CAACoB,aAAa,CAAC,CAACmF,kBAAkB,CAAC,CAAC,GACnD,IAAIrF,IAAI,CAAClB,MAAM,CAACwG,YAAY,IAAIxG,MAAM,CAACoB,aAAa,CAAC,CAACmF,kBAAkB,CAAC;0BAAC;4BAAAhD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAE5E,CAAC,eACL1H,OAAA;4BAAIkH,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAC9D,EAAAkD,iBAAA,GAAArG,MAAM,CAACL,SAAS,cAAA0G,iBAAA,uBAAhBA,iBAAA,CAAkBhE,QAAQ,KAAI;0BAAK;4BAAAkB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClC,CAAC,eACL1H,OAAA;4BAAIkH,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAC9DnD,MAAM,CAACN,aAAa,IAAI;0BAAK;4BAAA6D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC5B,CAAC,EACJ7F,SAAS,KAAK,MAAM,iBACnB7B,OAAA,CAAAE,SAAA;4BAAAiH,QAAA,gBACEnH,OAAA;8BAAIkH,SAAS,EAAC,6BAA6B;8BAAAC,QAAA,eACzCnH,OAAA;gCACEkH,SAAS,EAAE,sEACTlD,MAAM,CAACH,MAAM,KAAK,UAAU,GAAG,yCAAyC,GAAG,yBAAyB,EACnG;gCAAAsD,QAAA,EAEFnD,MAAM,CAACH,MAAM,KAAK,UAAU,GAAG,UAAU,GAAGG,MAAM,CAACH,MAAM,KAAK,QAAQ,GAAG,UAAU,GAAGG,MAAM,CAACH;8BAAM;gCAAA0D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAChG;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,eACL1H,OAAA;8BAAIkH,SAAS,EAAC,mDAAmD;8BAAAC,QAAA,EAC9DnD,MAAM,CAACyG,cAAc,IAAI;4BAAK;8BAAAlD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC7B,CAAC;0BAAA,eACL,CACH,eACD1H,OAAA;4BAAIkH,SAAS,EAAC,oDAAoD;4BAAAC,QAAA,eAChEnH,OAAA;8BAAQyJ,OAAO,EAAEA,CAAA,KAAMnI,iBAAiB,CAAC0C,MAAM,CAAE;8BAACkD,SAAS,EAAC,kDAAkD;8BAAAC,QAAA,EAAC;4BAAI;8BAAAI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1H,CAAC;wBAAA,GApCA1D,MAAM,CAAC0G,GAAG;0BAAAnD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAqCN,CAAC;sBAAA,CACb;oBACF;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAELrG,cAAc,iBACbrB,OAAA;MAAKkH,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FnH,OAAA,CAACnB,MAAM,CAAC0J,GAAG;QACTC,OAAO,EAAE;UAAEU,KAAK,EAAE,GAAG;UAAEpB,OAAO,EAAE;QAAE,CAAE;QACpCW,OAAO,EAAE;UAAES,KAAK,EAAE,CAAC;UAAEpB,OAAO,EAAE;QAAE,CAAE;QAClCZ,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAEzFnH,OAAA;UAAKkH,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBnH,OAAA;YAAKkH,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDnH,OAAA;cAAIkH,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrE1H,OAAA;cAAQyJ,OAAO,EAAEA,CAAA,KAAMnI,iBAAiB,CAAC,IAAI,CAAE;cAAC4F,SAAS,EAAC,gEAAgE;cAAAC,QAAA,eACxHnH,OAAA;gBAAKkH,SAAS,EAAC,SAAS;gBAACwB,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACsB,MAAM,EAAC,cAAc;gBAAA9C,QAAA,eAC5EnH,OAAA;kBAAMkK,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACvB,CAAC,EAAC;gBAAsB;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAEL,CAACrG,cAAc,gBACdrB,OAAA;YAAGkH,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAyB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,gBAElE1H,OAAA;YAAKkH,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAExBnH,OAAA;cAAKkH,SAAS,EAAC,8EAA8E;cAAAC,QAAA,gBAC3FnH,OAAA;gBAAIkH,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,gBACzEnH,OAAA,CAACZ,QAAQ;kBAAC8H,SAAS,EAAC;gBAA6B;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uBAEtD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1H,OAAA;gBAAKkH,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDnH,OAAA;kBAAAmH,QAAA,gBACEnH,OAAA;oBAAIkH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3D1H,OAAA;oBAAGkH,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EACtC,EAAA9G,qBAAA,GAAAgB,cAAc,CAACsC,SAAS,cAAAtD,qBAAA,uBAAxBA,qBAAA,CAA0BgG,QAAQ,KAAI;kBAAK;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN1H,OAAA;kBAAAmH,QAAA,gBACEnH,OAAA;oBAAIkH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAW;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClE1H,OAAA;oBAAGkH,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EACtC,EAAA7G,sBAAA,GAAAe,cAAc,CAACsC,SAAS,cAAArD,sBAAA,uBAAxBA,sBAAA,CAA0BsD,UAAU,KAAI;kBAAK;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN1H,OAAA;cAAKkH,SAAS,EAAC,8EAA8E;cAAAC,QAAA,gBAC3FnH,OAAA;gBAAIkH,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,gBACzEnH,OAAA,CAACX,aAAa;kBAAC6H,SAAS,EAAC;gBAA6B;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,kBAE3D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1H,OAAA;gBAAKkH,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBnH,OAAA;kBAAKkH,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDnH,OAAA;oBAAAmH,QAAA,gBACEnH,OAAA;sBAAIkH,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAO;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9D1H,OAAA;sBAAGkH,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EACtC9F,cAAc,CAACsJ,WAAW,IAAI;oBAAK;sBAAApD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACN1H,OAAA;oBAAAmH,QAAA,gBACEnH,OAAA;sBAAIkH,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAc;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrE1H,OAAA;sBAAGkH,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EACtC9F,cAAc,CAACqC,aAAa,IAAI;oBAAK;sBAAA6D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACN1H,OAAA;oBAAAmH,QAAA,gBACEnH,OAAA;sBAAIkH,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAe;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtE1H,OAAA;sBAAGkH,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EACtC,IAAIjC,IAAI,CAAC7D,cAAc,CAAC+D,aAAa,CAAC,CAACwF,cAAc,CAAC;oBAAC;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,EACLrG,cAAc,CAACwC,MAAM,KAAK,SAAS,IAAIxC,cAAc,CAACmJ,YAAY,iBACjExK,OAAA;oBAAAmH,QAAA,gBACEnH,OAAA;sBAAIkH,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAa;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpE1H,OAAA;sBAAGkH,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EACtC,IAAIjC,IAAI,CAAC7D,cAAc,CAACmJ,YAAY,CAAC,CAACI,cAAc,CAAC;oBAAC;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CACN,eACD1H,OAAA;oBAAAmH,QAAA,gBACEnH,OAAA;sBAAIkH,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAM;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7D1H,OAAA;sBACEkH,SAAS,EAAE,2EACT7F,cAAc,CAACwC,MAAM,KAAK,UAAU,GAChC,yCAAyC,GACzCxC,cAAc,CAACwC,MAAM,KAAK,QAAQ,GAClC,yBAAyB,GACzB,yCAAyC,EAC5C;sBAAAsD,QAAA,EAEF9F,cAAc,CAACwC;oBAAM;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN1H,OAAA;kBAAKkH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBnH,OAAA;oBAAIkH,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAY;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACvErG,cAAc,CAACwJ,WAAW,IAAIxJ,cAAc,CAACwJ,WAAW,CAACjE,MAAM,GAAG,CAAC,gBAClE5G,OAAA;oBAAKkH,SAAS,EAAC,4DAA4D;oBAAAC,QAAA,eACzEnH,OAAA;sBAAKkH,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EACtC9F,cAAc,CAACwJ,WAAW,CAACzD,GAAG,CAAC,CAAC0D,IAAI,EAAEC,KAAK,kBAC1C/K,OAAA;wBAEEkH,SAAS,EAAE,yCACT4D,IAAI,CAACE,SAAS,GAAG,cAAc,GAAG,UAAU,EAC3C;wBAAA7D,QAAA,gBAEHnH,OAAA;0BAAKkH,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,gBAChCnH,OAAA;4BAAMkH,SAAS,EAAC,2DAA2D;4BAAAC,QAAA,GAAE4D,KAAK,GAAG,CAAC,EAAC,GAAC;0BAAA;4BAAAxD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC/F1H,OAAA;4BAAMkH,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAE2D,IAAI,CAACG;0BAAW;4BAAA1D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1E,CAAC,eACN1H,OAAA;0BAAAmH,QAAA,EACG2D,IAAI,CAACE,SAAS,gBACbhL,OAAA;4BAAMkH,SAAS,EAAC,uGAAuG;4BAAAC,QAAA,gBACrHnH,OAAA,CAACR,aAAa;8BAAC0H,SAAS,EAAC;4BAAc;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,WAE5C;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,gBAEP1H,OAAA;4BAAMkH,SAAS,EAAC,mGAAmG;4BAAAC,QAAA,gBACjHnH,OAAA,CAACP,aAAa;8BAACyH,SAAS,EAAC;4BAAc;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,YAE5C;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM;wBACP;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA,GArBDqD,KAAK;wBAAAxD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAsBP,CACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEN1H,OAAA;oBAAGkH,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAyB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAClE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEN1H,OAAA;kBAAAmH,QAAA,gBACEnH,OAAA;oBAAIkH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3D1H,OAAA;oBAAGkH,SAAS,EAAC,wEAAwE;oBAAAC,QAAA,EAClF9F,cAAc,CAAC6J,IAAI,IAAI;kBAAmB;oBAAA3D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAEN1H,OAAA;kBAAAmH,QAAA,gBACEnH,OAAA;oBAAIkH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9D1H,OAAA;oBAAGkH,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EACtC9F,cAAc,CAAC8J,OAAO,IAAI;kBAAK;oBAAA5D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,EAELrG,cAAc,CAAC+J,OAAO,iBACrBpL,OAAA;kBAAAmH,QAAA,gBACEnH,OAAA;oBAAIkH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAY;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnE1H,OAAA;oBACEqL,IAAI,EAAE,qBAAqBhK,cAAc,CAAC+J,OAAO,MAAO;oBACxDrB,MAAM,EAAC,QAAQ;oBACfuB,GAAG,EAAC,qBAAqB;oBACzBpE,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,gBAExDnH,OAAA,CAACV,SAAS;sBAAC4H,SAAS,EAAC;oBAA2B;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,kBAErD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN1H,OAAA;cAAKkH,SAAS,EAAC,8EAA8E;cAAAC,QAAA,gBAC3FnH,OAAA;gBAAIkH,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,EAAC;cAAmB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACjFrG,cAAc,CAACwC,MAAM,KAAK,SAAS,gBAClC7D,OAAA;gBAAGkH,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA6B;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,gBAEtE1H,OAAA;gBAAKkH,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBnH,OAAA;kBAAAmH,QAAA,gBACEnH,OAAA;oBAAIkH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAU;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjE1H,OAAA;oBAAGkH,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,GACtC9F,cAAc,CAACoJ,cAAc,IAAI,KAAK,EAAC,QAAM,EAAC,EAAAlK,qBAAA,GAAAc,cAAc,CAACkK,YAAY,cAAAhL,qBAAA,uBAA3BA,qBAAA,CAA6BmK,GAAG,KAAI,KAAK,EAAC,GAC3F;kBAAA;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN1H,OAAA;kBAAAmH,QAAA,gBACEnH,OAAA;oBAAIkH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7D1H,OAAA;oBAAGkH,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EACjD9F,cAAc,CAACwC,MAAM,KAAK,UAAU,GAAG,UAAU,GACjDxC,cAAc,CAACwC,MAAM,KAAK,QAAQ,GAAG,UAAU,GAC/CxC,cAAc,CAACwC,MAAM,IAAI;kBAAK;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN1H,OAAA;kBAAAmH,QAAA,gBACEnH,OAAA;oBAAIkH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACvEV,WAAW,CAAC3F,cAAc,CAACmK,gBAAgB,CAAC;gBAAA;kBAAAjE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACN1H,OAAA;kBAAAmH,QAAA,gBACEnH,OAAA;oBAAIkH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACzEV,WAAW,CAAC3F,cAAc,CAACoK,kBAAkB,CAAC;gBAAA;kBAAAlE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACN1H,OAAA;kBAAAmH,QAAA,gBACEnH,OAAA;oBAAIkH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtE1H,OAAA;oBAAGkH,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EACtC9F,cAAc,CAAC6J,IAAI,IAAI;kBAAmB;oBAAA3D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,EAELrG,cAAc,CAACqK,mBAAmB,iBACjC1L,OAAA;kBAAKkH,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,gBACjDnH,OAAA;oBAAIkH,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChF1H,OAAA;oBAAKkH,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,EAChF9F,cAAc,CAACqK,mBAAmB,CAACC,UAAU,CAAC,YAAY,CAAC,gBAC1D3L,OAAA;sBAAK4L,GAAG,EAAEvK,cAAc,CAACqK,mBAAoB;sBAACG,GAAG,EAAC,WAAW;sBAAC3E,SAAS,EAAC;oBAAU;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAErF1H,OAAA;sBAAGkH,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,EAAE9F,cAAc,CAACqK;oBAAmB;sBAAAnE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAC9E;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtH,EAAA,CApzBID,OAAO;EAAA,QACMvB,WAAW,EACJG,OAAO;AAAA;AAAA+M,EAAA,GAF3B3L,OAAO;AAszBb,eAAeA,OAAO;AAAC,IAAA2L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}