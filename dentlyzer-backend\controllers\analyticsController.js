// controllers/analyticsController.js
const Patient = require('../models/Patient');
const Appointment = require('../models/Appointment');

const getAnalyticsData = async (req, res) => {
  try {
    const studentId = req.user.id;
    const { timeRange = 'month' } = req.query;

    // Calculate date range
    const now = new Date();
    let startDate;
    switch (timeRange) {
      case 'week':
        startDate = new Date(now.setDate(now.getDate() - 7));
        break;
      case 'year':
        startDate = new Date(now.setFullYear(now.getFullYear() - 1));
        break;
      case 'month':
      default:
        startDate = new Date(now.setMonth(now.getMonth() - 1));
    }

    // Procedure counts based on treatment sheets
    const treatmentSheets = await Patient.aggregate([
      { $match: { drId: studentId }},
      { $unwind: '$treatmentSheets' },
      { $match: {
        'treatmentSheets.createdAt': { $gte: startDate }
      }},
      { $group: {
        _id: '$treatmentSheets.type',
        count: { $sum: 1 }
      }}
    ]);

    const procedures = treatmentSheets.map(sheet => ({
      name: sheet._id || 'Unknown',
      count: sheet.count
    }));

    // Patient demographics
    const patients = await Patient.aggregate([
      { $match: { drId: studentId }},
      { $group: {
        _id: {
          $switch: {
            branches: [
              { case: { $lte: ['$age', 12] }, then: 'Children' },
              { case: { $lte: ['$age', 19] }, then: 'Adolescents' },
              { case: { $lte: ['$age', 64] }, then: 'Adults' }
            ],
            default: 'Seniors'
          }
        },
        value: { $sum: 1 }
      }},
      { $project: { name: '$_id', value: 1, _id: 0 }}
    ]);

    // Performance metrics based on treatment sheets
    const sheetsSubmitted = await Patient.aggregate([
      { $match: { drId: studentId }},
      { $unwind: '$treatmentSheets' },
      { $match: {
        'treatmentSheets.createdAt': { $gte: startDate }
      }},
      { $count: 'total' }
    ]);

    const performanceMetrics = {
      averageProcedureTime: await getAverageProcedureTime(studentId, startDate),
      studentAccuracy: await getStudentAccuracy(studentId, startDate),
      commonIssues: await getCommonIssues(studentId, startDate),
      sheetsSubmitted: sheetsSubmitted[0]?.total || 0,
      casesCompleted: await Appointment.countDocuments({
        doctor: studentId,
        status: 'completed',
        date: { $gte: startDate }
      }),
      casesReferred: await Appointment.countDocuments({
        doctor: studentId,
        status: 'referred',
        date: { $gte: startDate }
      })
    };

    // Time series data
    const timeSeries = await getTimeSeriesData(studentId, timeRange);

    res.json({
      procedures,
      patientDemographics: patients,
      performanceMetrics,
      timeSeries
    });
  } catch (error) {
    console.error('Error fetching analytics:', error);
    res.status(500).json({ message: 'Server error fetching analytics' });
  }
};

const getAverageProcedureTime = async (studentId, startDate) => {
  const result = await Appointment.aggregate([
    { $match: { doctor: studentId, status: 'completed', date: { $gte: startDate }}},
    { $group: { 
      _id: null,
      avgTime: { $avg: { $divide: [{ $subtract: ['$endTime', '$startTime'] }, 60000] }}
    }}
  ]);
  return result[0]?.avgTime ? `${Math.round(result[0].avgTime)} mins` : 'N/A';
};

const getStudentAccuracy = async (studentId, startDate) => {
  // Assuming evaluations are stored in appointments
  const result = await Appointment.aggregate([
    { $match: { doctor: studentId, status: 'completed', date: { $gte: startDate }}},
    { $group: { 
      _id: null,
      avgAccuracy: { $avg: '$evaluation.accuracy' } // Assuming accuracy is a field 0-100
    }}
  ]);
  return result[0]?.avgAccuracy ? `${Math.round(result[0].avgAccuracy)}%` : 'N/A';
};

const getCommonIssues = async (studentId, startDate) => {
  const result = await Appointment.aggregate([
    { $match: { doctor: studentId, date: { $gte: startDate }}},
    { $unwind: '$evaluation.issues' },
    { $group: { 
      _id: '$evaluation.issues',
      count: { $sum: 1 }
    }},
    { $sort: { count: -1 }},
    { $limit: 3 }
  ]);
  return result.map(r => r._id).filter(Boolean);
};

const getTimeSeriesData = async (studentId, timeRange) => {
  const groupBy = timeRange === 'year' ? '$year' : '$month';

  // Get treatment sheet data
  const sheetData = await Patient.aggregate([
    { $match: { drId: studentId }},
    { $unwind: '$treatmentSheets' },
    { $group: {
      _id: {
        [groupBy]: { $month: '$treatmentSheets.createdAt' },
        ...(timeRange === 'year' && { year: { $year: '$treatmentSheets.createdAt' }})
      },
      sheets: { $sum: 1 },
      patients: { $addToSet: '$nationalId' }
    }},
    { $project: {
      month: { $arrayElemAt: [
        ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        { $subtract: ['$_id.month', 1] }
      ]},
      sheets: 1,
      patients: { $size: '$patients' }
    }},
    { $sort: { '_id.month': 1 }}
  ]);

  // Get appointment data for comparison
  const appointmentData = await Appointment.aggregate([
    { $match: { doctor: studentId, status: 'completed' }},
    { $group: {
      _id: {
        [groupBy]: { $month: '$date' },
        ...(timeRange === 'year' && { year: { $year: '$date' }})
      },
      procedures: { $sum: 1 },
      patients: { $addToSet: '$patientId' }
    }},
    { $project: {
      month: { $arrayElemAt: [
        ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        { $subtract: ['$_id.month', 1] }
      ]},
      procedures: 1,
      patients: { $size: '$patients' }
    }},
    { $sort: { '_id.month': 1 }}
  ]);

  // Combine the data
  return {
    sheets: sheetData,
    appointments: appointmentData
  };
};

module.exports = { getAnalyticsData };