import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import PatientNav from './PatientNav';
import Loader from '../components/Loader';
import { FaCheck, FaUser, FaCheckCircle, FaTimesCircle, FaSave, FaSpinner } from 'react-icons/fa';

// CSS for spinner animation
const spinnerAnimation = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  .animate-spin {
    animation: spin 1s linear infinite;
  }
`;

const ReviewSteps = () => {
  const { nationalId } = useParams();
  const navigate = useNavigate();
  const { user, token } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [patientData, setPatientData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [reviewData, setReviewData] = useState({
    patientId: { nationalId: '', fullName: '' },
    procedureType: '',
    chartId: '',
    note: '',
    reviewSteps: [],
    subType: '' // For Removable Prosthodontics, Fixed Prosthodontics, or Operative subtypes
  });

  // Get the selected sheet type from localStorage (set by PatientNav)
  useEffect(() => {
    const savedSheetType = localStorage.getItem('selectedSheetType');
    if (savedSheetType) {
      setReviewData(prev => ({
        ...prev,
        procedureType: savedSheetType
      }));

      // Fetch review steps for this procedure type
      fetchReviewSteps(savedSheetType);
    }
  }, []);

  // Listen for changes to the selected sheet type in localStorage
  useEffect(() => {
    const handleStorageChange = () => {
      const savedSheetType = localStorage.getItem('selectedSheetType');
      if (savedSheetType && savedSheetType !== reviewData.procedureType) {
        setReviewData(prev => ({
          ...prev,
          procedureType: savedSheetType,
          reviewSteps: [] // Clear previous steps
        }));

        // Fetch review steps for this procedure type
        fetchReviewSteps(savedSheetType);
      }
    };

    // Add event listener for storage changes
    window.addEventListener('storage', handleStorageChange);

    // Clean up
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [reviewData.procedureType]);

  const fetchReviewSteps = async (procedureType, subType = '') => {
    // Validate procedure type
    if (!procedureType || procedureType.trim() === '') {
      setError('Please select a valid procedure type');
      return;
    }

    try {
      // Show loading state
      setLoading(true);

      // Add subType as a query parameter if it's provided
      const url = `http://localhost:5000/api/reviews/steps/${procedureType}${subType ? `?subType=${encodeURIComponent(subType)}` : ''}`;

      const response = await axios.get(url, {
        headers: { Authorization: `Bearer ${token}` },
      });

      // Check if we got valid data back
      if (!response.data || !Array.isArray(response.data) || response.data.length === 0) {
        setError(`No review steps found for ${procedureType}${subType ? ` (${subType})` : ''}. Please try another procedure type.`);
        return;
      }

      // Update state with the fetched steps and subType
      setReviewData(prev => ({
        ...prev,
        reviewSteps: response.data,
        subType: subType || prev.subType
      }));

      // Clear any previous errors
      setError(null);
    } catch (err) {
      console.error('Error fetching review steps:', err);

      // Provide more specific error messages based on the error
      if (err.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        if (err.response.status === 401) {
          setError('Your session has expired. Please log in again.');
        } else if (err.response.status === 404) {
          setError(`No review steps found for ${procedureType}. Please try another procedure type.`);
        } else {
          setError(`Server error: ${err.response.data.message || 'Failed to load review steps'}`);
        }
      } else if (err.request) {
        // The request was made but no response was received
        setError('Network error. Please check your connection and try again.');
      } else {
        // Something happened in setting up the request that triggered an Error
        setError('Failed to load review steps. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Check if user is logged in and is a student
        if (!token || !user) {
          console.log('No token or user found');
          setError('Please log in to submit a review');
          navigate('/login');
          return;
        }

        if (user.role !== 'student') {
          console.log('User is not a student:', user.role);
          setError('Only students can submit reviews');
          navigate('/dashboard');
          return;
        }

        // Fetch patient data
        const patientResponse = await axios.get(`http://localhost:5000/api/patients/public/${nationalId}`);
        setPatientData(patientResponse.data);
        setReviewData((prev) => ({
          ...prev,
          patientId: { nationalId: patientResponse.data.nationalId, fullName: patientResponse.data.fullName },
        }));

        setLoading(false);
      } catch (err) {
        console.error('API Error:', {
          message: err.message,
          status: err.response?.status,
          data: err.response?.data,
        });

        // Handle different error scenarios
        if (err.response?.status === 404) {
          setError(`Patient with ID ${nationalId} not found. Please check the patient ID.`);
        } else if (err.response?.status === 401) {
          setError('Session expired or invalid. Please log in again.');
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          navigate('/login');
        } else if (err.response?.status === 403) {
          setError('You do not have permission to access this resource.');
          navigate('/dashboard');
        } else {
          setError(err.response?.data?.message || 'Failed to load patient data');
        }
        setLoading(false);
      }
    };
    fetchData();
  }, [nationalId, navigate, user, token]);

  const handleStepToggle = (index) => {
    const updatedSteps = [...reviewData.reviewSteps];
    updatedSteps[index].completed = !updatedSteps[index].completed;
    setReviewData(prev => ({
      ...prev,
      reviewSteps: updatedSteps
    }));
  };

  const handleNoteChange = (e) => {
    setReviewData(prev => ({
      ...prev,
      note: e.target.value
    }));
  };

  const handleSubmitReview = async () => {
    // Validate required fields
    if (!reviewData.procedureType || reviewData.procedureType.trim() === '') {
      setError('Please select a procedure type before submitting');
      return;
    }

    // Validate subType for Removable Prosthodontics
    if (reviewData.procedureType === 'Removable Prosthodontics' && !reviewData.subType) {
      setError('Please select either Complete Denture or Partial Denture before submitting');
      return;
    }

    // Validate subType for Fixed Prosthodontics
    if (reviewData.procedureType === 'Fixed Prosthodontics' && !reviewData.subType) {
      setError('Please select either Crown Procedure or Bridge Procedure before submitting');
      return;
    }

    // Validate subType for Operative
    if (reviewData.procedureType === 'Operative' && !reviewData.subType) {
      setError('Please select either Amalgam Restoration or Composite Restoration before submitting');
      return;
    }

    if (!reviewData.reviewSteps || reviewData.reviewSteps.length === 0) {
      setError('No review steps available. Please select a different procedure type');
      return;
    }

    if (!reviewData.patientId || !reviewData.patientId.nationalId) {
      setError('Patient information is missing. Please refresh the page and try again');
      return;
    }

    // Check if any steps are completed
    const completedSteps = reviewData.reviewSteps.filter(step => step.completed);
    if (completedSteps.length === 0) {
      setError('Please complete at least one review step before submitting');
      return;
    }

    try {
      // Show loading state
      setLoading(true);

      // Create a payload with all required fields
      const payload = {
        patientId: reviewData.patientId,
        procedureType: reviewData.procedureType,
        note: reviewData.note || '',
        reviewSteps: reviewData.reviewSteps || [],
        comment: '', // Add empty comment field
      };

      // Add subType to the payload if it's a Removable Prosthodontics, Fixed Prosthodontics, or Operative review
      if ((reviewData.procedureType === 'Removable Prosthodontics' ||
           reviewData.procedureType === 'Fixed Prosthodontics' ||
           reviewData.procedureType === 'Operative') && reviewData.subType) {
        payload.procedureType = `${reviewData.procedureType} - ${reviewData.subType}`;
      }

      console.log('Submitting review:', payload);
      const response = await axios.post('http://localhost:5000/api/reviews', payload, {
        headers: { Authorization: `Bearer ${token}` },
      });

      console.log('Review submitted:', response.data);

      // Clear any errors and show success message
      setError(null);
      setSuccess(true);
    } catch (err) {
      console.error('Submit Review Error:', err);

      // More detailed error logging and user-friendly messages
      if (err.response) {
        console.error('Response data:', err.response.data);
        console.error('Response status:', err.response.status);
        console.error('Response headers:', err.response.headers);

        if (err.response.status === 401) {
          setError('Your session has expired. Please log in again.');
        } else if (err.response.status === 400) {
          setError(`Validation error: ${err.response.data.message || 'Please check your submission'}`);
        } else if (err.response.status === 404) {
          setError('Student information not found. Please log in again.');
        } else {
          setError(`Server error: ${err.response.data.message || 'Failed to submit review'}`);
        }
      } else if (err.request) {
        console.error('Request made but no response received:', err.request);
        setError('Network error. Please check your connection and try again.');
      } else {
        console.error('Error setting up request:', err.message);
        setError('Failed to submit review. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  if (loading) {
    return <Loader />;
  }

  // Only show full-page error for critical errors that prevent the component from functioning
  if (error && (error.includes('session') || error.includes('log in') || error.includes('permission'))) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white flex items-center justify-center">
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="text-center max-w-md bg-white p-8 rounded-xl shadow-sm border border-gray-100"
            >
              <div className="text-[#0077B6] mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-12 w-12 mx-auto"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">Authentication Error</h3>
              <p className="text-gray-600 mb-6">{error}</p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate('/login')}
                className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium shadow-md"
              >
                Go to Login
              </motion.button>
            </motion.div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Add style tag for animations */}
      <style>{spinnerAnimation}</style>
      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <PatientNav />
        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white">
          <div className="max-w-4xl mx-auto">
            <motion.div variants={container} initial="hidden" animate="show">
              <div className="flex justify-between items-center mb-6">
                <h1 className="text-3xl font-bold text-[#0077B6]">Review Steps</h1>
                <div className="text-sm text-gray-600">
                  {new Date().toLocaleDateString()} {new Date().toLocaleTimeString()}
                </div>
              </div>

              {/* Inline Error Message */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-red-50 border-l-4 border-red-400 p-4 mb-6"
                >
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-5 w-5 text-red-400"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-[#333333]">{error}</p>
                    </div>
                    <div className="ml-auto pl-3">
                      <div className="-mx-1.5 -my-1.5">
                        <button
                          onClick={() => setError(null)}
                          className="inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none"
                        >
                          <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path
                              fillRule="evenodd"
                              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {success ? (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-[#28A745]/10 p-6 rounded-xl shadow-sm mb-8 border border-[#28A745]/20 text-center"
                >
                  <FaCheck className="h-12 w-12 text-[#28A745] mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Review Submitted Successfully</h3>
                  <p className="text-gray-600 mb-2">Your {reviewData.procedureType} review has been saved successfully.</p>
                  <p className="text-gray-600 mb-6">It will be available in your history and sent for supervisor approval.</p>
                  <div className="flex justify-center gap-4">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => {
                        // Reset form and fetch new steps
                        setReviewData({
                          patientId: {
                            nationalId: patientData.nationalId,
                            fullName: patientData.fullName,
                          },
                          procedureType: reviewData.procedureType,
                          chartId: '',
                          note: '',
                          reviewSteps: []
                        });
                        fetchReviewSteps(reviewData.procedureType);
                        setSuccess(false);
                      }}
                      className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90"
                    >
                      Submit Another Review
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => navigate(`/patientprofile/${nationalId}`)}
                      className="px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50"
                    >
                      Back to Patient Profile
                    </motion.button>
                  </div>
                </motion.div>
              ) : (
                <>
                  {/* Basic Info */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10"
                  >
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Patient Name</h4>
                        <p className="text-sm font-semibold text-gray-900">{patientData?.fullName || 'N/A'}</p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">National ID</h4>
                        <p className="text-sm font-semibold text-gray-900">{patientData?.nationalId || 'N/A'}</p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Student Name</h4>
                        <p className="text-sm font-semibold text-gray-900">{user?.name || 'N/A'}</p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Student ID</h4>
                        <p className="text-sm font-semibold text-gray-900">{user?.studentId || 'N/A'}</p>
                      </div>
                    </div>
                  </motion.div>

                  {/* Procedure Type Selector */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10"
                  >
                    <h3 className="text-lg font-semibold text-[#0077B6] mb-4">
                      Select Procedure Type
                    </h3>
                    <div className="relative">
                      <select
                        value={reviewData.procedureType || ''}
                        onChange={(e) => {
                          const newProcedureType = e.target.value;

                          if (!newProcedureType) {
                            // Handle empty selection
                            setError('Please select a valid procedure type');
                            return;
                          }

                          // Clear any previous errors
                          setError(null);

                          // Update localStorage
                          localStorage.setItem('selectedSheetType', newProcedureType);

                          // Update state
                          setReviewData(prev => ({
                            ...prev,
                            procedureType: newProcedureType,
                            reviewSteps: [] // Clear previous steps
                          }));

                          // Fetch new steps
                          fetchReviewSteps(newProcedureType);
                        }}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] appearance-none"
                      >
                        <option value="">Select a procedure type</option>
                        <option value="Operative">Operative</option>
                        <option value="Fixed Prosthodontics">Fixed Prosthodontics</option>
                        <option value="Removable Prosthodontics">Removable Prosthodontics</option>
                        <option value="Endodontics">Endodontics</option>
                        <option value="Periodontics">Periodontics</option>
                      </select>
                      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                        <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                          <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                        </svg>
                      </div>
                    </div>
                  </motion.div>

                  {/* Removable Prosthodontics Type Selector */}
                  {reviewData.procedureType === 'Removable Prosthodontics' && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10"
                    >
                      <h3 className="text-lg font-semibold text-[#0077B6] mb-4">
                        Select Denture Type
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <button
                          onClick={() => {
                            const subType = 'Complete Denture';
                            setReviewData(prev => ({ ...prev, subType }));
                            fetchReviewSteps(reviewData.procedureType, subType);
                          }}
                          className={`p-4 border rounded-lg flex flex-col items-center transition-all ${
                            reviewData.subType === 'Complete Denture'
                              ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md'
                              : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'
                          }`}
                        >
                          <span className="text-lg font-medium mb-2">Complete Denture</span>
                          <span className="text-sm text-gray-600 text-center">
                            Full denture evaluation for edentulous patients
                          </span>
                        </button>
                        <button
                          onClick={() => {
                            const subType = 'Partial Denture';
                            setReviewData(prev => ({ ...prev, subType }));
                            fetchReviewSteps(reviewData.procedureType, subType);
                          }}
                          className={`p-4 border rounded-lg flex flex-col items-center transition-all ${
                            reviewData.subType === 'Partial Denture'
                              ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md'
                              : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'
                          }`}
                        >
                          <span className="text-lg font-medium mb-2">Partial Denture</span>
                          <span className="text-sm text-gray-600 text-center">
                            Partial denture evaluation for partially edentulous patients
                          </span>
                        </button>
                      </div>
                    </motion.div>
                  )}

                  {/* Fixed Prosthodontics Type Selector */}
                  {reviewData.procedureType === 'Fixed Prosthodontics' && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10"
                    >
                      <h3 className="text-lg font-semibold text-[#0077B6] mb-4">
                        Select Procedure Type
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <button
                          onClick={() => {
                            const subType = 'Crown Procedure';
                            setReviewData(prev => ({ ...prev, subType }));
                            fetchReviewSteps(reviewData.procedureType, subType);
                          }}
                          className={`p-4 border rounded-lg flex flex-col items-center transition-all ${
                            reviewData.subType === 'Crown Procedure'
                              ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md'
                              : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'
                          }`}
                        >
                          <span className="text-lg font-medium mb-2">Crown Procedure</span>
                          <span className="text-sm text-gray-600 text-center">
                            Single crown preparation and placement
                          </span>
                        </button>
                        <button
                          onClick={() => {
                            const subType = 'Bridge Procedure';
                            setReviewData(prev => ({ ...prev, subType }));
                            fetchReviewSteps(reviewData.procedureType, subType);
                          }}
                          className={`p-4 border rounded-lg flex flex-col items-center transition-all ${
                            reviewData.subType === 'Bridge Procedure'
                              ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md'
                              : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'
                          }`}
                        >
                          <span className="text-lg font-medium mb-2">Bridge Procedure</span>
                          <span className="text-sm text-gray-600 text-center">
                            Bridge preparation and placement for multiple teeth
                          </span>
                        </button>
                      </div>
                    </motion.div>
                  )}

                  {/* Operative Type Selector */}
                  {reviewData.procedureType === 'Operative' && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10"
                    >
                      <h3 className="text-lg font-semibold text-[#0077B6] mb-4">
                        Select Restoration Type
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <button
                          onClick={() => {
                            const subType = 'Amalgam Restoration';
                            setReviewData(prev => ({ ...prev, subType }));
                            fetchReviewSteps(reviewData.procedureType, subType);
                          }}
                          className={`p-4 border rounded-lg flex flex-col items-center transition-all ${
                            reviewData.subType === 'Amalgam Restoration'
                              ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md'
                              : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'
                          }`}
                        >
                          <span className="text-lg font-medium mb-2">Amalgam Restoration</span>
                          <span className="text-sm text-gray-600 text-center">
                            Metallic restoration for posterior teeth
                          </span>
                        </button>
                        <button
                          onClick={() => {
                            const subType = 'Composite Restoration';
                            setReviewData(prev => ({ ...prev, subType }));
                            fetchReviewSteps(reviewData.procedureType, subType);
                          }}
                          className={`p-4 border rounded-lg flex flex-col items-center transition-all ${
                            reviewData.subType === 'Composite Restoration'
                              ? 'border-[#0077B6] bg-[#0077B6]/10 shadow-md'
                              : 'border-gray-200 hover:border-[#0077B6]/50 hover:bg-[#0077B6]/5'
                          }`}
                        >
                          <span className="text-lg font-medium mb-2">Composite Restoration</span>
                          <span className="text-sm text-gray-600 text-center">
                            Tooth-colored restoration for anterior or posterior teeth
                          </span>
                        </button>
                      </div>
                    </motion.div>
                  )}

                  {/* Review Steps */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10"
                  >
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-semibold text-[#0077B6]">
                        {(reviewData.procedureType === 'Removable Prosthodontics' ||
                          reviewData.procedureType === 'Fixed Prosthodontics' ||
                          reviewData.procedureType === 'Operative') && reviewData.subType
                          ? `${reviewData.procedureType}: ${reviewData.subType} Evaluation`
                          : `${reviewData.procedureType || 'Procedure'} Review Steps`}
                      </h3>

                      {reviewData.reviewSteps && reviewData.reviewSteps.length > 0 && (
                        <div className="flex items-center">
                          <div className="bg-gray-200 rounded-full h-2.5 w-32 mr-2">
                            <div
                              className="bg-[#0077B6] h-2.5 rounded-full"
                              style={{
                                width: `${(reviewData.reviewSteps.filter(step => step.completed).length / reviewData.reviewSteps.length) * 100}%`
                              }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-600">
                            {reviewData.reviewSteps.filter(step => step.completed).length}/{reviewData.reviewSteps.length} completed
                          </span>
                        </div>
                      )}
                    </div>

                    {!reviewData.procedureType ? (
                      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <svg
                              className="h-5 w-5 text-yellow-400"
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm text-yellow-700">
                              Please select a procedure type from the dropdown above.
                            </p>
                          </div>
                        </div>
                      </div>
                    ) : reviewData.procedureType === 'Removable Prosthodontics' && !reviewData.subType ? (
                      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <svg
                              className="h-5 w-5 text-yellow-400"
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm text-yellow-700">
                              Please select either Complete Denture or Partial Denture from the options above.
                            </p>
                          </div>
                        </div>
                      </div>
                    ) : reviewData.procedureType === 'Fixed Prosthodontics' && !reviewData.subType ? (
                      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <svg
                              className="h-5 w-5 text-yellow-400"
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm text-yellow-700">
                              Please select either Crown Procedure or Bridge Procedure from the options above.
                            </p>
                          </div>
                        </div>
                      </div>
                    ) : reviewData.procedureType === 'Operative' && !reviewData.subType ? (
                      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <svg
                              className="h-5 w-5 text-yellow-400"
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm text-yellow-700">
                              Please select either Amalgam Restoration or Composite Restoration from the options above.
                            </p>
                          </div>
                        </div>
                      </div>
                    ) : reviewData.reviewSteps && reviewData.reviewSteps.length > 0 ? (
                      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                        <div className="divide-y divide-gray-200">
                          {reviewData.reviewSteps.map((step, index) => (
                            <div
                              key={index}
                              className={`p-4 flex items-center justify-between transition-colors ${
                                step.completed ? 'bg-orange-50' : 'hover:bg-gray-50'
                              }`}
                            >
                              <div className="flex items-center">
                                <span className="text-sm font-medium text-gray-900">
                                  <span className="inline-block w-6 text-center mr-2 text-blue-600 font-bold">{index + 1}.</span>
                                  {step.description}
                                </span>
                              </div>
                              <button
                                onClick={() => handleStepToggle(index)}
                                className={`flex items-center justify-center w-20 h-8 rounded-full transition-colors ${
                                  step.completed
                                    ? 'bg-orange-500 text-white'
                                    : 'bg-gray-200 text-gray-500'
                                }`}
                              >
                                {step.completed ? (
                                  <>
                                    <FaCheckCircle className="h-4 w-4 mr-1" />
                                    <span className="text-xs font-medium">Pending</span>
                                  </>
                                ) : (
                                  <>
                                    <FaTimesCircle className="h-4 w-4 mr-1" />
                                    <span className="text-xs font-medium">Not Done</span>
                                  </>
                                )}
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <svg
                              className="h-5 w-5 text-yellow-400"
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm text-yellow-700">
                              No review steps available for this procedure type.
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </motion.div>

                  {/* Notes */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-white p-6 rounded-xl shadow-sm mb-6 border border-[#0077B6]/10"
                  >
                    <h3 className="text-lg font-semibold text-[#0077B6] mb-4">Additional Notes (Optional)</h3>
                    <textarea
                      placeholder="Add any additional notes about the review steps"
                      value={reviewData.note}
                      onChange={handleNoteChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6]"
                      rows="4"
                    />
                  </motion.div>

                  {/* Submit Button */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex justify-end"
                  >
                    <motion.button
                      whileHover={loading ? {} : { scale: 1.05 }}
                      whileTap={loading ? {} : { scale: 0.95 }}
                      onClick={handleSubmitReview}
                      disabled={
                        loading ||
                        !reviewData.procedureType ||
                        reviewData.reviewSteps.length === 0 ||
                        (reviewData.procedureType === 'Removable Prosthodontics' && !reviewData.subType) ||
                        (reviewData.procedureType === 'Fixed Prosthodontics' && !reviewData.subType) ||
                        (reviewData.procedureType === 'Operative' && !reviewData.subType)
                      }
                      className={`px-6 py-3 rounded-lg flex items-center ${
                        loading
                          ? 'bg-[#0077B6]/60 text-white cursor-wait'
                          : !reviewData.procedureType ||
                            reviewData.reviewSteps.length === 0 ||
                            (reviewData.procedureType === 'Removable Prosthodontics' && !reviewData.subType) ||
                            (reviewData.procedureType === 'Fixed Prosthodontics' && !reviewData.subType) ||
                            (reviewData.procedureType === 'Operative' && !reviewData.subType)
                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            : 'bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white shadow-md hover:shadow-lg'
                      }`}
                    >
                      {loading ? (
                        <>
                          <FaSpinner className="h-5 w-5 mr-2 animate-spin" />
                          Submitting...
                        </>
                      ) : (
                        <>
                          <FaSave className="h-5 w-5 mr-2" />
                          Submit Review
                        </>
                      )}
                    </motion.button>
                  </motion.div>
                </>
              )}
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default ReviewSteps;