{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\student\\\\Analytics.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useRef } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport Navbar from './Navbar';\nimport Sidebar from './Sidebar';\nimport Loader from '../components/Loader';\nimport { motion } from 'framer-motion';\nimport { FaUsers, FaTooth, FaCalendarAlt, FaClipboardCheck, FaCheckCircle, FaHourglassHalf, FaStarHalfAlt, FaStar, FaUserInjured, FaChartBar } from 'react-icons/fa';\nimport { Chart as ChartJS, CategoryScale, LinearScale, BarElement, ArcElement, PointElement, LineElement, RadialLinearScale, Title, Tooltip, Legend, Filler } from 'chart.js';\nimport { <PERSON>, Pie, Doughnut, Line, PolarArea, Radar } from 'react-chartjs-2';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, BarElement, ArcElement, PointElement, LineElement, RadialLinearScale, Title, Tooltip, Legend, Filler);\nconst Analytics = () => {\n  _s();\n  var _analyticsData$review, _analyticsData$review2, _analyticsData$review3, _analyticsData$review4, _analyticsData$review5, _analyticsData$review6, _analyticsData$review7, _analyticsData$review8, _analyticsData$review9, _analyticsData$review10, _analyticsData$review11, _analyticsData$review12, _analyticsData$review13, _analyticsData$review14, _analyticsData$review15, _analyticsData$review16, _analyticsData$review17, _analyticsData$review18, _analyticsData$review19, _analyticsData$review20, _analyticsData$review21, _analyticsData$review22, _analyticsData$review23, _analyticsData$review24, _analyticsData$review25, _analyticsData$review26, _analyticsData$review27, _analyticsData$review28, _analyticsData$review29, _analyticsData$review30, _analyticsData$review31, _analyticsData$review32, _analyticsData$review33, _analyticsData$review34, _analyticsData$review35, _analyticsData$review36, _analyticsData$review37, _analyticsData$review38, _analyticsData$review39, _analyticsData$review40, _analyticsData$review41, _analyticsData$review42, _analyticsData$review43, _analyticsData$review44;\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [timeRange, setTimeRange] = useState('all'); // 'week', 'month', 'all'\n  const [activeTab, setActiveTab] = useState('overview'); // 'overview', 'patients', 'procedures', 'reviews'\n  const [chartType, setChartType] = useState({\n    appointments: 'doughnut',\n    appointmentsPerMonth: 'line',\n    age: 'bar',\n    gender: 'pie',\n    procedures: 'bar',\n    diseases: 'doughnut',\n    reviewStatus: 'doughnut',\n    reviewProcedures: 'bar',\n    reviewTrend: 'line',\n    reviewQuality: 'radar'\n  });\n\n  // Chart references\n  const statusChartRef = useRef(null);\n  const procedureChartRef = useRef(null);\n  const reviewTrendChartRef = useRef(null);\n  const qualityMetricsChartRef = useRef(null);\n  const [analyticsData, setAnalyticsData] = useState({\n    appointmentStats: {\n      pending: 0,\n      completed: 0,\n      cancelled: 0\n    },\n    appointmentTypes: {},\n    ageDistribution: {},\n    genderDistribution: {},\n    procedureFrequency: {},\n    chronicDiseasePrevalence: {},\n    reviewStats: {\n      statusDistribution: {\n        accepted: 0,\n        pending: 0,\n        denied: 0\n      },\n      procedureTypeDistribution: {},\n      qualityMetrics: {\n        avgProcedureQuality: 0,\n        avgPatientInteraction: 0\n      },\n      reviewsByMonth: [],\n      acceptanceRate: 0,\n      denialRate: 0,\n      totalReviews: 0\n    }\n  });\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n  useEffect(() => {\n    const fetchAnalytics = async () => {\n      if (!user || !token) {\n        setError('Please log in to view analytics.');\n        setLoading(false);\n        return;\n      }\n      try {\n        const config = {\n          headers: {\n            Authorization: `Bearer ${token}`\n          },\n          params: {\n            range: timeRange\n          }\n        };\n\n        // Fetch general analytics data\n        const analyticsResponse = await axios.get('http://localhost:5000/api/analytics', config);\n\n        // Fetch reviews data separately\n        let reviewsData = {\n          statusDistribution: {\n            accepted: 0,\n            pending: 0,\n            denied: 0\n          },\n          procedureTypeDistribution: {},\n          qualityMetrics: {\n            avgProcedureQuality: 0,\n            avgPatientInteraction: 0,\n            avgDocumentation: 0,\n            avgTechnique: 0,\n            avgTimeManagement: 0,\n            avgCommunication: 0\n          },\n          reviewsByMonth: [],\n          acceptanceRate: 0,\n          denialRate: 0,\n          totalReviews: 0\n        };\n        if (user.studentId) {\n          try {\n            const reviewsResponse = await axios.get(`http://localhost:5000/api/reviews/student?studentId=${user.studentId}`, config);\n            const reviews = reviewsResponse.data;\n            if (Array.isArray(reviews) && reviews.length > 0) {\n              // Calculate review statistics\n              const totalReviews = reviews.length;\n              const pendingReviews = reviews.filter(r => r.status === 'pending');\n              const doneReviews = reviews.filter(r => r.status !== 'pending');\n              const acceptedReviews = doneReviews.filter(r => r.status === 'accepted');\n              const deniedReviews = doneReviews.filter(r => r.status === 'denied');\n              const acceptanceRate = totalReviews > 0 ? (acceptedReviews.length / totalReviews * 100).toFixed(1) : 0;\n              const denialRate = totalReviews > 0 ? (deniedReviews.length / totalReviews * 100).toFixed(1) : 0;\n\n              // Calculate average ratings\n              const avgProcedureQuality = doneReviews.length > 0 ? (doneReviews.reduce((sum, r) => sum + (r.procedureQuality || 0), 0) / doneReviews.length).toFixed(1) : 0;\n              const avgPatientInteraction = doneReviews.length > 0 ? (doneReviews.reduce((sum, r) => sum + (r.patientInteraction || 0), 0) / doneReviews.length).toFixed(1) : 0;\n\n              // Group reviews by procedure type\n              const procedureTypes = {};\n              reviews.forEach(review => {\n                const type = review.procedureType || 'Unknown';\n                if (!procedureTypes[type]) {\n                  procedureTypes[type] = {\n                    total: 0,\n                    accepted: 0,\n                    denied: 0,\n                    pending: 0\n                  };\n                }\n                procedureTypes[type].total++;\n                if (review.status === 'accepted') procedureTypes[type].accepted++;else if (review.status === 'denied') procedureTypes[type].denied++;else procedureTypes[type].pending++;\n              });\n\n              // Group reviews by month\n              const reviewsByMonth = [];\n              const monthsMap = {};\n              reviews.forEach(review => {\n                const date = new Date(review.submittedDate);\n                const monthYear = `${date.toLocaleString('default', {\n                  month: 'short'\n                })} ${date.getFullYear()}`;\n                if (!monthsMap[monthYear]) {\n                  monthsMap[monthYear] = {\n                    month: date.toLocaleString('default', {\n                      month: 'short'\n                    }),\n                    year: date.getFullYear(),\n                    total: 0,\n                    accepted: 0,\n                    pending: 0,\n                    denied: 0\n                  };\n                }\n                monthsMap[monthYear].total++;\n                if (review.status === 'accepted') monthsMap[monthYear].accepted++;else if (review.status === 'pending') monthsMap[monthYear].pending++;else monthsMap[monthYear].denied++;\n              });\n\n              // Convert to array and sort by date\n              Object.values(monthsMap).forEach(month => {\n                reviewsByMonth.push(month);\n              });\n              reviewsByMonth.sort((a, b) => {\n                if (a.year !== b.year) return a.year - b.year;\n                const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n                return months.indexOf(a.month) - months.indexOf(b.month);\n              });\n\n              // Update reviewsData\n              reviewsData = {\n                statusDistribution: {\n                  accepted: acceptedReviews.length,\n                  pending: pendingReviews.length,\n                  denied: deniedReviews.length\n                },\n                procedureTypeDistribution: procedureTypes,\n                qualityMetrics: {\n                  avgProcedureQuality: parseFloat(avgProcedureQuality),\n                  avgPatientInteraction: parseFloat(avgPatientInteraction),\n                  avgDocumentation: parseFloat(avgProcedureQuality),\n                  // Using procedure quality as a placeholder\n                  avgTechnique: parseFloat(avgProcedureQuality),\n                  // Using procedure quality as a placeholder\n                  avgTimeManagement: parseFloat(avgPatientInteraction),\n                  // Using patient interaction as a placeholder\n                  avgCommunication: parseFloat(avgPatientInteraction) // Using patient interaction as a placeholder\n                },\n                reviewsByMonth: reviewsByMonth,\n                acceptanceRate: parseFloat(acceptanceRate),\n                denialRate: parseFloat(denialRate),\n                totalReviews: totalReviews\n              };\n            }\n          } catch (err) {\n            console.error('Error fetching reviews:', err);\n            // Continue with default reviewsData if reviews fetch fails\n          }\n        }\n\n        // Transform data for better chart display\n        const transformedData = {\n          ...analyticsResponse.data,\n          ageDistribution: transformAgeData(analyticsResponse.data.ageDistribution),\n          chronicDiseasePrevalence: transformDiseaseData(analyticsResponse.data.chronicDiseasePrevalence),\n          // Transform procedures data from new backend format\n          procedureFrequency: analyticsResponse.data.procedures ? analyticsResponse.data.procedures.reduce((acc, proc) => {\n            acc[proc.name] = proc.count;\n            return acc;\n          }, {}) : {},\n          reviewStats: reviewsData\n        };\n        setAnalyticsData(transformedData);\n        setLoading(false);\n      } catch (err) {\n        console.error('Fetch analytics error:', err);\n        let errorMessage = 'Failed to load analytics data';\n        if (err.response) {\n          var _err$response$data;\n          if (err.response.status === 401) {\n            navigate('/login');\n            return;\n          }\n          errorMessage = ((_err$response$data = err.response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || errorMessage;\n        }\n        setError(errorMessage);\n        setLoading(false);\n      }\n    };\n    fetchAnalytics();\n  }, [user, token, navigate, timeRange]);\n\n  // Helper functions to transform data\n  const transformAgeData = ageData => {\n    return Object.entries(ageData).sort(([a], [b]) => parseInt(a.split('-')[0]) - parseInt(b.split('-')[0])).reduce((acc, [key, value]) => ({\n      ...acc,\n      [key]: value\n    }), {});\n  };\n  const transformDiseaseData = diseaseData => {\n    const sorted = Object.entries(diseaseData).sort(([, a], [, b]) => b - a).slice(0, 5); // Show top 5 only\n    return Object.fromEntries(sorted);\n  };\n\n  // Function to toggle chart types\n  const toggleChartType = chartKey => {\n    const chartTypes = {\n      appointments: ['doughnut', 'pie', 'bar'],\n      appointmentsPerMonth: ['line', 'bar', 'area'],\n      age: ['bar', 'line'],\n      gender: ['pie', 'doughnut', 'bar'],\n      procedures: ['bar', 'pie', 'doughnut'],\n      diseases: ['doughnut', 'pie', 'bar'],\n      reviewStatus: ['doughnut', 'pie', 'bar'],\n      reviewProcedures: ['bar', 'line'],\n      reviewTrend: ['line', 'bar'],\n      reviewQuality: ['radar', 'bar', 'polarArea']\n    };\n    const currentType = chartType[chartKey];\n    const types = chartTypes[chartKey];\n    const currentIndex = types.indexOf(currentType);\n    const nextIndex = (currentIndex + 1) % types.length;\n    setChartType({\n      ...chartType,\n      [chartKey]: types[nextIndex]\n    });\n  };\n\n  // Function to render stars for ratings\n  const renderStars = rating => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex\",\n    children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(FaStar, {\n      className: `h-5 w-5 ${i < (rating || 0) ? 'text-yellow-400' : 'text-gray-300'}`\n    }, i, false, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 315,\n    columnNumber: 5\n  }, this);\n\n  // Chart data configurations\n  const appointmentStatusData = {\n    labels: ['Pending', 'Completed', 'Cancelled'],\n    datasets: [{\n      data: [analyticsData.appointmentStats.pending, analyticsData.appointmentStats.completed, analyticsData.appointmentStats.cancelled],\n      backgroundColor: ['#FBBF24', '#10B981', '#EF4444'],\n      hoverBackgroundColor: ['#F59E0B', '#059669', '#DC2626']\n    }]\n  };\n  const appointmentTypesData = {\n    labels: Object.keys(analyticsData.appointmentTypes),\n    datasets: [{\n      data: Object.values(analyticsData.appointmentTypes),\n      backgroundColor: ['#3B82F6', '#10B981', '#FBBF24', '#EF4444', '#8B5CF6']\n    }]\n  };\n\n  // Appointments per month data\n  const appointmentsPerMonthData = {\n    labels: (analyticsData.appointmentsPerMonth || []).map(item => item.month) || [],\n    datasets: [{\n      label: 'Total Appointments',\n      data: (analyticsData.appointmentsPerMonth || []).map(item => item.total) || [],\n      backgroundColor: 'rgba(59, 130, 246, 0.5)',\n      borderColor: 'rgba(59, 130, 246, 1)',\n      borderWidth: 2,\n      tension: 0.4,\n      fill: true\n    }, {\n      label: 'Completed',\n      data: (analyticsData.appointmentsPerMonth || []).map(item => item.completed) || [],\n      backgroundColor: 'rgba(16, 185, 129, 0.5)',\n      borderColor: 'rgba(16, 185, 129, 1)',\n      borderWidth: 2,\n      tension: 0.4,\n      fill: false\n    }, {\n      label: 'Pending',\n      data: (analyticsData.appointmentsPerMonth || []).map(item => item.pending) || [],\n      backgroundColor: 'rgba(245, 158, 11, 0.5)',\n      borderColor: 'rgba(245, 158, 11, 1)',\n      borderWidth: 2,\n      tension: 0.4,\n      fill: false\n    }]\n  };\n  const ageDistributionData = {\n    labels: Object.keys(analyticsData.ageDistribution),\n    datasets: [{\n      label: 'Patients',\n      data: Object.values(analyticsData.ageDistribution),\n      backgroundColor: '#3B82F6',\n      borderColor: '#2563EB',\n      borderWidth: 1\n    }]\n  };\n  const genderDistributionData = {\n    labels: Object.keys(analyticsData.genderDistribution),\n    datasets: [{\n      data: Object.values(analyticsData.genderDistribution),\n      backgroundColor: ['#3B82F6', '#EC4899', '#6B7280'],\n      hoverBackgroundColor: ['#2563EB', '#DB2777', '#4B5563']\n    }]\n  };\n  const procedureFrequencyData = {\n    labels: Object.keys(analyticsData.procedureFrequency),\n    datasets: [{\n      label: 'Treatment Sheets',\n      data: Object.values(analyticsData.procedureFrequency),\n      backgroundColor: '#10B981',\n      borderColor: '#059669',\n      borderWidth: 1\n    }]\n  };\n  const chronicDiseaseData = {\n    labels: Object.keys(analyticsData.chronicDiseasePrevalence),\n    datasets: [{\n      data: Object.values(analyticsData.chronicDiseasePrevalence),\n      backgroundColor: ['#EF4444', '#FBBF24', '#3B82F6', '#8B5CF6', '#EC4899'],\n      hoverBackgroundColor: ['#DC2626', '#F59E0B', '#2563EB', '#7C3AED', '#DB2777']\n    }]\n  };\n\n  // Review status distribution data\n  const reviewStatusData = {\n    labels: ['Accepted', 'Pending', 'Denied'],\n    datasets: [{\n      data: [((_analyticsData$review = analyticsData.reviewStats) === null || _analyticsData$review === void 0 ? void 0 : (_analyticsData$review2 = _analyticsData$review.statusDistribution) === null || _analyticsData$review2 === void 0 ? void 0 : _analyticsData$review2.accepted) || 0, ((_analyticsData$review3 = analyticsData.reviewStats) === null || _analyticsData$review3 === void 0 ? void 0 : (_analyticsData$review4 = _analyticsData$review3.statusDistribution) === null || _analyticsData$review4 === void 0 ? void 0 : _analyticsData$review4.pending) || 0, ((_analyticsData$review5 = analyticsData.reviewStats) === null || _analyticsData$review5 === void 0 ? void 0 : (_analyticsData$review6 = _analyticsData$review5.statusDistribution) === null || _analyticsData$review6 === void 0 ? void 0 : _analyticsData$review6.denied) || 0],\n      backgroundColor: ['#10B981', '#F59E0B', '#EF4444'],\n      hoverBackgroundColor: ['#059669', '#D97706', '#DC2626'],\n      borderWidth: 1\n    }]\n  };\n\n  // Review procedure type distribution data\n  const reviewProcedureData = {\n    labels: Object.keys(((_analyticsData$review7 = analyticsData.reviewStats) === null || _analyticsData$review7 === void 0 ? void 0 : _analyticsData$review7.procedureTypeDistribution) || {}),\n    datasets: [{\n      label: 'Accepted',\n      data: Object.values(((_analyticsData$review8 = analyticsData.reviewStats) === null || _analyticsData$review8 === void 0 ? void 0 : _analyticsData$review8.procedureTypeDistribution) || {}).map(item => (item === null || item === void 0 ? void 0 : item.accepted) || 0),\n      backgroundColor: 'rgba(16, 185, 129, 0.7)',\n      borderColor: 'rgba(16, 185, 129, 1)',\n      borderWidth: 1\n    }, {\n      label: 'Pending',\n      data: Object.values(((_analyticsData$review9 = analyticsData.reviewStats) === null || _analyticsData$review9 === void 0 ? void 0 : _analyticsData$review9.procedureTypeDistribution) || {}).map(item => (item === null || item === void 0 ? void 0 : item.pending) || 0),\n      backgroundColor: 'rgba(245, 158, 11, 0.7)',\n      borderColor: 'rgba(245, 158, 11, 1)',\n      borderWidth: 1\n    }, {\n      label: 'Denied',\n      data: Object.values(((_analyticsData$review10 = analyticsData.reviewStats) === null || _analyticsData$review10 === void 0 ? void 0 : _analyticsData$review10.procedureTypeDistribution) || {}).map(item => (item === null || item === void 0 ? void 0 : item.denied) || 0),\n      backgroundColor: 'rgba(239, 68, 68, 0.7)',\n      borderColor: 'rgba(239, 68, 68, 1)',\n      borderWidth: 1\n    }]\n  };\n\n  // Review trend data (mock data for now)\n  const defaultMonths = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];\n  const defaultTotalData = [5, 8, 12, 15, 20, 25];\n  const defaultAcceptedData = [4, 6, 9, 12, 16, 20];\n  const reviewTrendData = {\n    labels: (((_analyticsData$review11 = analyticsData.reviewStats) === null || _analyticsData$review11 === void 0 ? void 0 : _analyticsData$review11.reviewsByMonth) || []).length > 0 ? (_analyticsData$review12 = analyticsData.reviewStats) === null || _analyticsData$review12 === void 0 ? void 0 : _analyticsData$review12.reviewsByMonth.map(item => item.month) : defaultMonths,\n    datasets: [{\n      label: 'Total Reviews',\n      data: (((_analyticsData$review13 = analyticsData.reviewStats) === null || _analyticsData$review13 === void 0 ? void 0 : _analyticsData$review13.reviewsByMonth) || []).length > 0 ? (_analyticsData$review14 = analyticsData.reviewStats) === null || _analyticsData$review14 === void 0 ? void 0 : _analyticsData$review14.reviewsByMonth.map(item => item.total) : defaultTotalData,\n      backgroundColor: 'rgba(59, 130, 246, 0.5)',\n      borderColor: 'rgba(59, 130, 246, 1)',\n      borderWidth: 2,\n      tension: 0.4,\n      fill: true\n    }, {\n      label: 'Accepted Reviews',\n      data: (((_analyticsData$review15 = analyticsData.reviewStats) === null || _analyticsData$review15 === void 0 ? void 0 : _analyticsData$review15.reviewsByMonth) || []).length > 0 ? (_analyticsData$review16 = analyticsData.reviewStats) === null || _analyticsData$review16 === void 0 ? void 0 : _analyticsData$review16.reviewsByMonth.map(item => item.accepted) : defaultAcceptedData,\n      backgroundColor: 'rgba(16, 185, 129, 0.5)',\n      borderColor: 'rgba(16, 185, 129, 1)',\n      borderWidth: 2,\n      tension: 0.4,\n      fill: false\n    }]\n  };\n\n  // Review quality metrics data\n  const reviewQualityData = {\n    labels: ['Procedure Quality', 'Patient Interaction', 'Documentation', 'Technique', 'Time Management', 'Communication'],\n    datasets: [{\n      label: 'Average Ratings',\n      data: [((_analyticsData$review17 = analyticsData.reviewStats) === null || _analyticsData$review17 === void 0 ? void 0 : (_analyticsData$review18 = _analyticsData$review17.qualityMetrics) === null || _analyticsData$review18 === void 0 ? void 0 : _analyticsData$review18.avgProcedureQuality) || 0, ((_analyticsData$review19 = analyticsData.reviewStats) === null || _analyticsData$review19 === void 0 ? void 0 : (_analyticsData$review20 = _analyticsData$review19.qualityMetrics) === null || _analyticsData$review20 === void 0 ? void 0 : _analyticsData$review20.avgPatientInteraction) || 0, ((_analyticsData$review21 = analyticsData.reviewStats) === null || _analyticsData$review21 === void 0 ? void 0 : (_analyticsData$review22 = _analyticsData$review21.qualityMetrics) === null || _analyticsData$review22 === void 0 ? void 0 : _analyticsData$review22.avgDocumentation) || 0, ((_analyticsData$review23 = analyticsData.reviewStats) === null || _analyticsData$review23 === void 0 ? void 0 : (_analyticsData$review24 = _analyticsData$review23.qualityMetrics) === null || _analyticsData$review24 === void 0 ? void 0 : _analyticsData$review24.avgTechnique) || 0, ((_analyticsData$review25 = analyticsData.reviewStats) === null || _analyticsData$review25 === void 0 ? void 0 : (_analyticsData$review26 = _analyticsData$review25.qualityMetrics) === null || _analyticsData$review26 === void 0 ? void 0 : _analyticsData$review26.avgTimeManagement) || 0, ((_analyticsData$review27 = analyticsData.reviewStats) === null || _analyticsData$review27 === void 0 ? void 0 : (_analyticsData$review28 = _analyticsData$review27.qualityMetrics) === null || _analyticsData$review28 === void 0 ? void 0 : _analyticsData$review28.avgCommunication) || 0],\n      backgroundColor: 'rgba(59, 130, 246, 0.5)',\n      borderColor: 'rgba(59, 130, 246, 1)',\n      borderWidth: 2,\n      pointBackgroundColor: 'rgba(59, 130, 246, 1)',\n      pointBorderColor: '#fff',\n      pointHoverBackgroundColor: '#fff',\n      pointHoverBorderColor: 'rgba(59, 130, 246, 1)',\n      pointRadius: 4\n    }]\n  };\n\n  // Animation variants\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 542,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 547,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 text-red-500 mr-3\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-700 font-medium\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\",\n                  children: \"Analytics Dashboard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#333333]\",\n                  children: [\"Insights for \", (user === null || user === void 0 ? void 0 : user.name) || 'Student']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: timeRange,\n                  onChange: e => setTimeRange(e.target.value),\n                  className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] bg-white text-sm transition-all duration-300\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"week\",\n                    children: \"Last 7 Days\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"month\",\n                    children: \"Last 30 Days\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 592,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"all\",\n                    children: \"All Time\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              className: \"bg-white rounded-xl shadow-md overflow-hidden mb-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex overflow-x-auto\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('overview'),\n                  className: `flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${activeTab === 'overview' ? 'border-[#20B2AA] text-[#0077B6]' : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)]'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaChartBar, {\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 613,\n                    columnNumber: 21\n                  }, this), \"Overview\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 605,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('patients'),\n                  className: `flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${activeTab === 'patients' ? 'border-[#20B2AA] text-[#0077B6]' : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)]'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaUserInjured, {\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 624,\n                    columnNumber: 21\n                  }, this), \"Patients\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('procedures'),\n                  className: `flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${activeTab === 'procedures' ? 'border-[#20B2AA] text-[#0077B6]' : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)]'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaTooth, {\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 635,\n                    columnNumber: 21\n                  }, this), \"Treatment Sheets\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 627,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('reviews'),\n                  className: `flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${activeTab === 'reviews' ? 'border-[#20B2AA] text-[#0077B6]' : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)]'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaClipboardCheck, {\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 646,\n                    columnNumber: 21\n                  }, this), \"Reviews\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 15\n            }, this), activeTab === 'overview' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                variants: container,\n                initial: \"hidden\",\n                whileInView: \"show\",\n                viewport: {\n                  once: true\n                },\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#28A745] group\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[rgba(0,119,182,0.05)] w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-[rgba(0,119,182,0.1)] transition-colors duration-300\",\n                      children: /*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                        className: \"h-6 w-6 text-[#0077B6]\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 669,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 668,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Appointments\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 672,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-[#0077B6]\",\n                        children: analyticsData.appointmentStats.pending + analyticsData.appointmentStats.completed + analyticsData.appointmentStats.cancelled\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 673,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center text-xs text-gray-600 mt-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"flex items-center text-yellow-600 mr-2\",\n                          children: [/*#__PURE__*/_jsxDEV(FaHourglassHalf, {\n                            className: \"h-3 w-3 mr-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 680,\n                            columnNumber: 31\n                          }, this), \" \", analyticsData.appointmentStats.pending, \" pending\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 679,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"flex items-center text-green-600\",\n                          children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                            className: \"h-3 w-3 mr-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 683,\n                            columnNumber: 31\n                          }, this), \" \", analyticsData.appointmentStats.completed, \" completed\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 682,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 678,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 671,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 667,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 663,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#28A745] group\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-green-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-green-100 transition-colors duration-300\",\n                      children: /*#__PURE__*/_jsxDEV(FaUsers, {\n                        className: \"h-6 w-6 text-green-600\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 696,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 695,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Patients\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 699,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-[#0077B6]\",\n                        children: Object.values(analyticsData.ageDistribution).reduce((a, b) => a + b, 0)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 700,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-600 mt-1\",\n                        children: Object.keys(analyticsData.genderDistribution).length > 0 && `${Math.round((analyticsData.genderDistribution['Male'] || 0) / Object.values(analyticsData.genderDistribution).reduce((a, b) => a + b, 0) * 100)}% male,\n                               ${Math.round((analyticsData.genderDistribution['Female'] || 0) / Object.values(analyticsData.genderDistribution).reduce((a, b) => a + b, 0) * 100)}% female`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 703,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 698,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 694,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#20B2AA] group\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-purple-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-purple-100 transition-colors duration-300\",\n                      children: /*#__PURE__*/_jsxDEV(FaTooth, {\n                        className: \"h-6 w-6 text-purple-600\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 721,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 720,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Treatment Sheets\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 724,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-[#0077B6]\",\n                        children: Object.values(analyticsData.procedureFrequency).reduce((a, b) => a + b, 0)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 725,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-600 mt-1\",\n                        children: Object.keys(analyticsData.procedureFrequency).length > 0 && `Top: ${Object.entries(analyticsData.procedureFrequency).sort(([, a], [, b]) => b - a)[0][0]}`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 728,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 723,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 719,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#20B2AA] group\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-yellow-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-yellow-100 transition-colors duration-300\",\n                      children: /*#__PURE__*/_jsxDEV(FaClipboardCheck, {\n                        className: \"h-6 w-6 text-yellow-600\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 744,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 743,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Reviews\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 747,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-[#0077B6]\",\n                        children: ((_analyticsData$review29 = analyticsData.reviewStats) === null || _analyticsData$review29 === void 0 ? void 0 : _analyticsData$review29.totalReviews) || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 748,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center text-xs text-gray-600 mt-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"flex items-center text-green-600 mr-2\",\n                          children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                            className: \"h-3 w-3 mr-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 753,\n                            columnNumber: 31\n                          }, this), \" \", ((_analyticsData$review30 = analyticsData.reviewStats) === null || _analyticsData$review30 === void 0 ? void 0 : (_analyticsData$review31 = _analyticsData$review30.statusDistribution) === null || _analyticsData$review31 === void 0 ? void 0 : _analyticsData$review31.accepted) || 0, \" accepted\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 752,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"flex items-center text-yellow-600\",\n                          children: [/*#__PURE__*/_jsxDEV(FaHourglassHalf, {\n                            className: \"h-3 w-3 mr-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 756,\n                            columnNumber: 31\n                          }, this), \" \", ((_analyticsData$review32 = analyticsData.reviewStats) === null || _analyticsData$review32 === void 0 ? void 0 : (_analyticsData$review33 = _analyticsData$review32.statusDistribution) === null || _analyticsData$review33 === void 0 ? void 0 : _analyticsData$review33.pending) || 0, \" pending\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 755,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 751,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 746,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 742,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 738,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 656,\n                columnNumber: 19\n              }, this)\n            }, void 0, false), activeTab === 'overview' && /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              whileInView: \"show\",\n              viewport: {\n                once: true\n              },\n              className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"bg-white p-6 rounded-xl shadow-sm border border-[rgba(0,119,182,0.1)] hover:shadow-md transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Appointment Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 781,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => toggleChartType('appointments'),\n                    className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\",\n                    children: \"Change Chart\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 782,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-64\",\n                  children: [chartType.appointments === 'doughnut' && /*#__PURE__*/_jsxDEV(Doughnut, {\n                    data: appointmentStatusData,\n                    options: {\n                      maintainAspectRatio: false,\n                      plugins: {\n                        legend: {\n                          position: 'bottom'\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 791,\n                    columnNumber: 25\n                  }, this), chartType.appointments === 'pie' && /*#__PURE__*/_jsxDEV(Pie, {\n                    data: appointmentStatusData,\n                    options: {\n                      maintainAspectRatio: false,\n                      plugins: {\n                        legend: {\n                          position: 'bottom'\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 802,\n                    columnNumber: 25\n                  }, this), chartType.appointments === 'bar' && /*#__PURE__*/_jsxDEV(Bar, {\n                    data: appointmentStatusData,\n                    options: {\n                      maintainAspectRatio: false,\n                      scales: {\n                        y: {\n                          beginAtZero: true\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 813,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 789,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Appointments Per Month\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 832,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => toggleChartType('appointmentsPerMonth'),\n                    className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\",\n                    children: \"Change Chart\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 833,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 831,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-64\",\n                  children: [chartType.appointmentsPerMonth === 'line' && /*#__PURE__*/_jsxDEV(Line, {\n                    data: appointmentsPerMonthData,\n                    options: {\n                      maintainAspectRatio: false,\n                      scales: {\n                        y: {\n                          beginAtZero: true\n                        }\n                      },\n                      plugins: {\n                        legend: {\n                          position: 'bottom'\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 842,\n                    columnNumber: 25\n                  }, this), chartType.appointmentsPerMonth === 'bar' && /*#__PURE__*/_jsxDEV(Bar, {\n                    data: appointmentsPerMonthData,\n                    options: {\n                      maintainAspectRatio: false,\n                      scales: {\n                        y: {\n                          beginAtZero: true\n                        }\n                      },\n                      plugins: {\n                        legend: {\n                          position: 'bottom'\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 856,\n                    columnNumber: 25\n                  }, this), chartType.appointmentsPerMonth === 'area' && /*#__PURE__*/_jsxDEV(Line, {\n                    data: appointmentsPerMonthData,\n                    options: {\n                      maintainAspectRatio: false,\n                      scales: {\n                        y: {\n                          beginAtZero: true\n                        }\n                      },\n                      plugins: {\n                        legend: {\n                          position: 'bottom'\n                        }\n                      },\n                      elements: {\n                        line: {\n                          fill: true\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 870,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 840,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 17\n            }, this), activeTab === 'patients' && /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              whileInView: \"show\",\n              viewport: {\n                once: true\n              },\n              className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Patient Age Distribution\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 908,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => toggleChartType('age'),\n                    className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\",\n                    children: \"Change Chart\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 909,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 907,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-64\",\n                  children: [chartType.age === 'bar' && /*#__PURE__*/_jsxDEV(Bar, {\n                    data: ageDistributionData,\n                    options: {\n                      maintainAspectRatio: false,\n                      scales: {\n                        y: {\n                          beginAtZero: true\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 918,\n                    columnNumber: 25\n                  }, this), chartType.age === 'line' && /*#__PURE__*/_jsxDEV(Line, {\n                    data: ageDistributionData,\n                    options: {\n                      maintainAspectRatio: false,\n                      scales: {\n                        y: {\n                          beginAtZero: true\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 929,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 916,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 903,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Patient Gender\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 948,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => toggleChartType('gender'),\n                    className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\",\n                    children: \"Change Chart\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 949,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 947,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-64\",\n                  children: [chartType.gender === 'pie' && /*#__PURE__*/_jsxDEV(Pie, {\n                    data: genderDistributionData,\n                    options: {\n                      maintainAspectRatio: false,\n                      plugins: {\n                        legend: {\n                          position: 'bottom'\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 958,\n                    columnNumber: 25\n                  }, this), chartType.gender === 'doughnut' && /*#__PURE__*/_jsxDEV(Doughnut, {\n                    data: genderDistributionData,\n                    options: {\n                      maintainAspectRatio: false,\n                      plugins: {\n                        legend: {\n                          position: 'bottom'\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 969,\n                    columnNumber: 25\n                  }, this), chartType.gender === 'bar' && /*#__PURE__*/_jsxDEV(Bar, {\n                    data: genderDistributionData,\n                    options: {\n                      maintainAspectRatio: false,\n                      scales: {\n                        y: {\n                          beginAtZero: true\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 980,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 956,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 943,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 lg:col-span-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Chronic Disease Prevalence\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 999,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => toggleChartType('diseases'),\n                    className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\",\n                    children: \"Change Chart\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1000,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 998,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-64\",\n                  children: [chartType.diseases === 'doughnut' && /*#__PURE__*/_jsxDEV(Doughnut, {\n                    data: chronicDiseaseData,\n                    options: {\n                      maintainAspectRatio: false,\n                      plugins: {\n                        legend: {\n                          position: 'bottom'\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1009,\n                    columnNumber: 25\n                  }, this), chartType.diseases === 'pie' && /*#__PURE__*/_jsxDEV(Pie, {\n                    data: chronicDiseaseData,\n                    options: {\n                      maintainAspectRatio: false,\n                      plugins: {\n                        legend: {\n                          position: 'bottom'\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1020,\n                    columnNumber: 25\n                  }, this), chartType.diseases === 'bar' && /*#__PURE__*/_jsxDEV(Bar, {\n                    data: chronicDiseaseData,\n                    options: {\n                      maintainAspectRatio: false,\n                      scales: {\n                        y: {\n                          beginAtZero: true\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1031,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1007,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 994,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 895,\n              columnNumber: 17\n            }, this), activeTab === 'procedures' && /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              whileInView: \"show\",\n              viewport: {\n                once: true\n              },\n              className: \"grid grid-cols-1 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Treatment Sheet Submissions by Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1061,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => toggleChartType('procedures'),\n                    className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\",\n                    children: \"Change Chart\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1062,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1060,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-80\",\n                  children: [chartType.procedures === 'bar' && /*#__PURE__*/_jsxDEV(Bar, {\n                    data: procedureFrequencyData,\n                    options: {\n                      maintainAspectRatio: false,\n                      scales: {\n                        y: {\n                          beginAtZero: true\n                        }\n                      },\n                      indexAxis: 'y' // Horizontal bars\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1071,\n                    columnNumber: 25\n                  }, this), chartType.procedures === 'pie' && /*#__PURE__*/_jsxDEV(Pie, {\n                    data: procedureFrequencyData,\n                    options: {\n                      maintainAspectRatio: false,\n                      plugins: {\n                        legend: {\n                          position: 'bottom'\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1083,\n                    columnNumber: 25\n                  }, this), chartType.procedures === 'doughnut' && /*#__PURE__*/_jsxDEV(Doughnut, {\n                    data: procedureFrequencyData,\n                    options: {\n                      maintainAspectRatio: false,\n                      plugins: {\n                        legend: {\n                          position: 'bottom'\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1094,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1069,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1056,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Appointment Types\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1113,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => toggleChartType('appointments'),\n                    className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\",\n                    children: \"Change Chart\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1114,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1112,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-64\",\n                  children: [chartType.appointments === 'doughnut' && /*#__PURE__*/_jsxDEV(Doughnut, {\n                    data: appointmentTypesData,\n                    options: {\n                      maintainAspectRatio: false,\n                      plugins: {\n                        legend: {\n                          position: 'bottom'\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1123,\n                    columnNumber: 25\n                  }, this), chartType.appointments === 'pie' && /*#__PURE__*/_jsxDEV(Pie, {\n                    data: appointmentTypesData,\n                    options: {\n                      maintainAspectRatio: false,\n                      plugins: {\n                        legend: {\n                          position: 'bottom'\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1134,\n                    columnNumber: 25\n                  }, this), chartType.appointments === 'bar' && /*#__PURE__*/_jsxDEV(Bar, {\n                    data: appointmentTypesData,\n                    options: {\n                      maintainAspectRatio: false,\n                      scales: {\n                        y: {\n                          beginAtZero: true\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1145,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1121,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1108,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1048,\n              columnNumber: 17\n            }, this), activeTab === 'reviews' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                variants: container,\n                initial: \"hidden\",\n                whileInView: \"show\",\n                viewport: {\n                  once: true\n                },\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[rgba(0,119,182,0.1)] w-10 h-10 rounded-full flex items-center justify-center mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(FaClipboardCheck, {\n                        className: \"h-5 w-5 text-[#0077B6]\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1177,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1176,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Total Reviews\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1180,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-[#0077B6]\",\n                        children: ((_analyticsData$review34 = analyticsData.reviewStats) === null || _analyticsData$review34 === void 0 ? void 0 : _analyticsData$review34.totalReviews) || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1181,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1179,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1175,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1171,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[rgba(40,167,69,0.1)] w-10 h-10 rounded-full flex items-center justify-center mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                        className: \"h-5 w-5 text-[#28A745]\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1194,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1193,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Acceptance Rate\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1197,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-[#28A745]\",\n                        children: [((_analyticsData$review35 = analyticsData.reviewStats) === null || _analyticsData$review35 === void 0 ? void 0 : _analyticsData$review35.acceptanceRate) || 0, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1198,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1196,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1192,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-2 bg-gray-200 rounded-full overflow-hidden\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-full bg-[#28A745] rounded-full\",\n                      style: {\n                        width: `${((_analyticsData$review36 = analyticsData.reviewStats) === null || _analyticsData$review36 === void 0 ? void 0 : _analyticsData$review36.acceptanceRate) || 0}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1204,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1203,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1188,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[rgba(32,178,170,0.1)] w-10 h-10 rounded-full flex items-center justify-center mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(FaStarHalfAlt, {\n                        className: \"h-5 w-5 text-[#20B2AA]\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1217,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1216,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Avg. Procedure Quality\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1220,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-[#20B2AA]\",\n                        children: [((_analyticsData$review37 = analyticsData.reviewStats) === null || _analyticsData$review37 === void 0 ? void 0 : (_analyticsData$review38 = _analyticsData$review37.qualityMetrics) === null || _analyticsData$review38 === void 0 ? void 0 : _analyticsData$review38.avgProcedureQuality) || 0, \"/5\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1221,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1219,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1215,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-1\",\n                    children: renderStars(((_analyticsData$review39 = analyticsData.reviewStats) === null || _analyticsData$review39 === void 0 ? void 0 : (_analyticsData$review40 = _analyticsData$review39.qualityMetrics) === null || _analyticsData$review40 === void 0 ? void 0 : _analyticsData$review40.avgProcedureQuality) || 0)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1226,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1211,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[rgba(0,119,182,0.1)] w-10 h-10 rounded-full flex items-center justify-center mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(FaStarHalfAlt, {\n                        className: \"h-5 w-5 text-[#0077B6]\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1237,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1236,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-gray-500\",\n                        children: \"Avg. Patient Interaction\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1240,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-[#0077B6]\",\n                        children: [((_analyticsData$review41 = analyticsData.reviewStats) === null || _analyticsData$review41 === void 0 ? void 0 : (_analyticsData$review42 = _analyticsData$review41.qualityMetrics) === null || _analyticsData$review42 === void 0 ? void 0 : _analyticsData$review42.avgPatientInteraction) || 0, \"/5\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1241,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1239,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1235,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-1\",\n                    children: renderStars(((_analyticsData$review43 = analyticsData.reviewStats) === null || _analyticsData$review43 === void 0 ? void 0 : (_analyticsData$review44 = _analyticsData$review43.qualityMetrics) === null || _analyticsData$review44 === void 0 ? void 0 : _analyticsData$review44.avgPatientInteraction) || 0)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1246,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1231,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1164,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                variants: container,\n                initial: \"hidden\",\n                whileInView: \"show\",\n                viewport: {\n                  once: true\n                },\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-xl font-bold text-[#0077B6]\",\n                      children: \"Review Status Distribution\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1266,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => toggleChartType('reviewStatus'),\n                      className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\",\n                      children: \"Change Chart\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1267,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1265,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-64\",\n                    ref: statusChartRef,\n                    children: [chartType.reviewStatus === 'doughnut' && /*#__PURE__*/_jsxDEV(Doughnut, {\n                      data: reviewStatusData,\n                      options: {\n                        maintainAspectRatio: false,\n                        plugins: {\n                          legend: {\n                            position: 'bottom'\n                          }\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1276,\n                      columnNumber: 27\n                    }, this), chartType.reviewStatus === 'pie' && /*#__PURE__*/_jsxDEV(Pie, {\n                      data: reviewStatusData,\n                      options: {\n                        maintainAspectRatio: false,\n                        plugins: {\n                          legend: {\n                            position: 'bottom'\n                          }\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1287,\n                      columnNumber: 27\n                    }, this), chartType.reviewStatus === 'bar' && /*#__PURE__*/_jsxDEV(Bar, {\n                      data: reviewStatusData,\n                      options: {\n                        maintainAspectRatio: false,\n                        scales: {\n                          y: {\n                            beginAtZero: true\n                          }\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1298,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1274,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1261,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-xl font-bold text-[#0077B6]\",\n                      children: \"Reviews by Procedure Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1317,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => toggleChartType('reviewProcedures'),\n                      className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\",\n                      children: \"Change Chart\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1318,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1316,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-64\",\n                    ref: procedureChartRef,\n                    children: [chartType.reviewProcedures === 'bar' && /*#__PURE__*/_jsxDEV(Bar, {\n                      data: reviewProcedureData,\n                      options: {\n                        maintainAspectRatio: false,\n                        scales: {\n                          x: {\n                            stacked: true\n                          },\n                          y: {\n                            stacked: true,\n                            beginAtZero: true\n                          }\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1327,\n                      columnNumber: 27\n                    }, this), chartType.reviewProcedures === 'line' && /*#__PURE__*/_jsxDEV(Line, {\n                      data: reviewProcedureData,\n                      options: {\n                        maintainAspectRatio: false,\n                        scales: {\n                          y: {\n                            beginAtZero: true\n                          }\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1339,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1325,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1312,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-xl font-bold text-[#0077B6]\",\n                      children: \"Review Quality Metrics\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1358,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => toggleChartType('reviewQuality'),\n                      className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\",\n                      children: \"Change Chart\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1359,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1357,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-64\",\n                    ref: qualityMetricsChartRef,\n                    children: [chartType.reviewQuality === 'radar' && /*#__PURE__*/_jsxDEV(Radar, {\n                      data: reviewQualityData,\n                      options: {\n                        maintainAspectRatio: false,\n                        scales: {\n                          r: {\n                            beginAtZero: true,\n                            max: 5,\n                            ticks: {\n                              stepSize: 1\n                            }\n                          }\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1368,\n                      columnNumber: 27\n                    }, this), chartType.reviewQuality === 'bar' && /*#__PURE__*/_jsxDEV(Bar, {\n                      data: reviewQualityData,\n                      options: {\n                        maintainAspectRatio: false,\n                        scales: {\n                          y: {\n                            beginAtZero: true,\n                            max: 5,\n                            ticks: {\n                              stepSize: 1\n                            }\n                          }\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1385,\n                      columnNumber: 27\n                    }, this), chartType.reviewQuality === 'polarArea' && /*#__PURE__*/_jsxDEV(PolarArea, {\n                      data: reviewQualityData,\n                      options: {\n                        maintainAspectRatio: false,\n                        scales: {\n                          r: {\n                            beginAtZero: true,\n                            max: 5,\n                            ticks: {\n                              stepSize: 1\n                            }\n                          }\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1402,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1366,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1353,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-xl font-bold text-[#0077B6]\",\n                      children: \"Review Trend Over Time\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1427,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => toggleChartType('reviewTrend'),\n                      className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\",\n                      children: \"Change Chart\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1428,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1426,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-64\",\n                    ref: reviewTrendChartRef,\n                    children: [chartType.reviewTrend === 'line' && /*#__PURE__*/_jsxDEV(Line, {\n                      data: reviewTrendData,\n                      options: {\n                        maintainAspectRatio: false,\n                        scales: {\n                          y: {\n                            beginAtZero: true\n                          }\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1437,\n                      columnNumber: 27\n                    }, this), chartType.reviewTrend === 'bar' && /*#__PURE__*/_jsxDEV(Bar, {\n                      data: reviewTrendData,\n                      options: {\n                        maintainAspectRatio: false,\n                        scales: {\n                          y: {\n                            beginAtZero: true\n                          }\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1448,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1435,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1422,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1253,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 549,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 546,\n    columnNumber: 5\n  }, this);\n};\n_s(Analytics, \"RhC4b/cM6kTXu9Fmb8y4/551png=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Analytics;\nexport default Analytics;\nvar _c;\n$RefreshReg$(_c, \"Analytics\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "useNavigate", "Link", "axios", "useAuth", "<PERSON><PERSON><PERSON>", "Sidebar", "Loader", "motion", "FaUsers", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaCalendarAlt", "FaClipboardCheck", "FaCheckCircle", "FaHourglassHalf", "FaStarHalfAlt", "FaStar", "FaUserInjured", "FaChartBar", "Chart", "ChartJS", "CategoryScale", "LinearScale", "BarElement", "ArcElement", "PointElement", "LineElement", "RadialLinearScale", "Title", "<PERSON><PERSON><PERSON>", "Legend", "Filler", "Bar", "Pie", "Doughnut", "Line", "PolarArea", "Radar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "register", "Analytics", "_s", "_analyticsData$review", "_analyticsData$review2", "_analyticsData$review3", "_analyticsData$review4", "_analyticsData$review5", "_analyticsData$review6", "_analyticsData$review7", "_analyticsData$review8", "_analyticsData$review9", "_analyticsData$review10", "_analyticsData$review11", "_analyticsData$review12", "_analyticsData$review13", "_analyticsData$review14", "_analyticsData$review15", "_analyticsData$review16", "_analyticsData$review17", "_analyticsData$review18", "_analyticsData$review19", "_analyticsData$review20", "_analyticsData$review21", "_analyticsData$review22", "_analyticsData$review23", "_analyticsData$review24", "_analyticsData$review25", "_analyticsData$review26", "_analyticsData$review27", "_analyticsData$review28", "_analyticsData$review29", "_analyticsData$review30", "_analyticsData$review31", "_analyticsData$review32", "_analyticsData$review33", "_analyticsData$review34", "_analyticsData$review35", "_analyticsData$review36", "_analyticsData$review37", "_analyticsData$review38", "_analyticsData$review39", "_analyticsData$review40", "_analyticsData$review41", "_analyticsData$review42", "_analyticsData$review43", "_analyticsData$review44", "sidebarOpen", "setSidebarOpen", "loading", "setLoading", "error", "setError", "timeRange", "setTimeRange", "activeTab", "setActiveTab", "chartType", "setChartType", "appointments", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "age", "gender", "procedures", "diseases", "reviewStatus", "reviewProcedures", "reviewTrend", "reviewQuality", "statusChartRef", "procedureChartRef", "reviewTrendChartRef", "qualityMetricsChartRef", "analyticsData", "setAnalyticsData", "appointmentStats", "pending", "completed", "cancelled", "appointmentTypes", "ageDistribution", "genderDistribution", "procedureFrequency", "chronicDiseasePrevalence", "reviewStats", "statusDistribution", "accepted", "denied", "procedureTypeDistribution", "qualityMetrics", "avgProcedureQuality", "avgPatientInteraction", "reviewsByMonth", "acceptanceRate", "denialRate", "totalReviews", "navigate", "user", "token", "fetchAnalytics", "config", "headers", "Authorization", "params", "range", "analyticsResponse", "get", "reviewsData", "avgDocumentation", "avgTechnique", "avgTimeManagement", "avgCommunication", "studentId", "reviewsResponse", "reviews", "data", "Array", "isArray", "length", "pendingReviews", "filter", "r", "status", "doneReviews", "acceptedReviews", "deniedReviews", "toFixed", "reduce", "sum", "procedureQuality", "patientInteraction", "procedureTypes", "for<PERSON>ach", "review", "type", "procedureType", "total", "monthsMap", "date", "Date", "submittedDate", "monthYear", "toLocaleString", "month", "getFullYear", "year", "Object", "values", "push", "sort", "a", "b", "months", "indexOf", "parseFloat", "err", "console", "transformedData", "transformAgeData", "transformDiseaseData", "acc", "proc", "name", "count", "errorMessage", "response", "_err$response$data", "message", "ageData", "entries", "parseInt", "split", "key", "value", "diseaseData", "sorted", "slice", "fromEntries", "toggleChartType", "chart<PERSON>ey", "chartTypes", "currentType", "types", "currentIndex", "nextIndex", "renderStars", "rating", "className", "children", "map", "_", "i", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "appointmentStatusData", "labels", "datasets", "backgroundColor", "hoverBackgroundColor", "appointmentTypesData", "keys", "appointmentsPerMonthData", "item", "label", "borderColor", "borderWidth", "tension", "fill", "ageDistributionData", "genderDistributionData", "procedureFrequencyData", "chronicDiseaseData", "reviewStatusData", "reviewProcedureData", "defaultMonths", "defaultTotalData", "defaultAcceptedData", "reviewTrendData", "reviewQualityData", "pointBackgroundColor", "pointBorderColor", "pointHoverBackgroundColor", "pointHoverBorderColor", "pointRadius", "container", "hidden", "opacity", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "y", "isOpen", "setIsOpen", "toggleSidebar", "div", "initial", "animate", "viewBox", "fillRule", "d", "clipRule", "duration", "onChange", "e", "target", "onClick", "variants", "whileInView", "viewport", "once", "Math", "round", "options", "maintainAspectRatio", "plugins", "legend", "position", "scales", "beginAtZero", "elements", "line", "indexAxis", "style", "width", "ref", "x", "stacked", "max", "ticks", "stepSize", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/student/Analytics.jsx"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\r\nimport { useNavigate, Link } from 'react-router-dom';\r\nimport axios from 'axios';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport Navbar from './Navbar';\r\nimport Sidebar from './Sidebar';\r\nimport Loader from '../components/Loader';\r\nimport { motion } from 'framer-motion';\r\nimport {\r\n  FaUsers,\r\n  FaTooth,\r\n  FaCalendarAlt,\r\n  FaClipboardCheck,\r\n  FaCheckCircle,\r\n  FaHourglassHalf,\r\n  FaStarHalfAlt,\r\n  FaStar,\r\n  FaUserInjured,\r\n  FaChartBar,\r\n} from 'react-icons/fa';\r\nimport {\r\n  Chart as ChartJS,\r\n  CategoryScale,\r\n  LinearScale,\r\n  BarElement,\r\n  ArcElement,\r\n  PointElement,\r\n  LineElement,\r\n  RadialLinearScale,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  Filler\r\n} from 'chart.js';\r\nimport { Bar, Pie, Doughnut, Line, PolarArea, Radar } from 'react-chartjs-2';\r\n\r\nChartJS.register(\r\n  CategoryScale,\r\n  LinearScale,\r\n  BarElement,\r\n  ArcElement,\r\n  PointElement,\r\n  LineElement,\r\n  RadialLinearScale,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  Filler\r\n);\r\n\r\nconst Analytics = () => {\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [timeRange, setTimeRange] = useState('all'); // 'week', 'month', 'all'\r\n  const [activeTab, setActiveTab] = useState('overview'); // 'overview', 'patients', 'procedures', 'reviews'\r\n  const [chartType, setChartType] = useState({\r\n    appointments: 'doughnut',\r\n    appointmentsPerMonth: 'line',\r\n    age: 'bar',\r\n    gender: 'pie',\r\n    procedures: 'bar',\r\n    diseases: 'doughnut',\r\n    reviewStatus: 'doughnut',\r\n    reviewProcedures: 'bar',\r\n    reviewTrend: 'line',\r\n    reviewQuality: 'radar'\r\n  });\r\n\r\n  // Chart references\r\n  const statusChartRef = useRef(null);\r\n  const procedureChartRef = useRef(null);\r\n  const reviewTrendChartRef = useRef(null);\r\n  const qualityMetricsChartRef = useRef(null);\r\n\r\n  const [analyticsData, setAnalyticsData] = useState({\r\n    appointmentStats: { pending: 0, completed: 0, cancelled: 0 },\r\n    appointmentTypes: {},\r\n    ageDistribution: {},\r\n    genderDistribution: {},\r\n    procedureFrequency: {},\r\n    chronicDiseasePrevalence: {},\r\n    reviewStats: {\r\n      statusDistribution: { accepted: 0, pending: 0, denied: 0 },\r\n      procedureTypeDistribution: {},\r\n      qualityMetrics: {\r\n        avgProcedureQuality: 0,\r\n        avgPatientInteraction: 0\r\n      },\r\n      reviewsByMonth: [],\r\n      acceptanceRate: 0,\r\n      denialRate: 0,\r\n      totalReviews: 0\r\n    }\r\n  });\r\n  const navigate = useNavigate();\r\n  const { user, token } = useAuth();\r\n\r\n  useEffect(() => {\r\n    const fetchAnalytics = async () => {\r\n      if (!user || !token) {\r\n        setError('Please log in to view analytics.');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const config = {\r\n          headers: { Authorization: `Bearer ${token}` },\r\n          params: { range: timeRange }\r\n        };\r\n\r\n        // Fetch general analytics data\r\n        const analyticsResponse = await axios.get('http://localhost:5000/api/analytics', config);\r\n\r\n        // Fetch reviews data separately\r\n        let reviewsData = {\r\n          statusDistribution: { accepted: 0, pending: 0, denied: 0 },\r\n          procedureTypeDistribution: {},\r\n          qualityMetrics: {\r\n            avgProcedureQuality: 0,\r\n            avgPatientInteraction: 0,\r\n            avgDocumentation: 0,\r\n            avgTechnique: 0,\r\n            avgTimeManagement: 0,\r\n            avgCommunication: 0\r\n          },\r\n          reviewsByMonth: [],\r\n          acceptanceRate: 0,\r\n          denialRate: 0,\r\n          totalReviews: 0\r\n        };\r\n\r\n        if (user.studentId) {\r\n          try {\r\n            const reviewsResponse = await axios.get(`http://localhost:5000/api/reviews/student?studentId=${user.studentId}`, config);\r\n            const reviews = reviewsResponse.data;\r\n\r\n            if (Array.isArray(reviews) && reviews.length > 0) {\r\n              // Calculate review statistics\r\n              const totalReviews = reviews.length;\r\n              const pendingReviews = reviews.filter(r => r.status === 'pending');\r\n              const doneReviews = reviews.filter(r => r.status !== 'pending');\r\n              const acceptedReviews = doneReviews.filter(r => r.status === 'accepted');\r\n              const deniedReviews = doneReviews.filter(r => r.status === 'denied');\r\n\r\n              const acceptanceRate = totalReviews > 0 ? ((acceptedReviews.length / totalReviews) * 100).toFixed(1) : 0;\r\n              const denialRate = totalReviews > 0 ? ((deniedReviews.length / totalReviews) * 100).toFixed(1) : 0;\r\n\r\n              // Calculate average ratings\r\n              const avgProcedureQuality = doneReviews.length > 0\r\n                ? (doneReviews.reduce((sum, r) => sum + (r.procedureQuality || 0), 0) / doneReviews.length).toFixed(1)\r\n                : 0;\r\n              const avgPatientInteraction = doneReviews.length > 0\r\n                ? (doneReviews.reduce((sum, r) => sum + (r.patientInteraction || 0), 0) / doneReviews.length).toFixed(1)\r\n                : 0;\r\n\r\n              // Group reviews by procedure type\r\n              const procedureTypes = {};\r\n              reviews.forEach(review => {\r\n                const type = review.procedureType || 'Unknown';\r\n                if (!procedureTypes[type]) {\r\n                  procedureTypes[type] = {\r\n                    total: 0,\r\n                    accepted: 0,\r\n                    denied: 0,\r\n                    pending: 0\r\n                  };\r\n                }\r\n                procedureTypes[type].total++;\r\n                if (review.status === 'accepted') procedureTypes[type].accepted++;\r\n                else if (review.status === 'denied') procedureTypes[type].denied++;\r\n                else procedureTypes[type].pending++;\r\n              });\r\n\r\n              // Group reviews by month\r\n              const reviewsByMonth = [];\r\n              const monthsMap = {};\r\n\r\n              reviews.forEach(review => {\r\n                const date = new Date(review.submittedDate);\r\n                const monthYear = `${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}`;\r\n\r\n                if (!monthsMap[monthYear]) {\r\n                  monthsMap[monthYear] = {\r\n                    month: date.toLocaleString('default', { month: 'short' }),\r\n                    year: date.getFullYear(),\r\n                    total: 0,\r\n                    accepted: 0,\r\n                    pending: 0,\r\n                    denied: 0\r\n                  };\r\n                }\r\n\r\n                monthsMap[monthYear].total++;\r\n                if (review.status === 'accepted') monthsMap[monthYear].accepted++;\r\n                else if (review.status === 'pending') monthsMap[monthYear].pending++;\r\n                else monthsMap[monthYear].denied++;\r\n              });\r\n\r\n              // Convert to array and sort by date\r\n              Object.values(monthsMap).forEach(month => {\r\n                reviewsByMonth.push(month);\r\n              });\r\n\r\n              reviewsByMonth.sort((a, b) => {\r\n                if (a.year !== b.year) return a.year - b.year;\r\n                const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\r\n                return months.indexOf(a.month) - months.indexOf(b.month);\r\n              });\r\n\r\n              // Update reviewsData\r\n              reviewsData = {\r\n                statusDistribution: {\r\n                  accepted: acceptedReviews.length,\r\n                  pending: pendingReviews.length,\r\n                  denied: deniedReviews.length\r\n                },\r\n                procedureTypeDistribution: procedureTypes,\r\n                qualityMetrics: {\r\n                  avgProcedureQuality: parseFloat(avgProcedureQuality),\r\n                  avgPatientInteraction: parseFloat(avgPatientInteraction),\r\n                  avgDocumentation: parseFloat(avgProcedureQuality), // Using procedure quality as a placeholder\r\n                  avgTechnique: parseFloat(avgProcedureQuality), // Using procedure quality as a placeholder\r\n                  avgTimeManagement: parseFloat(avgPatientInteraction), // Using patient interaction as a placeholder\r\n                  avgCommunication: parseFloat(avgPatientInteraction) // Using patient interaction as a placeholder\r\n                },\r\n                reviewsByMonth: reviewsByMonth,\r\n                acceptanceRate: parseFloat(acceptanceRate),\r\n                denialRate: parseFloat(denialRate),\r\n                totalReviews: totalReviews\r\n              };\r\n            }\r\n          } catch (err) {\r\n            console.error('Error fetching reviews:', err);\r\n            // Continue with default reviewsData if reviews fetch fails\r\n          }\r\n        }\r\n\r\n        // Transform data for better chart display\r\n        const transformedData = {\r\n          ...analyticsResponse.data,\r\n          ageDistribution: transformAgeData(analyticsResponse.data.ageDistribution),\r\n          chronicDiseasePrevalence: transformDiseaseData(analyticsResponse.data.chronicDiseasePrevalence),\r\n          // Transform procedures data from new backend format\r\n          procedureFrequency: analyticsResponse.data.procedures ?\r\n            analyticsResponse.data.procedures.reduce((acc, proc) => {\r\n              acc[proc.name] = proc.count;\r\n              return acc;\r\n            }, {}) : {},\r\n          reviewStats: reviewsData\r\n        };\r\n\r\n        setAnalyticsData(transformedData);\r\n        setLoading(false);\r\n      } catch (err) {\r\n        console.error('Fetch analytics error:', err);\r\n        let errorMessage = 'Failed to load analytics data';\r\n        if (err.response) {\r\n          if (err.response.status === 401) {\r\n            navigate('/login');\r\n            return;\r\n          }\r\n          errorMessage = err.response.data?.message || errorMessage;\r\n        }\r\n        setError(errorMessage);\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchAnalytics();\r\n  }, [user, token, navigate, timeRange]);\r\n\r\n  // Helper functions to transform data\r\n  const transformAgeData = (ageData) => {\r\n    return Object.entries(ageData)\r\n      .sort(([a], [b]) => parseInt(a.split('-')[0]) - parseInt(b.split('-')[0]))\r\n      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});\r\n  };\r\n\r\n  const transformDiseaseData = (diseaseData) => {\r\n    const sorted = Object.entries(diseaseData)\r\n      .sort(([,a], [,b]) => b - a)\r\n      .slice(0, 5); // Show top 5 only\r\n    return Object.fromEntries(sorted);\r\n  };\r\n\r\n  // Function to toggle chart types\r\n  const toggleChartType = (chartKey) => {\r\n    const chartTypes = {\r\n      appointments: ['doughnut', 'pie', 'bar'],\r\n      appointmentsPerMonth: ['line', 'bar', 'area'],\r\n      age: ['bar', 'line'],\r\n      gender: ['pie', 'doughnut', 'bar'],\r\n      procedures: ['bar', 'pie', 'doughnut'],\r\n      diseases: ['doughnut', 'pie', 'bar'],\r\n      reviewStatus: ['doughnut', 'pie', 'bar'],\r\n      reviewProcedures: ['bar', 'line'],\r\n      reviewTrend: ['line', 'bar'],\r\n      reviewQuality: ['radar', 'bar', 'polarArea']\r\n    };\r\n\r\n    const currentType = chartType[chartKey];\r\n    const types = chartTypes[chartKey];\r\n    const currentIndex = types.indexOf(currentType);\r\n    const nextIndex = (currentIndex + 1) % types.length;\r\n\r\n    setChartType({\r\n      ...chartType,\r\n      [chartKey]: types[nextIndex]\r\n    });\r\n  };\r\n\r\n  // Function to render stars for ratings\r\n  const renderStars = (rating) => (\r\n    <div className=\"flex\">\r\n      {[...Array(5)].map((_, i) => (\r\n        <FaStar\r\n          key={i}\r\n          className={`h-5 w-5 ${i < (rating || 0) ? 'text-yellow-400' : 'text-gray-300'}`}\r\n        />\r\n      ))}\r\n    </div>\r\n  );\r\n\r\n  // Chart data configurations\r\n  const appointmentStatusData = {\r\n    labels: ['Pending', 'Completed', 'Cancelled'],\r\n    datasets: [{\r\n      data: [\r\n        analyticsData.appointmentStats.pending,\r\n        analyticsData.appointmentStats.completed,\r\n        analyticsData.appointmentStats.cancelled,\r\n      ],\r\n      backgroundColor: ['#FBBF24', '#10B981', '#EF4444'],\r\n      hoverBackgroundColor: ['#F59E0B', '#059669', '#DC2626'],\r\n    }]\r\n  };\r\n\r\n  const appointmentTypesData = {\r\n    labels: Object.keys(analyticsData.appointmentTypes),\r\n    datasets: [{\r\n      data: Object.values(analyticsData.appointmentTypes),\r\n      backgroundColor: ['#3B82F6', '#10B981', '#FBBF24', '#EF4444', '#8B5CF6'],\r\n    }]\r\n  };\r\n\r\n  // Appointments per month data\r\n  const appointmentsPerMonthData = {\r\n    labels: (analyticsData.appointmentsPerMonth || []).map(item => item.month) || [],\r\n    datasets: [\r\n      {\r\n        label: 'Total Appointments',\r\n        data: (analyticsData.appointmentsPerMonth || []).map(item => item.total) || [],\r\n        backgroundColor: 'rgba(59, 130, 246, 0.5)',\r\n        borderColor: 'rgba(59, 130, 246, 1)',\r\n        borderWidth: 2,\r\n        tension: 0.4,\r\n        fill: true,\r\n      },\r\n      {\r\n        label: 'Completed',\r\n        data: (analyticsData.appointmentsPerMonth || []).map(item => item.completed) || [],\r\n        backgroundColor: 'rgba(16, 185, 129, 0.5)',\r\n        borderColor: 'rgba(16, 185, 129, 1)',\r\n        borderWidth: 2,\r\n        tension: 0.4,\r\n        fill: false,\r\n      },\r\n      {\r\n        label: 'Pending',\r\n        data: (analyticsData.appointmentsPerMonth || []).map(item => item.pending) || [],\r\n        backgroundColor: 'rgba(245, 158, 11, 0.5)',\r\n        borderColor: 'rgba(245, 158, 11, 1)',\r\n        borderWidth: 2,\r\n        tension: 0.4,\r\n        fill: false,\r\n      }\r\n    ]\r\n  };\r\n\r\n  const ageDistributionData = {\r\n    labels: Object.keys(analyticsData.ageDistribution),\r\n    datasets: [{\r\n      label: 'Patients',\r\n      data: Object.values(analyticsData.ageDistribution),\r\n      backgroundColor: '#3B82F6',\r\n      borderColor: '#2563EB',\r\n      borderWidth: 1,\r\n    }]\r\n  };\r\n\r\n  const genderDistributionData = {\r\n    labels: Object.keys(analyticsData.genderDistribution),\r\n    datasets: [{\r\n      data: Object.values(analyticsData.genderDistribution),\r\n      backgroundColor: ['#3B82F6', '#EC4899', '#6B7280'],\r\n      hoverBackgroundColor: ['#2563EB', '#DB2777', '#4B5563'],\r\n    }]\r\n  };\r\n\r\n  const procedureFrequencyData = {\r\n    labels: Object.keys(analyticsData.procedureFrequency),\r\n    datasets: [{\r\n      label: 'Treatment Sheets',\r\n      data: Object.values(analyticsData.procedureFrequency),\r\n      backgroundColor: '#10B981',\r\n      borderColor: '#059669',\r\n      borderWidth: 1,\r\n    }]\r\n  };\r\n\r\n  const chronicDiseaseData = {\r\n    labels: Object.keys(analyticsData.chronicDiseasePrevalence),\r\n    datasets: [{\r\n      data: Object.values(analyticsData.chronicDiseasePrevalence),\r\n      backgroundColor: ['#EF4444', '#FBBF24', '#3B82F6', '#8B5CF6', '#EC4899'],\r\n      hoverBackgroundColor: ['#DC2626', '#F59E0B', '#2563EB', '#7C3AED', '#DB2777'],\r\n    }]\r\n  };\r\n\r\n  // Review status distribution data\r\n  const reviewStatusData = {\r\n    labels: ['Accepted', 'Pending', 'Denied'],\r\n    datasets: [{\r\n      data: [\r\n        analyticsData.reviewStats?.statusDistribution?.accepted || 0,\r\n        analyticsData.reviewStats?.statusDistribution?.pending || 0,\r\n        analyticsData.reviewStats?.statusDistribution?.denied || 0\r\n      ],\r\n      backgroundColor: ['#10B981', '#F59E0B', '#EF4444'],\r\n      hoverBackgroundColor: ['#059669', '#D97706', '#DC2626'],\r\n      borderWidth: 1\r\n    }]\r\n  };\r\n\r\n  // Review procedure type distribution data\r\n  const reviewProcedureData = {\r\n    labels: Object.keys(analyticsData.reviewStats?.procedureTypeDistribution || {}),\r\n    datasets: [\r\n      {\r\n        label: 'Accepted',\r\n        data: Object.values(analyticsData.reviewStats?.procedureTypeDistribution || {}).map(item => item?.accepted || 0),\r\n        backgroundColor: 'rgba(16, 185, 129, 0.7)',\r\n        borderColor: 'rgba(16, 185, 129, 1)',\r\n        borderWidth: 1,\r\n      },\r\n      {\r\n        label: 'Pending',\r\n        data: Object.values(analyticsData.reviewStats?.procedureTypeDistribution || {}).map(item => item?.pending || 0),\r\n        backgroundColor: 'rgba(245, 158, 11, 0.7)',\r\n        borderColor: 'rgba(245, 158, 11, 1)',\r\n        borderWidth: 1,\r\n      },\r\n      {\r\n        label: 'Denied',\r\n        data: Object.values(analyticsData.reviewStats?.procedureTypeDistribution || {}).map(item => item?.denied || 0),\r\n        backgroundColor: 'rgba(239, 68, 68, 0.7)',\r\n        borderColor: 'rgba(239, 68, 68, 1)',\r\n        borderWidth: 1,\r\n      },\r\n    ],\r\n  };\r\n\r\n  // Review trend data (mock data for now)\r\n  const defaultMonths = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];\r\n  const defaultTotalData = [5, 8, 12, 15, 20, 25];\r\n  const defaultAcceptedData = [4, 6, 9, 12, 16, 20];\r\n\r\n  const reviewTrendData = {\r\n    labels: (analyticsData.reviewStats?.reviewsByMonth || []).length > 0\r\n      ? analyticsData.reviewStats?.reviewsByMonth.map(item => item.month)\r\n      : defaultMonths,\r\n    datasets: [\r\n      {\r\n        label: 'Total Reviews',\r\n        data: (analyticsData.reviewStats?.reviewsByMonth || []).length > 0\r\n          ? analyticsData.reviewStats?.reviewsByMonth.map(item => item.total)\r\n          : defaultTotalData,\r\n        backgroundColor: 'rgba(59, 130, 246, 0.5)',\r\n        borderColor: 'rgba(59, 130, 246, 1)',\r\n        borderWidth: 2,\r\n        tension: 0.4,\r\n        fill: true,\r\n      },\r\n      {\r\n        label: 'Accepted Reviews',\r\n        data: (analyticsData.reviewStats?.reviewsByMonth || []).length > 0\r\n          ? analyticsData.reviewStats?.reviewsByMonth.map(item => item.accepted)\r\n          : defaultAcceptedData,\r\n        backgroundColor: 'rgba(16, 185, 129, 0.5)',\r\n        borderColor: 'rgba(16, 185, 129, 1)',\r\n        borderWidth: 2,\r\n        tension: 0.4,\r\n        fill: false,\r\n      },\r\n    ],\r\n  };\r\n\r\n  // Review quality metrics data\r\n  const reviewQualityData = {\r\n    labels: ['Procedure Quality', 'Patient Interaction', 'Documentation', 'Technique', 'Time Management', 'Communication'],\r\n    datasets: [\r\n      {\r\n        label: 'Average Ratings',\r\n        data: [\r\n          analyticsData.reviewStats?.qualityMetrics?.avgProcedureQuality || 0,\r\n          analyticsData.reviewStats?.qualityMetrics?.avgPatientInteraction || 0,\r\n          analyticsData.reviewStats?.qualityMetrics?.avgDocumentation || 0,\r\n          analyticsData.reviewStats?.qualityMetrics?.avgTechnique || 0,\r\n          analyticsData.reviewStats?.qualityMetrics?.avgTimeManagement || 0,\r\n          analyticsData.reviewStats?.qualityMetrics?.avgCommunication || 0,\r\n        ],\r\n        backgroundColor: 'rgba(59, 130, 246, 0.5)',\r\n        borderColor: 'rgba(59, 130, 246, 1)',\r\n        borderWidth: 2,\r\n        pointBackgroundColor: 'rgba(59, 130, 246, 1)',\r\n        pointBorderColor: '#fff',\r\n        pointHoverBackgroundColor: '#fff',\r\n        pointHoverBorderColor: 'rgba(59, 130, 246, 1)',\r\n        pointRadius: 4,\r\n      },\r\n    ],\r\n  };\r\n\r\n  // Animation variants\r\n  const container = {\r\n    hidden: { opacity: 0 },\r\n    show: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  const item = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    show: { opacity: 1, y: 0 },\r\n  };\r\n\r\n  if (loading) {\r\n    return <Loader />;\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50\">\r\n      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n\r\n        <main className=\"flex-1 overflow-y-auto p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\">\r\n          <div className=\"max-w-7xl mx-auto\">\r\n            {error && (\r\n              <motion.div\r\n                initial={{ opacity: 0, y: -20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                className=\"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\"\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  <svg className=\"w-5 h-5 text-red-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path\r\n                      fillRule=\"evenodd\"\r\n                      d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\"\r\n                      clipRule=\"evenodd\"\r\n                    />\r\n                  </svg>\r\n                  <p className=\"text-red-700 font-medium\">{error}</p>\r\n                </div>\r\n              </motion.div>\r\n            )}\r\n\r\n            <motion.div\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              transition={{ duration: 0.5 }}\r\n            >\r\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\r\n                <div>\r\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\">\r\n                    Analytics Dashboard\r\n                  </h1>\r\n                  <p className=\"text-[#333333]\">Insights for {user?.name || 'Student'}</p>\r\n                </div>\r\n                <div className=\"flex items-center gap-4\">\r\n                  <select\r\n                    value={timeRange}\r\n                    onChange={(e) => setTimeRange(e.target.value)}\r\n                    className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] bg-white text-sm transition-all duration-300\"\r\n                  >\r\n                    <option value=\"week\">Last 7 Days</option>\r\n                    <option value=\"month\">Last 30 Days</option>\r\n                    <option value=\"all\">All Time</option>\r\n                  </select>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Tab Navigation */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                className=\"bg-white rounded-xl shadow-md overflow-hidden mb-8\"\r\n              >\r\n                <div className=\"flex overflow-x-auto\">\r\n                  <button\r\n                    onClick={() => setActiveTab('overview')}\r\n                    className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${\r\n                      activeTab === 'overview'\r\n                        ? 'border-[#20B2AA] text-[#0077B6]'\r\n                        : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)]'\r\n                    }`}\r\n                  >\r\n                    <FaChartBar className=\"mr-2\" />\r\n                    Overview\r\n                  </button>\r\n                  <button\r\n                    onClick={() => setActiveTab('patients')}\r\n                    className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${\r\n                      activeTab === 'patients'\r\n                        ? 'border-[#20B2AA] text-[#0077B6]'\r\n                        : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)]'\r\n                    }`}\r\n                  >\r\n                    <FaUserInjured className=\"mr-2\" />\r\n                    Patients\r\n                  </button>\r\n                  <button\r\n                    onClick={() => setActiveTab('procedures')}\r\n                    className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${\r\n                      activeTab === 'procedures'\r\n                        ? 'border-[#20B2AA] text-[#0077B6]'\r\n                        : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)]'\r\n                    }`}\r\n                  >\r\n                    <FaTooth className=\"mr-2\" />\r\n                    Treatment Sheets\r\n                  </button>\r\n                  <button\r\n                    onClick={() => setActiveTab('reviews')}\r\n                    className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${\r\n                      activeTab === 'reviews'\r\n                        ? 'border-[#20B2AA] text-[#0077B6]'\r\n                        : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)]'\r\n                    }`}\r\n                  >\r\n                    <FaClipboardCheck className=\"mr-2\" />\r\n                    Reviews\r\n                  </button>\r\n                </div>\r\n              </motion.div>\r\n\r\n              {/* Overview Tab */}\r\n              {activeTab === 'overview' && (\r\n                <>\r\n                  {/* Summary Cards */}\r\n                  <motion.div\r\n                    variants={container}\r\n                    initial=\"hidden\"\r\n                    whileInView=\"show\"\r\n                    viewport={{ once: true }}\r\n                    className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\"\r\n                  >\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#28A745] group\"\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"bg-[rgba(0,119,182,0.05)] w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-[rgba(0,119,182,0.1)] transition-colors duration-300\">\r\n                          <FaCalendarAlt className=\"h-6 w-6 text-[#0077B6]\" />\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-sm font-medium text-gray-500\">Appointments</p>\r\n                          <p className=\"text-2xl font-bold text-[#0077B6]\">\r\n                            {analyticsData.appointmentStats.pending +\r\n                             analyticsData.appointmentStats.completed +\r\n                             analyticsData.appointmentStats.cancelled}\r\n                          </p>\r\n                          <div className=\"flex items-center text-xs text-gray-600 mt-1\">\r\n                            <span className=\"flex items-center text-yellow-600 mr-2\">\r\n                              <FaHourglassHalf className=\"h-3 w-3 mr-1\" /> {analyticsData.appointmentStats.pending} pending\r\n                            </span>\r\n                            <span className=\"flex items-center text-green-600\">\r\n                              <FaCheckCircle className=\"h-3 w-3 mr-1\" /> {analyticsData.appointmentStats.completed} completed\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </motion.div>\r\n\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#28A745] group\"\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"bg-green-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-green-100 transition-colors duration-300\">\r\n                          <FaUsers className=\"h-6 w-6 text-green-600\" />\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-sm font-medium text-gray-500\">Patients</p>\r\n                          <p className=\"text-2xl font-bold text-[#0077B6]\">\r\n                            {Object.values(analyticsData.ageDistribution).reduce((a, b) => a + b, 0)}\r\n                          </p>\r\n                          <p className=\"text-xs text-gray-600 mt-1\">\r\n                            {Object.keys(analyticsData.genderDistribution).length > 0 &&\r\n                              `${Math.round((analyticsData.genderDistribution['Male'] || 0) /\r\n                                Object.values(analyticsData.genderDistribution).reduce((a, b) => a + b, 0) * 100)}% male,\r\n                               ${Math.round((analyticsData.genderDistribution['Female'] || 0) /\r\n                                Object.values(analyticsData.genderDistribution).reduce((a, b) => a + b, 0) * 100)}% female`\r\n                            }\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                    </motion.div>\r\n\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#20B2AA] group\"\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"bg-purple-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-purple-100 transition-colors duration-300\">\r\n                          <FaTooth className=\"h-6 w-6 text-purple-600\" />\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-sm font-medium text-gray-500\">Treatment Sheets</p>\r\n                          <p className=\"text-2xl font-bold text-[#0077B6]\">\r\n                            {Object.values(analyticsData.procedureFrequency).reduce((a, b) => a + b, 0)}\r\n                          </p>\r\n                          <p className=\"text-xs text-gray-600 mt-1\">\r\n                            {Object.keys(analyticsData.procedureFrequency).length > 0 &&\r\n                              `Top: ${Object.entries(analyticsData.procedureFrequency)\r\n                                .sort(([,a], [,b]) => b - a)[0][0]}`\r\n                            }\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                    </motion.div>\r\n\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#20B2AA] group\"\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"bg-yellow-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-yellow-100 transition-colors duration-300\">\r\n                          <FaClipboardCheck className=\"h-6 w-6 text-yellow-600\" />\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-sm font-medium text-gray-500\">Reviews</p>\r\n                          <p className=\"text-2xl font-bold text-[#0077B6]\">\r\n                            {analyticsData.reviewStats?.totalReviews || 0}\r\n                          </p>\r\n                          <div className=\"flex items-center text-xs text-gray-600 mt-1\">\r\n                            <span className=\"flex items-center text-green-600 mr-2\">\r\n                              <FaCheckCircle className=\"h-3 w-3 mr-1\" /> {analyticsData.reviewStats?.statusDistribution?.accepted || 0} accepted\r\n                            </span>\r\n                            <span className=\"flex items-center text-yellow-600\">\r\n                              <FaHourglassHalf className=\"h-3 w-3 mr-1\" /> {analyticsData.reviewStats?.statusDistribution?.pending || 0} pending\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </motion.div>\r\n                  </motion.div>\r\n                </>\r\n              )}\r\n\r\n              {/* Overview Tab Charts */}\r\n              {activeTab === 'overview' && (\r\n                <motion.div\r\n                  variants={container}\r\n                  initial=\"hidden\"\r\n                  whileInView=\"show\"\r\n                  viewport={{ once: true }}\r\n                  className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\"\r\n                >\r\n                  {/* Appointment Status */}\r\n                  <motion.div\r\n                    variants={item}\r\n                    className=\"bg-white p-6 rounded-xl shadow-sm border border-[rgba(0,119,182,0.1)] hover:shadow-md transition-all duration-300\"\r\n                  >\r\n                    <div className=\"flex justify-between items-center mb-4\">\r\n                      <h2 className=\"text-xl font-bold text-[#0077B6]\">Appointment Status</h2>\r\n                      <button\r\n                        onClick={() => toggleChartType('appointments')}\r\n                        className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\"\r\n                      >\r\n                        Change Chart\r\n                      </button>\r\n                    </div>\r\n                    <div className=\"h-64\">\r\n                      {chartType.appointments === 'doughnut' && (\r\n                        <Doughnut\r\n                          data={appointmentStatusData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            plugins: {\r\n                              legend: { position: 'bottom' },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                      {chartType.appointments === 'pie' && (\r\n                        <Pie\r\n                          data={appointmentStatusData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            plugins: {\r\n                              legend: { position: 'bottom' },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                      {chartType.appointments === 'bar' && (\r\n                        <Bar\r\n                          data={appointmentStatusData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            scales: {\r\n                              y: { beginAtZero: true },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                    </div>\r\n                  </motion.div>\r\n\r\n                  {/* Appointments Per Month */}\r\n                  <motion.div\r\n                    variants={item}\r\n                    className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\"\r\n                  >\r\n                    <div className=\"flex justify-between items-center mb-4\">\r\n                      <h2 className=\"text-xl font-bold text-[#0077B6]\">Appointments Per Month</h2>\r\n                      <button\r\n                        onClick={() => toggleChartType('appointmentsPerMonth')}\r\n                        className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\"\r\n                      >\r\n                        Change Chart\r\n                      </button>\r\n                    </div>\r\n                    <div className=\"h-64\">\r\n                      {chartType.appointmentsPerMonth === 'line' && (\r\n                        <Line\r\n                          data={appointmentsPerMonthData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            scales: {\r\n                              y: { beginAtZero: true },\r\n                            },\r\n                            plugins: {\r\n                              legend: { position: 'bottom' },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                      {chartType.appointmentsPerMonth === 'bar' && (\r\n                        <Bar\r\n                          data={appointmentsPerMonthData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            scales: {\r\n                              y: { beginAtZero: true },\r\n                            },\r\n                            plugins: {\r\n                              legend: { position: 'bottom' },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                      {chartType.appointmentsPerMonth === 'area' && (\r\n                        <Line\r\n                          data={appointmentsPerMonthData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            scales: {\r\n                              y: { beginAtZero: true },\r\n                            },\r\n                            plugins: {\r\n                              legend: { position: 'bottom' },\r\n                            },\r\n                            elements: {\r\n                              line: {\r\n                                fill: true,\r\n                              },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                    </div>\r\n                  </motion.div>\r\n                </motion.div>\r\n              )}\r\n\r\n              {/* Patients Tab */}\r\n              {activeTab === 'patients' && (\r\n                <motion.div\r\n                  variants={container}\r\n                  initial=\"hidden\"\r\n                  whileInView=\"show\"\r\n                  viewport={{ once: true }}\r\n                  className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\"\r\n                >\r\n                  {/* Age Distribution */}\r\n                  <motion.div\r\n                    variants={item}\r\n                    className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\"\r\n                  >\r\n                    <div className=\"flex justify-between items-center mb-4\">\r\n                      <h2 className=\"text-xl font-bold text-[#0077B6]\">Patient Age Distribution</h2>\r\n                      <button\r\n                        onClick={() => toggleChartType('age')}\r\n                        className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\"\r\n                      >\r\n                        Change Chart\r\n                      </button>\r\n                    </div>\r\n                    <div className=\"h-64\">\r\n                      {chartType.age === 'bar' && (\r\n                        <Bar\r\n                          data={ageDistributionData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            scales: {\r\n                              y: { beginAtZero: true },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                      {chartType.age === 'line' && (\r\n                        <Line\r\n                          data={ageDistributionData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            scales: {\r\n                              y: { beginAtZero: true },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                    </div>\r\n                  </motion.div>\r\n\r\n                  {/* Gender Distribution */}\r\n                  <motion.div\r\n                    variants={item}\r\n                    className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\"\r\n                  >\r\n                    <div className=\"flex justify-between items-center mb-4\">\r\n                      <h2 className=\"text-xl font-bold text-[#0077B6]\">Patient Gender</h2>\r\n                      <button\r\n                        onClick={() => toggleChartType('gender')}\r\n                        className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\"\r\n                      >\r\n                        Change Chart\r\n                      </button>\r\n                    </div>\r\n                    <div className=\"h-64\">\r\n                      {chartType.gender === 'pie' && (\r\n                        <Pie\r\n                          data={genderDistributionData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            plugins: {\r\n                              legend: { position: 'bottom' },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                      {chartType.gender === 'doughnut' && (\r\n                        <Doughnut\r\n                          data={genderDistributionData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            plugins: {\r\n                              legend: { position: 'bottom' },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                      {chartType.gender === 'bar' && (\r\n                        <Bar\r\n                          data={genderDistributionData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            scales: {\r\n                              y: { beginAtZero: true },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                    </div>\r\n                  </motion.div>\r\n\r\n                  {/* Chronic Diseases */}\r\n                  <motion.div\r\n                    variants={item}\r\n                    className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 lg:col-span-2\"\r\n                  >\r\n                    <div className=\"flex justify-between items-center mb-4\">\r\n                      <h2 className=\"text-xl font-bold text-[#0077B6]\">Chronic Disease Prevalence</h2>\r\n                      <button\r\n                        onClick={() => toggleChartType('diseases')}\r\n                        className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\"\r\n                      >\r\n                        Change Chart\r\n                      </button>\r\n                    </div>\r\n                    <div className=\"h-64\">\r\n                      {chartType.diseases === 'doughnut' && (\r\n                        <Doughnut\r\n                          data={chronicDiseaseData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            plugins: {\r\n                              legend: { position: 'bottom' },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                      {chartType.diseases === 'pie' && (\r\n                        <Pie\r\n                          data={chronicDiseaseData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            plugins: {\r\n                              legend: { position: 'bottom' },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                      {chartType.diseases === 'bar' && (\r\n                        <Bar\r\n                          data={chronicDiseaseData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            scales: {\r\n                              y: { beginAtZero: true },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                    </div>\r\n                  </motion.div>\r\n                </motion.div>\r\n              )}\r\n\r\n              {/* Procedures Tab */}\r\n              {activeTab === 'procedures' && (\r\n                <motion.div\r\n                  variants={container}\r\n                  initial=\"hidden\"\r\n                  whileInView=\"show\"\r\n                  viewport={{ once: true }}\r\n                  className=\"grid grid-cols-1 gap-6\"\r\n                >\r\n                  {/* Procedure Frequency */}\r\n                  <motion.div\r\n                    variants={item}\r\n                    className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\"\r\n                  >\r\n                    <div className=\"flex justify-between items-center mb-4\">\r\n                      <h2 className=\"text-xl font-bold text-[#0077B6]\">Treatment Sheet Submissions by Type</h2>\r\n                      <button\r\n                        onClick={() => toggleChartType('procedures')}\r\n                        className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\"\r\n                      >\r\n                        Change Chart\r\n                      </button>\r\n                    </div>\r\n                    <div className=\"h-80\">\r\n                      {chartType.procedures === 'bar' && (\r\n                        <Bar\r\n                          data={procedureFrequencyData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            scales: {\r\n                              y: { beginAtZero: true },\r\n                            },\r\n                            indexAxis: 'y', // Horizontal bars\r\n                          }}\r\n                        />\r\n                      )}\r\n                      {chartType.procedures === 'pie' && (\r\n                        <Pie\r\n                          data={procedureFrequencyData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            plugins: {\r\n                              legend: { position: 'bottom' },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                      {chartType.procedures === 'doughnut' && (\r\n                        <Doughnut\r\n                          data={procedureFrequencyData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            plugins: {\r\n                              legend: { position: 'bottom' },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                    </div>\r\n                  </motion.div>\r\n\r\n                  {/* Appointment Types */}\r\n                  <motion.div\r\n                    variants={item}\r\n                    className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\"\r\n                  >\r\n                    <div className=\"flex justify-between items-center mb-4\">\r\n                      <h2 className=\"text-xl font-bold text-[#0077B6]\">Appointment Types</h2>\r\n                      <button\r\n                        onClick={() => toggleChartType('appointments')}\r\n                        className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\"\r\n                      >\r\n                        Change Chart\r\n                      </button>\r\n                    </div>\r\n                    <div className=\"h-64\">\r\n                      {chartType.appointments === 'doughnut' && (\r\n                        <Doughnut\r\n                          data={appointmentTypesData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            plugins: {\r\n                              legend: { position: 'bottom' },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                      {chartType.appointments === 'pie' && (\r\n                        <Pie\r\n                          data={appointmentTypesData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            plugins: {\r\n                              legend: { position: 'bottom' },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                      {chartType.appointments === 'bar' && (\r\n                        <Bar\r\n                          data={appointmentTypesData}\r\n                          options={{\r\n                            maintainAspectRatio: false,\r\n                            scales: {\r\n                              y: { beginAtZero: true },\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                    </div>\r\n                  </motion.div>\r\n                </motion.div>\r\n              )}\r\n\r\n              {/* Reviews Tab */}\r\n              {activeTab === 'reviews' && (\r\n                <>\r\n                  {/* Review Summary Cards */}\r\n                  <motion.div\r\n                    variants={container}\r\n                    initial=\"hidden\"\r\n                    whileInView=\"show\"\r\n                    viewport={{ once: true }}\r\n                    className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\"\r\n                  >\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100\"\r\n                    >\r\n                      <div className=\"flex items-center mb-4\">\r\n                        <div className=\"bg-[rgba(0,119,182,0.1)] w-10 h-10 rounded-full flex items-center justify-center mr-3\">\r\n                          <FaClipboardCheck className=\"h-5 w-5 text-[#0077B6]\" />\r\n                        </div>\r\n                        <div>\r\n                          <h3 className=\"text-sm font-medium text-gray-500\">Total Reviews</h3>\r\n                          <p className=\"text-2xl font-bold text-[#0077B6]\">\r\n                            {analyticsData.reviewStats?.totalReviews || 0}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                    </motion.div>\r\n\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100\"\r\n                    >\r\n                      <div className=\"flex items-center mb-4\">\r\n                        <div className=\"bg-[rgba(40,167,69,0.1)] w-10 h-10 rounded-full flex items-center justify-center mr-3\">\r\n                          <FaCheckCircle className=\"h-5 w-5 text-[#28A745]\" />\r\n                        </div>\r\n                        <div>\r\n                          <h3 className=\"text-sm font-medium text-gray-500\">Acceptance Rate</h3>\r\n                          <p className=\"text-2xl font-bold text-[#28A745]\">\r\n                            {analyticsData.reviewStats?.acceptanceRate || 0}%\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"h-2 bg-gray-200 rounded-full overflow-hidden\">\r\n                        <div\r\n                          className=\"h-full bg-[#28A745] rounded-full\"\r\n                          style={{ width: `${analyticsData.reviewStats?.acceptanceRate || 0}%` }}\r\n                        ></div>\r\n                      </div>\r\n                    </motion.div>\r\n\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100\"\r\n                    >\r\n                      <div className=\"flex items-center mb-4\">\r\n                        <div className=\"bg-[rgba(32,178,170,0.1)] w-10 h-10 rounded-full flex items-center justify-center mr-3\">\r\n                          <FaStarHalfAlt className=\"h-5 w-5 text-[#20B2AA]\" />\r\n                        </div>\r\n                        <div>\r\n                          <h3 className=\"text-sm font-medium text-gray-500\">Avg. Procedure Quality</h3>\r\n                          <p className=\"text-2xl font-bold text-[#20B2AA]\">\r\n                            {analyticsData.reviewStats?.qualityMetrics?.avgProcedureQuality || 0}/5\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"mt-1\">\r\n                        {renderStars(analyticsData.reviewStats?.qualityMetrics?.avgProcedureQuality || 0)}\r\n                      </div>\r\n                    </motion.div>\r\n\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100\"\r\n                    >\r\n                      <div className=\"flex items-center mb-4\">\r\n                        <div className=\"bg-[rgba(0,119,182,0.1)] w-10 h-10 rounded-full flex items-center justify-center mr-3\">\r\n                          <FaStarHalfAlt className=\"h-5 w-5 text-[#0077B6]\" />\r\n                        </div>\r\n                        <div>\r\n                          <h3 className=\"text-sm font-medium text-gray-500\">Avg. Patient Interaction</h3>\r\n                          <p className=\"text-2xl font-bold text-[#0077B6]\">\r\n                            {analyticsData.reviewStats?.qualityMetrics?.avgPatientInteraction || 0}/5\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"mt-1\">\r\n                        {renderStars(analyticsData.reviewStats?.qualityMetrics?.avgPatientInteraction || 0)}\r\n                      </div>\r\n                    </motion.div>\r\n                  </motion.div>\r\n\r\n                  {/* Review Charts */}\r\n                  <motion.div\r\n                    variants={container}\r\n                    initial=\"hidden\"\r\n                    whileInView=\"show\"\r\n                    viewport={{ once: true }}\r\n                    className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\"\r\n                  >\r\n                    {/* Review Status Distribution */}\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\"\r\n                    >\r\n                      <div className=\"flex justify-between items-center mb-4\">\r\n                        <h2 className=\"text-xl font-bold text-[#0077B6]\">Review Status Distribution</h2>\r\n                        <button\r\n                          onClick={() => toggleChartType('reviewStatus')}\r\n                          className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\"\r\n                        >\r\n                          Change Chart\r\n                        </button>\r\n                      </div>\r\n                      <div className=\"h-64\" ref={statusChartRef}>\r\n                        {chartType.reviewStatus === 'doughnut' && (\r\n                          <Doughnut\r\n                            data={reviewStatusData}\r\n                            options={{\r\n                              maintainAspectRatio: false,\r\n                              plugins: {\r\n                                legend: { position: 'bottom' },\r\n                              },\r\n                            }}\r\n                          />\r\n                        )}\r\n                        {chartType.reviewStatus === 'pie' && (\r\n                          <Pie\r\n                            data={reviewStatusData}\r\n                            options={{\r\n                              maintainAspectRatio: false,\r\n                              plugins: {\r\n                                legend: { position: 'bottom' },\r\n                              },\r\n                            }}\r\n                          />\r\n                        )}\r\n                        {chartType.reviewStatus === 'bar' && (\r\n                          <Bar\r\n                            data={reviewStatusData}\r\n                            options={{\r\n                              maintainAspectRatio: false,\r\n                              scales: {\r\n                                y: { beginAtZero: true },\r\n                              },\r\n                            }}\r\n                          />\r\n                        )}\r\n                      </div>\r\n                    </motion.div>\r\n\r\n                    {/* Review by Procedure Type */}\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\"\r\n                    >\r\n                      <div className=\"flex justify-between items-center mb-4\">\r\n                        <h2 className=\"text-xl font-bold text-[#0077B6]\">Reviews by Procedure Type</h2>\r\n                        <button\r\n                          onClick={() => toggleChartType('reviewProcedures')}\r\n                          className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\"\r\n                        >\r\n                          Change Chart\r\n                        </button>\r\n                      </div>\r\n                      <div className=\"h-64\" ref={procedureChartRef}>\r\n                        {chartType.reviewProcedures === 'bar' && (\r\n                          <Bar\r\n                            data={reviewProcedureData}\r\n                            options={{\r\n                              maintainAspectRatio: false,\r\n                              scales: {\r\n                                x: { stacked: true },\r\n                                y: { stacked: true, beginAtZero: true },\r\n                              },\r\n                            }}\r\n                          />\r\n                        )}\r\n                        {chartType.reviewProcedures === 'line' && (\r\n                          <Line\r\n                            data={reviewProcedureData}\r\n                            options={{\r\n                              maintainAspectRatio: false,\r\n                              scales: {\r\n                                y: { beginAtZero: true },\r\n                              },\r\n                            }}\r\n                          />\r\n                        )}\r\n                      </div>\r\n                    </motion.div>\r\n\r\n                    {/* Review Quality Metrics */}\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\"\r\n                    >\r\n                      <div className=\"flex justify-between items-center mb-4\">\r\n                        <h2 className=\"text-xl font-bold text-[#0077B6]\">Review Quality Metrics</h2>\r\n                        <button\r\n                          onClick={() => toggleChartType('reviewQuality')}\r\n                          className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\"\r\n                        >\r\n                          Change Chart\r\n                        </button>\r\n                      </div>\r\n                      <div className=\"h-64\" ref={qualityMetricsChartRef}>\r\n                        {chartType.reviewQuality === 'radar' && (\r\n                          <Radar\r\n                            data={reviewQualityData}\r\n                            options={{\r\n                              maintainAspectRatio: false,\r\n                              scales: {\r\n                                r: {\r\n                                  beginAtZero: true,\r\n                                  max: 5,\r\n                                  ticks: {\r\n                                    stepSize: 1\r\n                                  }\r\n                                }\r\n                              },\r\n                            }}\r\n                          />\r\n                        )}\r\n                        {chartType.reviewQuality === 'bar' && (\r\n                          <Bar\r\n                            data={reviewQualityData}\r\n                            options={{\r\n                              maintainAspectRatio: false,\r\n                              scales: {\r\n                                y: {\r\n                                  beginAtZero: true,\r\n                                  max: 5,\r\n                                  ticks: {\r\n                                    stepSize: 1\r\n                                  }\r\n                                },\r\n                              },\r\n                            }}\r\n                          />\r\n                        )}\r\n                        {chartType.reviewQuality === 'polarArea' && (\r\n                          <PolarArea\r\n                            data={reviewQualityData}\r\n                            options={{\r\n                              maintainAspectRatio: false,\r\n                              scales: {\r\n                                r: {\r\n                                  beginAtZero: true,\r\n                                  max: 5,\r\n                                  ticks: {\r\n                                    stepSize: 1\r\n                                  }\r\n                                }\r\n                              },\r\n                            }}\r\n                          />\r\n                        )}\r\n                      </div>\r\n                    </motion.div>\r\n\r\n                    {/* Review Trend Over Time */}\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300\"\r\n                    >\r\n                      <div className=\"flex justify-between items-center mb-4\">\r\n                        <h2 className=\"text-xl font-bold text-[#0077B6]\">Review Trend Over Time</h2>\r\n                        <button\r\n                          onClick={() => toggleChartType('reviewTrend')}\r\n                          className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-all duration-300 text-sm\"\r\n                        >\r\n                          Change Chart\r\n                        </button>\r\n                      </div>\r\n                      <div className=\"h-64\" ref={reviewTrendChartRef}>\r\n                        {chartType.reviewTrend === 'line' && (\r\n                          <Line\r\n                            data={reviewTrendData}\r\n                            options={{\r\n                              maintainAspectRatio: false,\r\n                              scales: {\r\n                                y: { beginAtZero: true },\r\n                              },\r\n                            }}\r\n                          />\r\n                        )}\r\n                        {chartType.reviewTrend === 'bar' && (\r\n                          <Bar\r\n                            data={reviewTrendData}\r\n                            options={{\r\n                              maintainAspectRatio: false,\r\n                              scales: {\r\n                                y: { beginAtZero: true },\r\n                              },\r\n                            }}\r\n                          />\r\n                        )}\r\n                      </div>\r\n                    </motion.div>\r\n                  </motion.div>\r\n                </>\r\n              )}\r\n            </motion.div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Analytics;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,OAAO,EACPC,OAAO,EACPC,aAAa,EACbC,gBAAgB,EAChBC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,MAAM,EACNC,aAAa,EACbC,UAAU,QACL,gBAAgB;AACvB,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,UAAU,EACVC,YAAY,EACZC,WAAW,EACXC,iBAAiB,EACjBC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,MAAM,QACD,UAAU;AACjB,SAASC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,EAAEC,KAAK,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7ErB,OAAO,CAACsB,QAAQ,CACdrB,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,UAAU,EACVC,YAAY,EACZC,WAAW,EACXC,iBAAiB,EACjBC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,MACF,CAAC;AAED,MAAMY,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;EACtB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6F,OAAO,EAAEC,UAAU,CAAC,GAAG9F,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+F,KAAK,EAAEC,QAAQ,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiG,SAAS,EAAEC,YAAY,CAAC,GAAGlG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACnD,MAAM,CAACmG,SAAS,EAAEC,YAAY,CAAC,GAAGpG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;EACxD,MAAM,CAACqG,SAAS,EAAEC,YAAY,CAAC,GAAGtG,QAAQ,CAAC;IACzCuG,YAAY,EAAE,UAAU;IACxBC,oBAAoB,EAAE,MAAM;IAC5BC,GAAG,EAAE,KAAK;IACVC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,UAAU;IACpBC,YAAY,EAAE,UAAU;IACxBC,gBAAgB,EAAE,KAAK;IACvBC,WAAW,EAAE,MAAM;IACnBC,aAAa,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAMC,cAAc,GAAG/G,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMgH,iBAAiB,GAAGhH,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMiH,mBAAmB,GAAGjH,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMkH,sBAAsB,GAAGlH,MAAM,CAAC,IAAI,CAAC;EAE3C,MAAM,CAACmH,aAAa,EAAEC,gBAAgB,CAAC,GAAGtH,QAAQ,CAAC;IACjDuH,gBAAgB,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,SAAS,EAAE,CAAC;MAAEC,SAAS,EAAE;IAAE,CAAC;IAC5DC,gBAAgB,EAAE,CAAC,CAAC;IACpBC,eAAe,EAAE,CAAC,CAAC;IACnBC,kBAAkB,EAAE,CAAC,CAAC;IACtBC,kBAAkB,EAAE,CAAC,CAAC;IACtBC,wBAAwB,EAAE,CAAC,CAAC;IAC5BC,WAAW,EAAE;MACXC,kBAAkB,EAAE;QAAEC,QAAQ,EAAE,CAAC;QAAEV,OAAO,EAAE,CAAC;QAAEW,MAAM,EAAE;MAAE,CAAC;MAC1DC,yBAAyB,EAAE,CAAC,CAAC;MAC7BC,cAAc,EAAE;QACdC,mBAAmB,EAAE,CAAC;QACtBC,qBAAqB,EAAE;MACzB,CAAC;MACDC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE,CAAC;MACjBC,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE;IAChB;EACF,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAGzI,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE0I,IAAI;IAAEC;EAAM,CAAC,GAAGxI,OAAO,CAAC,CAAC;EAEjCL,SAAS,CAAC,MAAM;IACd,MAAM8I,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI,CAACF,IAAI,IAAI,CAACC,KAAK,EAAE;QACnB9C,QAAQ,CAAC,kCAAkC,CAAC;QAC5CF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAI;QACF,MAAMkD,MAAM,GAAG;UACbC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUJ,KAAK;UAAG,CAAC;UAC7CK,MAAM,EAAE;YAAEC,KAAK,EAAEnD;UAAU;QAC7B,CAAC;;QAED;QACA,MAAMoD,iBAAiB,GAAG,MAAMhJ,KAAK,CAACiJ,GAAG,CAAC,qCAAqC,EAAEN,MAAM,CAAC;;QAExF;QACA,IAAIO,WAAW,GAAG;UAChBtB,kBAAkB,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEV,OAAO,EAAE,CAAC;YAAEW,MAAM,EAAE;UAAE,CAAC;UAC1DC,yBAAyB,EAAE,CAAC,CAAC;UAC7BC,cAAc,EAAE;YACdC,mBAAmB,EAAE,CAAC;YACtBC,qBAAqB,EAAE,CAAC;YACxBiB,gBAAgB,EAAE,CAAC;YACnBC,YAAY,EAAE,CAAC;YACfC,iBAAiB,EAAE,CAAC;YACpBC,gBAAgB,EAAE;UACpB,CAAC;UACDnB,cAAc,EAAE,EAAE;UAClBC,cAAc,EAAE,CAAC;UACjBC,UAAU,EAAE,CAAC;UACbC,YAAY,EAAE;QAChB,CAAC;QAED,IAAIE,IAAI,CAACe,SAAS,EAAE;UAClB,IAAI;YACF,MAAMC,eAAe,GAAG,MAAMxJ,KAAK,CAACiJ,GAAG,CAAC,uDAAuDT,IAAI,CAACe,SAAS,EAAE,EAAEZ,MAAM,CAAC;YACxH,MAAMc,OAAO,GAAGD,eAAe,CAACE,IAAI;YAEpC,IAAIC,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,IAAIA,OAAO,CAACI,MAAM,GAAG,CAAC,EAAE;cAChD;cACA,MAAMvB,YAAY,GAAGmB,OAAO,CAACI,MAAM;cACnC,MAAMC,cAAc,GAAGL,OAAO,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,CAAC;cAClE,MAAMC,WAAW,GAAGT,OAAO,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,CAAC;cAC/D,MAAME,eAAe,GAAGD,WAAW,CAACH,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,UAAU,CAAC;cACxE,MAAMG,aAAa,GAAGF,WAAW,CAACH,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC;cAEpE,MAAM7B,cAAc,GAAGE,YAAY,GAAG,CAAC,GAAG,CAAE6B,eAAe,CAACN,MAAM,GAAGvB,YAAY,GAAI,GAAG,EAAE+B,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;cACxG,MAAMhC,UAAU,GAAGC,YAAY,GAAG,CAAC,GAAG,CAAE8B,aAAa,CAACP,MAAM,GAAGvB,YAAY,GAAI,GAAG,EAAE+B,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;;cAElG;cACA,MAAMpC,mBAAmB,GAAGiC,WAAW,CAACL,MAAM,GAAG,CAAC,GAC9C,CAACK,WAAW,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEP,CAAC,KAAKO,GAAG,IAAIP,CAAC,CAACQ,gBAAgB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGN,WAAW,CAACL,MAAM,EAAEQ,OAAO,CAAC,CAAC,CAAC,GACpG,CAAC;cACL,MAAMnC,qBAAqB,GAAGgC,WAAW,CAACL,MAAM,GAAG,CAAC,GAChD,CAACK,WAAW,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEP,CAAC,KAAKO,GAAG,IAAIP,CAAC,CAACS,kBAAkB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGP,WAAW,CAACL,MAAM,EAAEQ,OAAO,CAAC,CAAC,CAAC,GACtG,CAAC;;cAEL;cACA,MAAMK,cAAc,GAAG,CAAC,CAAC;cACzBjB,OAAO,CAACkB,OAAO,CAACC,MAAM,IAAI;gBACxB,MAAMC,IAAI,GAAGD,MAAM,CAACE,aAAa,IAAI,SAAS;gBAC9C,IAAI,CAACJ,cAAc,CAACG,IAAI,CAAC,EAAE;kBACzBH,cAAc,CAACG,IAAI,CAAC,GAAG;oBACrBE,KAAK,EAAE,CAAC;oBACRlD,QAAQ,EAAE,CAAC;oBACXC,MAAM,EAAE,CAAC;oBACTX,OAAO,EAAE;kBACX,CAAC;gBACH;gBACAuD,cAAc,CAACG,IAAI,CAAC,CAACE,KAAK,EAAE;gBAC5B,IAAIH,MAAM,CAACX,MAAM,KAAK,UAAU,EAAES,cAAc,CAACG,IAAI,CAAC,CAAChD,QAAQ,EAAE,CAAC,KAC7D,IAAI+C,MAAM,CAACX,MAAM,KAAK,QAAQ,EAAES,cAAc,CAACG,IAAI,CAAC,CAAC/C,MAAM,EAAE,CAAC,KAC9D4C,cAAc,CAACG,IAAI,CAAC,CAAC1D,OAAO,EAAE;cACrC,CAAC,CAAC;;cAEF;cACA,MAAMgB,cAAc,GAAG,EAAE;cACzB,MAAM6C,SAAS,GAAG,CAAC,CAAC;cAEpBvB,OAAO,CAACkB,OAAO,CAACC,MAAM,IAAI;gBACxB,MAAMK,IAAI,GAAG,IAAIC,IAAI,CAACN,MAAM,CAACO,aAAa,CAAC;gBAC3C,MAAMC,SAAS,GAAG,GAAGH,IAAI,CAACI,cAAc,CAAC,SAAS,EAAE;kBAAEC,KAAK,EAAE;gBAAQ,CAAC,CAAC,IAAIL,IAAI,CAACM,WAAW,CAAC,CAAC,EAAE;gBAE/F,IAAI,CAACP,SAAS,CAACI,SAAS,CAAC,EAAE;kBACzBJ,SAAS,CAACI,SAAS,CAAC,GAAG;oBACrBE,KAAK,EAAEL,IAAI,CAACI,cAAc,CAAC,SAAS,EAAE;sBAAEC,KAAK,EAAE;oBAAQ,CAAC,CAAC;oBACzDE,IAAI,EAAEP,IAAI,CAACM,WAAW,CAAC,CAAC;oBACxBR,KAAK,EAAE,CAAC;oBACRlD,QAAQ,EAAE,CAAC;oBACXV,OAAO,EAAE,CAAC;oBACVW,MAAM,EAAE;kBACV,CAAC;gBACH;gBAEAkD,SAAS,CAACI,SAAS,CAAC,CAACL,KAAK,EAAE;gBAC5B,IAAIH,MAAM,CAACX,MAAM,KAAK,UAAU,EAAEe,SAAS,CAACI,SAAS,CAAC,CAACvD,QAAQ,EAAE,CAAC,KAC7D,IAAI+C,MAAM,CAACX,MAAM,KAAK,SAAS,EAAEe,SAAS,CAACI,SAAS,CAAC,CAACjE,OAAO,EAAE,CAAC,KAChE6D,SAAS,CAACI,SAAS,CAAC,CAACtD,MAAM,EAAE;cACpC,CAAC,CAAC;;cAEF;cACA2D,MAAM,CAACC,MAAM,CAACV,SAAS,CAAC,CAACL,OAAO,CAACW,KAAK,IAAI;gBACxCnD,cAAc,CAACwD,IAAI,CAACL,KAAK,CAAC;cAC5B,CAAC,CAAC;cAEFnD,cAAc,CAACyD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;gBAC5B,IAAID,CAAC,CAACL,IAAI,KAAKM,CAAC,CAACN,IAAI,EAAE,OAAOK,CAAC,CAACL,IAAI,GAAGM,CAAC,CAACN,IAAI;gBAC7C,MAAMO,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;gBACnG,OAAOA,MAAM,CAACC,OAAO,CAACH,CAAC,CAACP,KAAK,CAAC,GAAGS,MAAM,CAACC,OAAO,CAACF,CAAC,CAACR,KAAK,CAAC;cAC1D,CAAC,CAAC;;cAEF;cACApC,WAAW,GAAG;gBACZtB,kBAAkB,EAAE;kBAClBC,QAAQ,EAAEsC,eAAe,CAACN,MAAM;kBAChC1C,OAAO,EAAE2C,cAAc,CAACD,MAAM;kBAC9B/B,MAAM,EAAEsC,aAAa,CAACP;gBACxB,CAAC;gBACD9B,yBAAyB,EAAE2C,cAAc;gBACzC1C,cAAc,EAAE;kBACdC,mBAAmB,EAAEgE,UAAU,CAAChE,mBAAmB,CAAC;kBACpDC,qBAAqB,EAAE+D,UAAU,CAAC/D,qBAAqB,CAAC;kBACxDiB,gBAAgB,EAAE8C,UAAU,CAAChE,mBAAmB,CAAC;kBAAE;kBACnDmB,YAAY,EAAE6C,UAAU,CAAChE,mBAAmB,CAAC;kBAAE;kBAC/CoB,iBAAiB,EAAE4C,UAAU,CAAC/D,qBAAqB,CAAC;kBAAE;kBACtDoB,gBAAgB,EAAE2C,UAAU,CAAC/D,qBAAqB,CAAC,CAAC;gBACtD,CAAC;gBACDC,cAAc,EAAEA,cAAc;gBAC9BC,cAAc,EAAE6D,UAAU,CAAC7D,cAAc,CAAC;gBAC1CC,UAAU,EAAE4D,UAAU,CAAC5D,UAAU,CAAC;gBAClCC,YAAY,EAAEA;cAChB,CAAC;YACH;UACF,CAAC,CAAC,OAAO4D,GAAG,EAAE;YACZC,OAAO,CAACzG,KAAK,CAAC,yBAAyB,EAAEwG,GAAG,CAAC;YAC7C;UACF;QACF;;QAEA;QACA,MAAME,eAAe,GAAG;UACtB,GAAGpD,iBAAiB,CAACU,IAAI;UACzBnC,eAAe,EAAE8E,gBAAgB,CAACrD,iBAAiB,CAACU,IAAI,CAACnC,eAAe,CAAC;UACzEG,wBAAwB,EAAE4E,oBAAoB,CAACtD,iBAAiB,CAACU,IAAI,CAAChC,wBAAwB,CAAC;UAC/F;UACAD,kBAAkB,EAAEuB,iBAAiB,CAACU,IAAI,CAACpD,UAAU,GACnD0C,iBAAiB,CAACU,IAAI,CAACpD,UAAU,CAACgE,MAAM,CAAC,CAACiC,GAAG,EAAEC,IAAI,KAAK;YACtDD,GAAG,CAACC,IAAI,CAACC,IAAI,CAAC,GAAGD,IAAI,CAACE,KAAK;YAC3B,OAAOH,GAAG;UACZ,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;UACb5E,WAAW,EAAEuB;QACf,CAAC;QAEDjC,gBAAgB,CAACmF,eAAe,CAAC;QACjC3G,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOyG,GAAG,EAAE;QACZC,OAAO,CAACzG,KAAK,CAAC,wBAAwB,EAAEwG,GAAG,CAAC;QAC5C,IAAIS,YAAY,GAAG,+BAA+B;QAClD,IAAIT,GAAG,CAACU,QAAQ,EAAE;UAAA,IAAAC,kBAAA;UAChB,IAAIX,GAAG,CAACU,QAAQ,CAAC3C,MAAM,KAAK,GAAG,EAAE;YAC/B1B,QAAQ,CAAC,QAAQ,CAAC;YAClB;UACF;UACAoE,YAAY,GAAG,EAAAE,kBAAA,GAAAX,GAAG,CAACU,QAAQ,CAAClD,IAAI,cAAAmD,kBAAA,uBAAjBA,kBAAA,CAAmBC,OAAO,KAAIH,YAAY;QAC3D;QACAhH,QAAQ,CAACgH,YAAY,CAAC;QACtBlH,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDiD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACF,IAAI,EAAEC,KAAK,EAAEF,QAAQ,EAAE3C,SAAS,CAAC,CAAC;;EAEtC;EACA,MAAMyG,gBAAgB,GAAIU,OAAO,IAAK;IACpC,OAAOtB,MAAM,CAACuB,OAAO,CAACD,OAAO,CAAC,CAC3BnB,IAAI,CAAC,CAAC,CAACC,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKmB,QAAQ,CAACpB,CAAC,CAACqB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGD,QAAQ,CAACnB,CAAC,CAACoB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACzE5C,MAAM,CAAC,CAACiC,GAAG,EAAE,CAACY,GAAG,EAAEC,KAAK,CAAC,MAAM;MAAE,GAAGb,GAAG;MAAE,CAACY,GAAG,GAAGC;IAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClE,CAAC;EAED,MAAMd,oBAAoB,GAAIe,WAAW,IAAK;IAC5C,MAAMC,MAAM,GAAG7B,MAAM,CAACuB,OAAO,CAACK,WAAW,CAAC,CACvCzB,IAAI,CAAC,CAAC,GAAEC,CAAC,CAAC,EAAE,GAAEC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAC3B0B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,OAAO9B,MAAM,CAAC+B,WAAW,CAACF,MAAM,CAAC;EACnC,CAAC;;EAED;EACA,MAAMG,eAAe,GAAIC,QAAQ,IAAK;IACpC,MAAMC,UAAU,GAAG;MACjBzH,YAAY,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,CAAC;MACxCC,oBAAoB,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;MAC7CC,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;MACpBC,MAAM,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC;MAClCC,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC;MACtCC,QAAQ,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,CAAC;MACpCC,YAAY,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,CAAC;MACxCC,gBAAgB,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;MACjCC,WAAW,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;MAC5BC,aAAa,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,WAAW;IAC7C,CAAC;IAED,MAAMiH,WAAW,GAAG5H,SAAS,CAAC0H,QAAQ,CAAC;IACvC,MAAMG,KAAK,GAAGF,UAAU,CAACD,QAAQ,CAAC;IAClC,MAAMI,YAAY,GAAGD,KAAK,CAAC7B,OAAO,CAAC4B,WAAW,CAAC;IAC/C,MAAMG,SAAS,GAAG,CAACD,YAAY,GAAG,CAAC,IAAID,KAAK,CAAChE,MAAM;IAEnD5D,YAAY,CAAC;MACX,GAAGD,SAAS;MACZ,CAAC0H,QAAQ,GAAGG,KAAK,CAACE,SAAS;IAC7B,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,WAAW,GAAIC,MAAM,iBACzB7L,OAAA;IAAK8L,SAAS,EAAC,MAAM;IAAAC,QAAA,EAClB,CAAC,GAAGxE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACyE,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtBlM,OAAA,CAACvB,MAAM;MAELqN,SAAS,EAAE,WAAWI,CAAC,IAAIL,MAAM,IAAI,CAAC,CAAC,GAAG,iBAAiB,GAAG,eAAe;IAAG,GAD3EK,CAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEP,CACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;;EAED;EACA,MAAMC,qBAAqB,GAAG;IAC5BC,MAAM,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,CAAC;IAC7CC,QAAQ,EAAE,CAAC;MACTnF,IAAI,EAAE,CACJ1C,aAAa,CAACE,gBAAgB,CAACC,OAAO,EACtCH,aAAa,CAACE,gBAAgB,CAACE,SAAS,EACxCJ,aAAa,CAACE,gBAAgB,CAACG,SAAS,CACzC;MACDyH,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAClDC,oBAAoB,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;IACxD,CAAC;EACH,CAAC;EAED,MAAMC,oBAAoB,GAAG;IAC3BJ,MAAM,EAAEnD,MAAM,CAACwD,IAAI,CAACjI,aAAa,CAACM,gBAAgB,CAAC;IACnDuH,QAAQ,EAAE,CAAC;MACTnF,IAAI,EAAE+B,MAAM,CAACC,MAAM,CAAC1E,aAAa,CAACM,gBAAgB,CAAC;MACnDwH,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;IACzE,CAAC;EACH,CAAC;;EAED;EACA,MAAMI,wBAAwB,GAAG;IAC/BN,MAAM,EAAE,CAAC5H,aAAa,CAACb,oBAAoB,IAAI,EAAE,EAAEiI,GAAG,CAACe,IAAI,IAAIA,IAAI,CAAC7D,KAAK,CAAC,IAAI,EAAE;IAChFuD,QAAQ,EAAE,CACR;MACEO,KAAK,EAAE,oBAAoB;MAC3B1F,IAAI,EAAE,CAAC1C,aAAa,CAACb,oBAAoB,IAAI,EAAE,EAAEiI,GAAG,CAACe,IAAI,IAAIA,IAAI,CAACpE,KAAK,CAAC,IAAI,EAAE;MAC9E+D,eAAe,EAAE,yBAAyB;MAC1CO,WAAW,EAAE,uBAAuB;MACpCC,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE;IACR,CAAC,EACD;MACEJ,KAAK,EAAE,WAAW;MAClB1F,IAAI,EAAE,CAAC1C,aAAa,CAACb,oBAAoB,IAAI,EAAE,EAAEiI,GAAG,CAACe,IAAI,IAAIA,IAAI,CAAC/H,SAAS,CAAC,IAAI,EAAE;MAClF0H,eAAe,EAAE,yBAAyB;MAC1CO,WAAW,EAAE,uBAAuB;MACpCC,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE;IACR,CAAC,EACD;MACEJ,KAAK,EAAE,SAAS;MAChB1F,IAAI,EAAE,CAAC1C,aAAa,CAACb,oBAAoB,IAAI,EAAE,EAAEiI,GAAG,CAACe,IAAI,IAAIA,IAAI,CAAChI,OAAO,CAAC,IAAI,EAAE;MAChF2H,eAAe,EAAE,yBAAyB;MAC1CO,WAAW,EAAE,uBAAuB;MACpCC,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC;EAED,MAAMC,mBAAmB,GAAG;IAC1Bb,MAAM,EAAEnD,MAAM,CAACwD,IAAI,CAACjI,aAAa,CAACO,eAAe,CAAC;IAClDsH,QAAQ,EAAE,CAAC;MACTO,KAAK,EAAE,UAAU;MACjB1F,IAAI,EAAE+B,MAAM,CAACC,MAAM,CAAC1E,aAAa,CAACO,eAAe,CAAC;MAClDuH,eAAe,EAAE,SAAS;MAC1BO,WAAW,EAAE,SAAS;MACtBC,WAAW,EAAE;IACf,CAAC;EACH,CAAC;EAED,MAAMI,sBAAsB,GAAG;IAC7Bd,MAAM,EAAEnD,MAAM,CAACwD,IAAI,CAACjI,aAAa,CAACQ,kBAAkB,CAAC;IACrDqH,QAAQ,EAAE,CAAC;MACTnF,IAAI,EAAE+B,MAAM,CAACC,MAAM,CAAC1E,aAAa,CAACQ,kBAAkB,CAAC;MACrDsH,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAClDC,oBAAoB,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;IACxD,CAAC;EACH,CAAC;EAED,MAAMY,sBAAsB,GAAG;IAC7Bf,MAAM,EAAEnD,MAAM,CAACwD,IAAI,CAACjI,aAAa,CAACS,kBAAkB,CAAC;IACrDoH,QAAQ,EAAE,CAAC;MACTO,KAAK,EAAE,kBAAkB;MACzB1F,IAAI,EAAE+B,MAAM,CAACC,MAAM,CAAC1E,aAAa,CAACS,kBAAkB,CAAC;MACrDqH,eAAe,EAAE,SAAS;MAC1BO,WAAW,EAAE,SAAS;MACtBC,WAAW,EAAE;IACf,CAAC;EACH,CAAC;EAED,MAAMM,kBAAkB,GAAG;IACzBhB,MAAM,EAAEnD,MAAM,CAACwD,IAAI,CAACjI,aAAa,CAACU,wBAAwB,CAAC;IAC3DmH,QAAQ,EAAE,CAAC;MACTnF,IAAI,EAAE+B,MAAM,CAACC,MAAM,CAAC1E,aAAa,CAACU,wBAAwB,CAAC;MAC3DoH,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACxEC,oBAAoB,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;IAC9E,CAAC;EACH,CAAC;;EAED;EACA,MAAMc,gBAAgB,GAAG;IACvBjB,MAAM,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC;IACzCC,QAAQ,EAAE,CAAC;MACTnF,IAAI,EAAE,CACJ,EAAAhH,qBAAA,GAAAsE,aAAa,CAACW,WAAW,cAAAjF,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2BkF,kBAAkB,cAAAjF,sBAAA,uBAA7CA,sBAAA,CAA+CkF,QAAQ,KAAI,CAAC,EAC5D,EAAAjF,sBAAA,GAAAoE,aAAa,CAACW,WAAW,cAAA/E,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2BgF,kBAAkB,cAAA/E,sBAAA,uBAA7CA,sBAAA,CAA+CsE,OAAO,KAAI,CAAC,EAC3D,EAAArE,sBAAA,GAAAkE,aAAa,CAACW,WAAW,cAAA7E,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2B8E,kBAAkB,cAAA7E,sBAAA,uBAA7CA,sBAAA,CAA+C+E,MAAM,KAAI,CAAC,CAC3D;MACDgH,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAClDC,oBAAoB,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACvDO,WAAW,EAAE;IACf,CAAC;EACH,CAAC;;EAED;EACA,MAAMQ,mBAAmB,GAAG;IAC1BlB,MAAM,EAAEnD,MAAM,CAACwD,IAAI,CAAC,EAAAjM,sBAAA,GAAAgE,aAAa,CAACW,WAAW,cAAA3E,sBAAA,uBAAzBA,sBAAA,CAA2B+E,yBAAyB,KAAI,CAAC,CAAC,CAAC;IAC/E8G,QAAQ,EAAE,CACR;MACEO,KAAK,EAAE,UAAU;MACjB1F,IAAI,EAAE+B,MAAM,CAACC,MAAM,CAAC,EAAAzI,sBAAA,GAAA+D,aAAa,CAACW,WAAW,cAAA1E,sBAAA,uBAAzBA,sBAAA,CAA2B8E,yBAAyB,KAAI,CAAC,CAAC,CAAC,CAACqG,GAAG,CAACe,IAAI,IAAI,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEtH,QAAQ,KAAI,CAAC,CAAC;MAChHiH,eAAe,EAAE,yBAAyB;MAC1CO,WAAW,EAAE,uBAAuB;MACpCC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,KAAK,EAAE,SAAS;MAChB1F,IAAI,EAAE+B,MAAM,CAACC,MAAM,CAAC,EAAAxI,sBAAA,GAAA8D,aAAa,CAACW,WAAW,cAAAzE,sBAAA,uBAAzBA,sBAAA,CAA2B6E,yBAAyB,KAAI,CAAC,CAAC,CAAC,CAACqG,GAAG,CAACe,IAAI,IAAI,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEhI,OAAO,KAAI,CAAC,CAAC;MAC/G2H,eAAe,EAAE,yBAAyB;MAC1CO,WAAW,EAAE,uBAAuB;MACpCC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,KAAK,EAAE,QAAQ;MACf1F,IAAI,EAAE+B,MAAM,CAACC,MAAM,CAAC,EAAAvI,uBAAA,GAAA6D,aAAa,CAACW,WAAW,cAAAxE,uBAAA,uBAAzBA,uBAAA,CAA2B4E,yBAAyB,KAAI,CAAC,CAAC,CAAC,CAACqG,GAAG,CAACe,IAAI,IAAI,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAErH,MAAM,KAAI,CAAC,CAAC;MAC9GgH,eAAe,EAAE,wBAAwB;MACzCO,WAAW,EAAE,sBAAsB;MACnCC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAMS,aAAa,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAChE,MAAMC,gBAAgB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC/C,MAAMC,mBAAmB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAEjD,MAAMC,eAAe,GAAG;IACtBtB,MAAM,EAAE,CAAC,EAAAxL,uBAAA,GAAA4D,aAAa,CAACW,WAAW,cAAAvE,uBAAA,uBAAzBA,uBAAA,CAA2B+E,cAAc,KAAI,EAAE,EAAE0B,MAAM,GAAG,CAAC,IAAAxG,uBAAA,GAChE2D,aAAa,CAACW,WAAW,cAAAtE,uBAAA,uBAAzBA,uBAAA,CAA2B8E,cAAc,CAACiG,GAAG,CAACe,IAAI,IAAIA,IAAI,CAAC7D,KAAK,CAAC,GACjEyE,aAAa;IACjBlB,QAAQ,EAAE,CACR;MACEO,KAAK,EAAE,eAAe;MACtB1F,IAAI,EAAE,CAAC,EAAApG,uBAAA,GAAA0D,aAAa,CAACW,WAAW,cAAArE,uBAAA,uBAAzBA,uBAAA,CAA2B6E,cAAc,KAAI,EAAE,EAAE0B,MAAM,GAAG,CAAC,IAAAtG,uBAAA,GAC9DyD,aAAa,CAACW,WAAW,cAAApE,uBAAA,uBAAzBA,uBAAA,CAA2B4E,cAAc,CAACiG,GAAG,CAACe,IAAI,IAAIA,IAAI,CAACpE,KAAK,CAAC,GACjEiF,gBAAgB;MACpBlB,eAAe,EAAE,yBAAyB;MAC1CO,WAAW,EAAE,uBAAuB;MACpCC,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE;IACR,CAAC,EACD;MACEJ,KAAK,EAAE,kBAAkB;MACzB1F,IAAI,EAAE,CAAC,EAAAlG,uBAAA,GAAAwD,aAAa,CAACW,WAAW,cAAAnE,uBAAA,uBAAzBA,uBAAA,CAA2B2E,cAAc,KAAI,EAAE,EAAE0B,MAAM,GAAG,CAAC,IAAApG,uBAAA,GAC9DuD,aAAa,CAACW,WAAW,cAAAlE,uBAAA,uBAAzBA,uBAAA,CAA2B0E,cAAc,CAACiG,GAAG,CAACe,IAAI,IAAIA,IAAI,CAACtH,QAAQ,CAAC,GACpEoI,mBAAmB;MACvBnB,eAAe,EAAE,yBAAyB;MAC1CO,WAAW,EAAE,uBAAuB;MACpCC,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC;;EAED;EACA,MAAMW,iBAAiB,GAAG;IACxBvB,MAAM,EAAE,CAAC,mBAAmB,EAAE,qBAAqB,EAAE,eAAe,EAAE,WAAW,EAAE,iBAAiB,EAAE,eAAe,CAAC;IACtHC,QAAQ,EAAE,CACR;MACEO,KAAK,EAAE,iBAAiB;MACxB1F,IAAI,EAAE,CACJ,EAAAhG,uBAAA,GAAAsD,aAAa,CAACW,WAAW,cAAAjE,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BsE,cAAc,cAAArE,uBAAA,uBAAzCA,uBAAA,CAA2CsE,mBAAmB,KAAI,CAAC,EACnE,EAAArE,uBAAA,GAAAoD,aAAa,CAACW,WAAW,cAAA/D,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BoE,cAAc,cAAAnE,uBAAA,uBAAzCA,uBAAA,CAA2CqE,qBAAqB,KAAI,CAAC,EACrE,EAAApE,uBAAA,GAAAkD,aAAa,CAACW,WAAW,cAAA7D,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BkE,cAAc,cAAAjE,uBAAA,uBAAzCA,uBAAA,CAA2CoF,gBAAgB,KAAI,CAAC,EAChE,EAAAnF,uBAAA,GAAAgD,aAAa,CAACW,WAAW,cAAA3D,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BgE,cAAc,cAAA/D,uBAAA,uBAAzCA,uBAAA,CAA2CmF,YAAY,KAAI,CAAC,EAC5D,EAAAlF,uBAAA,GAAA8C,aAAa,CAACW,WAAW,cAAAzD,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B8D,cAAc,cAAA7D,uBAAA,uBAAzCA,uBAAA,CAA2CkF,iBAAiB,KAAI,CAAC,EACjE,EAAAjF,uBAAA,GAAA4C,aAAa,CAACW,WAAW,cAAAvD,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B4D,cAAc,cAAA3D,uBAAA,uBAAzCA,uBAAA,CAA2CiF,gBAAgB,KAAI,CAAC,CACjE;MACDwF,eAAe,EAAE,yBAAyB;MAC1CO,WAAW,EAAE,uBAAuB;MACpCC,WAAW,EAAE,CAAC;MACdc,oBAAoB,EAAE,uBAAuB;MAC7CC,gBAAgB,EAAE,MAAM;MACxBC,yBAAyB,EAAE,MAAM;MACjCC,qBAAqB,EAAE,uBAAuB;MAC9CC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MACJD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACVC,eAAe,EAAE;MACnB;IACF;EACF,CAAC;EAED,MAAM3B,IAAI,GAAG;IACXuB,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEI,CAAC,EAAE;IAAG,CAAC;IAC7BH,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEI,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAIvL,OAAO,EAAE;IACX,oBAAOpD,OAAA,CAAChC,MAAM;MAAAmO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;EAEA,oBACEtM,OAAA;IAAK8L,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvC/L,OAAA,CAACjC,OAAO;MAAC6Q,MAAM,EAAE1L,WAAY;MAAC2L,SAAS,EAAE1L;IAAe;MAAAgJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE3DtM,OAAA;MAAK8L,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnD/L,OAAA,CAAClC,MAAM;QAACgR,aAAa,EAAEA,CAAA,KAAM3L,cAAc,CAAC,CAACD,WAAW;MAAE;QAAAiJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE7DtM,OAAA;QAAM8L,SAAS,EAAC,mFAAmF;QAAAC,QAAA,eACjG/L,OAAA;UAAK8L,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAC/BzI,KAAK,iBACJtD,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;YACTC,OAAO,EAAE;cAAET,OAAO,EAAE,CAAC;cAAEI,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCM,OAAO,EAAE;cAAEV,OAAO,EAAE,CAAC;cAAEI,CAAC,EAAE;YAAE,CAAE;YAC9B7C,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAE7E/L,OAAA;cAAK8L,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC/L,OAAA;gBAAK8L,SAAS,EAAC,2BAA2B;gBAACsB,IAAI,EAAC,cAAc;gBAAC8B,OAAO,EAAC,WAAW;gBAAAnD,QAAA,eAChF/L,OAAA;kBACEmP,QAAQ,EAAC,SAAS;kBAClBC,CAAC,EAAC,yNAAyN;kBAC3NC,QAAQ,EAAC;gBAAS;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtM,OAAA;gBAAG8L,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAEzI;cAAK;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAEDtM,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;YACTC,OAAO,EAAE;cAAET,OAAO,EAAE;YAAE,CAAE;YACxBU,OAAO,EAAE;cAAEV,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAEa,QAAQ,EAAE;YAAI,CAAE;YAAAvD,QAAA,gBAE9B/L,OAAA;cAAK8L,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/F/L,OAAA;gBAAA+L,QAAA,gBACE/L,OAAA;kBAAI8L,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAEnE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtM,OAAA;kBAAG8L,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,GAAC,eAAa,EAAC,CAAA3F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiE,IAAI,KAAI,SAAS;gBAAA;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACNtM,OAAA;gBAAK8L,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,eACtC/L,OAAA;kBACEgL,KAAK,EAAExH,SAAU;kBACjB+L,QAAQ,EAAGC,CAAC,IAAK/L,YAAY,CAAC+L,CAAC,CAACC,MAAM,CAACzE,KAAK,CAAE;kBAC9Cc,SAAS,EAAC,mJAAmJ;kBAAAC,QAAA,gBAE7J/L,OAAA;oBAAQgL,KAAK,EAAC,MAAM;oBAAAe,QAAA,EAAC;kBAAW;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACzCtM,OAAA;oBAAQgL,KAAK,EAAC,OAAO;oBAAAe,QAAA,EAAC;kBAAY;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC3CtM,OAAA;oBAAQgL,KAAK,EAAC,KAAK;oBAAAe,QAAA,EAAC;kBAAQ;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtM,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;cACTC,OAAO,EAAE;gBAAET,OAAO,EAAE,CAAC;gBAAEI,CAAC,EAAE;cAAG,CAAE;cAC/BM,OAAO,EAAE;gBAAEV,OAAO,EAAE,CAAC;gBAAEI,CAAC,EAAE;cAAE,CAAE;cAC9B7C,SAAS,EAAC,oDAAoD;cAAAC,QAAA,eAE9D/L,OAAA;gBAAK8L,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnC/L,OAAA;kBACE0P,OAAO,EAAEA,CAAA,KAAM/L,YAAY,CAAC,UAAU,CAAE;kBACxCmI,SAAS,EAAE,gFACTpI,SAAS,KAAK,UAAU,GACpB,iCAAiC,GACjC,2FAA2F,EAC9F;kBAAAqI,QAAA,gBAEH/L,OAAA,CAACrB,UAAU;oBAACmN,SAAS,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,YAEjC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTtM,OAAA;kBACE0P,OAAO,EAAEA,CAAA,KAAM/L,YAAY,CAAC,UAAU,CAAE;kBACxCmI,SAAS,EAAE,gFACTpI,SAAS,KAAK,UAAU,GACpB,iCAAiC,GACjC,2FAA2F,EAC9F;kBAAAqI,QAAA,gBAEH/L,OAAA,CAACtB,aAAa;oBAACoN,SAAS,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,YAEpC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTtM,OAAA;kBACE0P,OAAO,EAAEA,CAAA,KAAM/L,YAAY,CAAC,YAAY,CAAE;kBAC1CmI,SAAS,EAAE,gFACTpI,SAAS,KAAK,YAAY,GACtB,iCAAiC,GACjC,2FAA2F,EAC9F;kBAAAqI,QAAA,gBAEH/L,OAAA,CAAC7B,OAAO;oBAAC2N,SAAS,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,oBAE9B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTtM,OAAA;kBACE0P,OAAO,EAAEA,CAAA,KAAM/L,YAAY,CAAC,SAAS,CAAE;kBACvCmI,SAAS,EAAE,gFACTpI,SAAS,KAAK,SAAS,GACnB,iCAAiC,GACjC,2FAA2F,EAC9F;kBAAAqI,QAAA,gBAEH/L,OAAA,CAAC3B,gBAAgB;oBAACyN,SAAS,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAEvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EAGZ5I,SAAS,KAAK,UAAU,iBACvB1D,OAAA,CAAAE,SAAA;cAAA6L,QAAA,eAEE/L,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;gBACTY,QAAQ,EAAEtB,SAAU;gBACpBW,OAAO,EAAC,QAAQ;gBAChBY,WAAW,EAAC,MAAM;gBAClBC,QAAQ,EAAE;kBAAEC,IAAI,EAAE;gBAAK,CAAE;gBACzBhE,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,gBAErE/L,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;kBACTY,QAAQ,EAAE5C,IAAK;kBACfjB,SAAS,EAAC,gJAAgJ;kBAAAC,QAAA,eAE1J/L,OAAA;oBAAK8L,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChC/L,OAAA;sBAAK8L,SAAS,EAAC,0JAA0J;sBAAAC,QAAA,eACvK/L,OAAA,CAAC5B,aAAa;wBAAC0N,SAAS,EAAC;sBAAwB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC,eACNtM,OAAA;sBAAA+L,QAAA,gBACE/L,OAAA;wBAAG8L,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAY;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACjEtM,OAAA;wBAAG8L,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAC7CnH,aAAa,CAACE,gBAAgB,CAACC,OAAO,GACtCH,aAAa,CAACE,gBAAgB,CAACE,SAAS,GACxCJ,aAAa,CAACE,gBAAgB,CAACG;sBAAS;wBAAAkH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC,eACJtM,OAAA;wBAAK8L,SAAS,EAAC,8CAA8C;wBAAAC,QAAA,gBAC3D/L,OAAA;0BAAM8L,SAAS,EAAC,wCAAwC;0BAAAC,QAAA,gBACtD/L,OAAA,CAACzB,eAAe;4BAACuN,SAAS,EAAC;0BAAc;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,KAAC,EAAC1H,aAAa,CAACE,gBAAgB,CAACC,OAAO,EAAC,UACvF;wBAAA;0BAAAoH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACPtM,OAAA;0BAAM8L,SAAS,EAAC,kCAAkC;0BAAAC,QAAA,gBAChD/L,OAAA,CAAC1B,aAAa;4BAACwN,SAAS,EAAC;0BAAc;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,KAAC,EAAC1H,aAAa,CAACE,gBAAgB,CAACE,SAAS,EAAC,YACvF;wBAAA;0BAAAmH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEbtM,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;kBACTY,QAAQ,EAAE5C,IAAK;kBACfjB,SAAS,EAAC,gJAAgJ;kBAAAC,QAAA,eAE1J/L,OAAA;oBAAK8L,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChC/L,OAAA;sBAAK8L,SAAS,EAAC,gIAAgI;sBAAAC,QAAA,eAC7I/L,OAAA,CAAC9B,OAAO;wBAAC4N,SAAS,EAAC;sBAAwB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC,eACNtM,OAAA;sBAAA+L,QAAA,gBACE/L,OAAA;wBAAG8L,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC7DtM,OAAA;wBAAG8L,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAC7C1C,MAAM,CAACC,MAAM,CAAC1E,aAAa,CAACO,eAAe,CAAC,CAAC+C,MAAM,CAAC,CAACuB,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC;sBAAC;wBAAAyC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvE,CAAC,eACJtM,OAAA;wBAAG8L,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EACtC1C,MAAM,CAACwD,IAAI,CAACjI,aAAa,CAACQ,kBAAkB,CAAC,CAACqC,MAAM,GAAG,CAAC,IACvD,GAAGsI,IAAI,CAACC,KAAK,CAAC,CAACpL,aAAa,CAACQ,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,IAC1DiE,MAAM,CAACC,MAAM,CAAC1E,aAAa,CAACQ,kBAAkB,CAAC,CAAC8C,MAAM,CAAC,CAACuB,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;AACjH,iCAAiCqG,IAAI,CAACC,KAAK,CAAC,CAACpL,aAAa,CAACQ,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAC5DiE,MAAM,CAACC,MAAM,CAAC1E,aAAa,CAACQ,kBAAkB,CAAC,CAAC8C,MAAM,CAAC,CAACuB,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;sBAAU;wBAAAyC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAE9F,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEbtM,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;kBACTY,QAAQ,EAAE5C,IAAK;kBACfjB,SAAS,EAAC,gJAAgJ;kBAAAC,QAAA,eAE1J/L,OAAA;oBAAK8L,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChC/L,OAAA;sBAAK8L,SAAS,EAAC,kIAAkI;sBAAAC,QAAA,eAC/I/L,OAAA,CAAC7B,OAAO;wBAAC2N,SAAS,EAAC;sBAAyB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACNtM,OAAA;sBAAA+L,QAAA,gBACE/L,OAAA;wBAAG8L,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAgB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACrEtM,OAAA;wBAAG8L,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAC7C1C,MAAM,CAACC,MAAM,CAAC1E,aAAa,CAACS,kBAAkB,CAAC,CAAC6C,MAAM,CAAC,CAACuB,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC;sBAAC;wBAAAyC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1E,CAAC,eACJtM,OAAA;wBAAG8L,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EACtC1C,MAAM,CAACwD,IAAI,CAACjI,aAAa,CAACS,kBAAkB,CAAC,CAACoC,MAAM,GAAG,CAAC,IACvD,QAAQ4B,MAAM,CAACuB,OAAO,CAAChG,aAAa,CAACS,kBAAkB,CAAC,CACrDmE,IAAI,CAAC,CAAC,GAAEC,CAAC,CAAC,EAAE,GAAEC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAAE;wBAAA0C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEvC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEbtM,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;kBACTY,QAAQ,EAAE5C,IAAK;kBACfjB,SAAS,EAAC,gJAAgJ;kBAAAC,QAAA,eAE1J/L,OAAA;oBAAK8L,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChC/L,OAAA;sBAAK8L,SAAS,EAAC,kIAAkI;sBAAAC,QAAA,eAC/I/L,OAAA,CAAC3B,gBAAgB;wBAACyN,SAAS,EAAC;sBAAyB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC,eACNtM,OAAA;sBAAA+L,QAAA,gBACE/L,OAAA;wBAAG8L,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAO;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC5DtM,OAAA;wBAAG8L,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAC7C,EAAA7J,uBAAA,GAAA0C,aAAa,CAACW,WAAW,cAAArD,uBAAA,uBAAzBA,uBAAA,CAA2BgE,YAAY,KAAI;sBAAC;wBAAAiG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C,CAAC,eACJtM,OAAA;wBAAK8L,SAAS,EAAC,8CAA8C;wBAAAC,QAAA,gBAC3D/L,OAAA;0BAAM8L,SAAS,EAAC,uCAAuC;0BAAAC,QAAA,gBACrD/L,OAAA,CAAC1B,aAAa;4BAACwN,SAAS,EAAC;0BAAc;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,KAAC,EAAC,EAAAnK,uBAAA,GAAAyC,aAAa,CAACW,WAAW,cAAApD,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BqD,kBAAkB,cAAApD,uBAAA,uBAA7CA,uBAAA,CAA+CqD,QAAQ,KAAI,CAAC,EAAC,WAC3G;wBAAA;0BAAA0G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACPtM,OAAA;0BAAM8L,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBACjD/L,OAAA,CAACzB,eAAe;4BAACuN,SAAS,EAAC;0BAAc;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,KAAC,EAAC,EAAAjK,uBAAA,GAAAuC,aAAa,CAACW,WAAW,cAAAlD,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BmD,kBAAkB,cAAAlD,uBAAA,uBAA7CA,uBAAA,CAA+CyC,OAAO,KAAI,CAAC,EAAC,UAC5G;wBAAA;0BAAAoH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,gBACb,CACH,EAGA5I,SAAS,KAAK,UAAU,iBACvB1D,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;cACTY,QAAQ,EAAEtB,SAAU;cACpBW,OAAO,EAAC,QAAQ;cAChBY,WAAW,EAAC,MAAM;cAClBC,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBhE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAGjD/L,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;gBACTY,QAAQ,EAAE5C,IAAK;gBACfjB,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAE7H/L,OAAA;kBAAK8L,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrD/L,OAAA;oBAAI8L,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxEtM,OAAA;oBACE0P,OAAO,EAAEA,CAAA,KAAMrE,eAAe,CAAC,cAAc,CAAE;oBAC/CS,SAAS,EAAC,iIAAiI;oBAAAC,QAAA,EAC5I;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNtM,OAAA;kBAAK8L,SAAS,EAAC,MAAM;kBAAAC,QAAA,GAClBnI,SAAS,CAACE,YAAY,KAAK,UAAU,iBACpC9D,OAAA,CAACL,QAAQ;oBACP2H,IAAI,EAAEiF,qBAAsB;oBAC5B0D,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BC,OAAO,EAAE;wBACPC,MAAM,EAAE;0BAAEC,QAAQ,EAAE;wBAAS;sBAC/B;oBACF;kBAAE;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF,EACA1I,SAAS,CAACE,YAAY,KAAK,KAAK,iBAC/B9D,OAAA,CAACN,GAAG;oBACF4H,IAAI,EAAEiF,qBAAsB;oBAC5B0D,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BC,OAAO,EAAE;wBACPC,MAAM,EAAE;0BAAEC,QAAQ,EAAE;wBAAS;sBAC/B;oBACF;kBAAE;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF,EACA1I,SAAS,CAACE,YAAY,KAAK,KAAK,iBAC/B9D,OAAA,CAACP,GAAG;oBACF6H,IAAI,EAAEiF,qBAAsB;oBAC5B0D,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BI,MAAM,EAAE;wBACN3B,CAAC,EAAE;0BAAE4B,WAAW,EAAE;wBAAK;sBACzB;oBACF;kBAAE;oBAAApE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAGbtM,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;gBACTY,QAAQ,EAAE5C,IAAK;gBACfjB,SAAS,EAAC,sGAAsG;gBAAAC,QAAA,gBAEhH/L,OAAA;kBAAK8L,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrD/L,OAAA;oBAAI8L,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAsB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5EtM,OAAA;oBACE0P,OAAO,EAAEA,CAAA,KAAMrE,eAAe,CAAC,sBAAsB,CAAE;oBACvDS,SAAS,EAAC,iIAAiI;oBAAAC,QAAA,EAC5I;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNtM,OAAA;kBAAK8L,SAAS,EAAC,MAAM;kBAAAC,QAAA,GAClBnI,SAAS,CAACG,oBAAoB,KAAK,MAAM,iBACxC/D,OAAA,CAACJ,IAAI;oBACH0H,IAAI,EAAEwF,wBAAyB;oBAC/BmD,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BI,MAAM,EAAE;wBACN3B,CAAC,EAAE;0BAAE4B,WAAW,EAAE;wBAAK;sBACzB,CAAC;sBACDJ,OAAO,EAAE;wBACPC,MAAM,EAAE;0BAAEC,QAAQ,EAAE;wBAAS;sBAC/B;oBACF;kBAAE;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF,EACA1I,SAAS,CAACG,oBAAoB,KAAK,KAAK,iBACvC/D,OAAA,CAACP,GAAG;oBACF6H,IAAI,EAAEwF,wBAAyB;oBAC/BmD,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BI,MAAM,EAAE;wBACN3B,CAAC,EAAE;0BAAE4B,WAAW,EAAE;wBAAK;sBACzB,CAAC;sBACDJ,OAAO,EAAE;wBACPC,MAAM,EAAE;0BAAEC,QAAQ,EAAE;wBAAS;sBAC/B;oBACF;kBAAE;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF,EACA1I,SAAS,CAACG,oBAAoB,KAAK,MAAM,iBACxC/D,OAAA,CAACJ,IAAI;oBACH0H,IAAI,EAAEwF,wBAAyB;oBAC/BmD,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BI,MAAM,EAAE;wBACN3B,CAAC,EAAE;0BAAE4B,WAAW,EAAE;wBAAK;sBACzB,CAAC;sBACDJ,OAAO,EAAE;wBACPC,MAAM,EAAE;0BAAEC,QAAQ,EAAE;wBAAS;sBAC/B,CAAC;sBACDG,QAAQ,EAAE;wBACRC,IAAI,EAAE;0BACJrD,IAAI,EAAE;wBACR;sBACF;oBACF;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACb,EAGA5I,SAAS,KAAK,UAAU,iBACvB1D,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;cACTY,QAAQ,EAAEtB,SAAU;cACpBW,OAAO,EAAC,QAAQ;cAChBY,WAAW,EAAC,MAAM;cAClBC,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBhE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAGjD/L,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;gBACTY,QAAQ,EAAE5C,IAAK;gBACfjB,SAAS,EAAC,sGAAsG;gBAAAC,QAAA,gBAEhH/L,OAAA;kBAAK8L,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrD/L,OAAA;oBAAI8L,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAwB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9EtM,OAAA;oBACE0P,OAAO,EAAEA,CAAA,KAAMrE,eAAe,CAAC,KAAK,CAAE;oBACtCS,SAAS,EAAC,iIAAiI;oBAAAC,QAAA,EAC5I;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNtM,OAAA;kBAAK8L,SAAS,EAAC,MAAM;kBAAAC,QAAA,GAClBnI,SAAS,CAACI,GAAG,KAAK,KAAK,iBACtBhE,OAAA,CAACP,GAAG;oBACF6H,IAAI,EAAE+F,mBAAoB;oBAC1B4C,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BI,MAAM,EAAE;wBACN3B,CAAC,EAAE;0BAAE4B,WAAW,EAAE;wBAAK;sBACzB;oBACF;kBAAE;oBAAApE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF,EACA1I,SAAS,CAACI,GAAG,KAAK,MAAM,iBACvBhE,OAAA,CAACJ,IAAI;oBACH0H,IAAI,EAAE+F,mBAAoB;oBAC1B4C,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BI,MAAM,EAAE;wBACN3B,CAAC,EAAE;0BAAE4B,WAAW,EAAE;wBAAK;sBACzB;oBACF;kBAAE;oBAAApE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAGbtM,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;gBACTY,QAAQ,EAAE5C,IAAK;gBACfjB,SAAS,EAAC,sGAAsG;gBAAAC,QAAA,gBAEhH/L,OAAA;kBAAK8L,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrD/L,OAAA;oBAAI8L,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAc;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpEtM,OAAA;oBACE0P,OAAO,EAAEA,CAAA,KAAMrE,eAAe,CAAC,QAAQ,CAAE;oBACzCS,SAAS,EAAC,iIAAiI;oBAAAC,QAAA,EAC5I;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNtM,OAAA;kBAAK8L,SAAS,EAAC,MAAM;kBAAAC,QAAA,GAClBnI,SAAS,CAACK,MAAM,KAAK,KAAK,iBACzBjE,OAAA,CAACN,GAAG;oBACF4H,IAAI,EAAEgG,sBAAuB;oBAC7B2C,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BC,OAAO,EAAE;wBACPC,MAAM,EAAE;0BAAEC,QAAQ,EAAE;wBAAS;sBAC/B;oBACF;kBAAE;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF,EACA1I,SAAS,CAACK,MAAM,KAAK,UAAU,iBAC9BjE,OAAA,CAACL,QAAQ;oBACP2H,IAAI,EAAEgG,sBAAuB;oBAC7B2C,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BC,OAAO,EAAE;wBACPC,MAAM,EAAE;0BAAEC,QAAQ,EAAE;wBAAS;sBAC/B;oBACF;kBAAE;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF,EACA1I,SAAS,CAACK,MAAM,KAAK,KAAK,iBACzBjE,OAAA,CAACP,GAAG;oBACF6H,IAAI,EAAEgG,sBAAuB;oBAC7B2C,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BI,MAAM,EAAE;wBACN3B,CAAC,EAAE;0BAAE4B,WAAW,EAAE;wBAAK;sBACzB;oBACF;kBAAE;oBAAApE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAGbtM,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;gBACTY,QAAQ,EAAE5C,IAAK;gBACfjB,SAAS,EAAC,oHAAoH;gBAAAC,QAAA,gBAE9H/L,OAAA;kBAAK8L,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrD/L,OAAA;oBAAI8L,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAA0B;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChFtM,OAAA;oBACE0P,OAAO,EAAEA,CAAA,KAAMrE,eAAe,CAAC,UAAU,CAAE;oBAC3CS,SAAS,EAAC,iIAAiI;oBAAAC,QAAA,EAC5I;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNtM,OAAA;kBAAK8L,SAAS,EAAC,MAAM;kBAAAC,QAAA,GAClBnI,SAAS,CAACO,QAAQ,KAAK,UAAU,iBAChCnE,OAAA,CAACL,QAAQ;oBACP2H,IAAI,EAAEkG,kBAAmB;oBACzByC,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BC,OAAO,EAAE;wBACPC,MAAM,EAAE;0BAAEC,QAAQ,EAAE;wBAAS;sBAC/B;oBACF;kBAAE;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF,EACA1I,SAAS,CAACO,QAAQ,KAAK,KAAK,iBAC3BnE,OAAA,CAACN,GAAG;oBACF4H,IAAI,EAAEkG,kBAAmB;oBACzByC,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BC,OAAO,EAAE;wBACPC,MAAM,EAAE;0BAAEC,QAAQ,EAAE;wBAAS;sBAC/B;oBACF;kBAAE;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF,EACA1I,SAAS,CAACO,QAAQ,KAAK,KAAK,iBAC3BnE,OAAA,CAACP,GAAG;oBACF6H,IAAI,EAAEkG,kBAAmB;oBACzByC,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BI,MAAM,EAAE;wBACN3B,CAAC,EAAE;0BAAE4B,WAAW,EAAE;wBAAK;sBACzB;oBACF;kBAAE;oBAAApE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACb,EAGA5I,SAAS,KAAK,YAAY,iBACzB1D,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;cACTY,QAAQ,EAAEtB,SAAU;cACpBW,OAAO,EAAC,QAAQ;cAChBY,WAAW,EAAC,MAAM;cAClBC,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBhE,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBAGlC/L,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;gBACTY,QAAQ,EAAE5C,IAAK;gBACfjB,SAAS,EAAC,sGAAsG;gBAAAC,QAAA,gBAEhH/L,OAAA;kBAAK8L,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrD/L,OAAA;oBAAI8L,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAmC;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzFtM,OAAA;oBACE0P,OAAO,EAAEA,CAAA,KAAMrE,eAAe,CAAC,YAAY,CAAE;oBAC7CS,SAAS,EAAC,iIAAiI;oBAAAC,QAAA,EAC5I;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNtM,OAAA;kBAAK8L,SAAS,EAAC,MAAM;kBAAAC,QAAA,GAClBnI,SAAS,CAACM,UAAU,KAAK,KAAK,iBAC7BlE,OAAA,CAACP,GAAG;oBACF6H,IAAI,EAAEiG,sBAAuB;oBAC7B0C,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BI,MAAM,EAAE;wBACN3B,CAAC,EAAE;0BAAE4B,WAAW,EAAE;wBAAK;sBACzB,CAAC;sBACDG,SAAS,EAAE,GAAG,CAAE;oBAClB;kBAAE;oBAAAvE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF,EACA1I,SAAS,CAACM,UAAU,KAAK,KAAK,iBAC7BlE,OAAA,CAACN,GAAG;oBACF4H,IAAI,EAAEiG,sBAAuB;oBAC7B0C,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BC,OAAO,EAAE;wBACPC,MAAM,EAAE;0BAAEC,QAAQ,EAAE;wBAAS;sBAC/B;oBACF;kBAAE;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF,EACA1I,SAAS,CAACM,UAAU,KAAK,UAAU,iBAClClE,OAAA,CAACL,QAAQ;oBACP2H,IAAI,EAAEiG,sBAAuB;oBAC7B0C,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BC,OAAO,EAAE;wBACPC,MAAM,EAAE;0BAAEC,QAAQ,EAAE;wBAAS;sBAC/B;oBACF;kBAAE;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAGbtM,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;gBACTY,QAAQ,EAAE5C,IAAK;gBACfjB,SAAS,EAAC,sGAAsG;gBAAAC,QAAA,gBAEhH/L,OAAA;kBAAK8L,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrD/L,OAAA;oBAAI8L,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvEtM,OAAA;oBACE0P,OAAO,EAAEA,CAAA,KAAMrE,eAAe,CAAC,cAAc,CAAE;oBAC/CS,SAAS,EAAC,iIAAiI;oBAAAC,QAAA,EAC5I;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNtM,OAAA;kBAAK8L,SAAS,EAAC,MAAM;kBAAAC,QAAA,GAClBnI,SAAS,CAACE,YAAY,KAAK,UAAU,iBACpC9D,OAAA,CAACL,QAAQ;oBACP2H,IAAI,EAAEsF,oBAAqB;oBAC3BqD,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BC,OAAO,EAAE;wBACPC,MAAM,EAAE;0BAAEC,QAAQ,EAAE;wBAAS;sBAC/B;oBACF;kBAAE;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF,EACA1I,SAAS,CAACE,YAAY,KAAK,KAAK,iBAC/B9D,OAAA,CAACN,GAAG;oBACF4H,IAAI,EAAEsF,oBAAqB;oBAC3BqD,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BC,OAAO,EAAE;wBACPC,MAAM,EAAE;0BAAEC,QAAQ,EAAE;wBAAS;sBAC/B;oBACF;kBAAE;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF,EACA1I,SAAS,CAACE,YAAY,KAAK,KAAK,iBAC/B9D,OAAA,CAACP,GAAG;oBACF6H,IAAI,EAAEsF,oBAAqB;oBAC3BqD,OAAO,EAAE;sBACPC,mBAAmB,EAAE,KAAK;sBAC1BI,MAAM,EAAE;wBACN3B,CAAC,EAAE;0BAAE4B,WAAW,EAAE;wBAAK;sBACzB;oBACF;kBAAE;oBAAApE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACb,EAGA5I,SAAS,KAAK,SAAS,iBACtB1D,OAAA,CAAAE,SAAA;cAAA6L,QAAA,gBAEE/L,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;gBACTY,QAAQ,EAAEtB,SAAU;gBACpBW,OAAO,EAAC,QAAQ;gBAChBY,WAAW,EAAC,MAAM;gBAClBC,QAAQ,EAAE;kBAAEC,IAAI,EAAE;gBAAK,CAAE;gBACzBhE,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBAEtD/L,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;kBACTY,QAAQ,EAAE5C,IAAK;kBACfjB,SAAS,EAAC,sGAAsG;kBAAAC,QAAA,eAEhH/L,OAAA;oBAAK8L,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrC/L,OAAA;sBAAK8L,SAAS,EAAC,uFAAuF;sBAAAC,QAAA,eACpG/L,OAAA,CAAC3B,gBAAgB;wBAACyN,SAAS,EAAC;sBAAwB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACNtM,OAAA;sBAAA+L,QAAA,gBACE/L,OAAA;wBAAI8L,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAa;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACpEtM,OAAA;wBAAG8L,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAC7C,EAAAxJ,uBAAA,GAAAqC,aAAa,CAACW,WAAW,cAAAhD,uBAAA,uBAAzBA,uBAAA,CAA2B2D,YAAY,KAAI;sBAAC;wBAAAiG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEbtM,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;kBACTY,QAAQ,EAAE5C,IAAK;kBACfjB,SAAS,EAAC,sGAAsG;kBAAAC,QAAA,gBAEhH/L,OAAA;oBAAK8L,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrC/L,OAAA;sBAAK8L,SAAS,EAAC,uFAAuF;sBAAAC,QAAA,eACpG/L,OAAA,CAAC1B,aAAa;wBAACwN,SAAS,EAAC;sBAAwB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC,eACNtM,OAAA;sBAAA+L,QAAA,gBACE/L,OAAA;wBAAI8L,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAe;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACtEtM,OAAA;wBAAG8L,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,GAC7C,EAAAvJ,uBAAA,GAAAoC,aAAa,CAACW,WAAW,cAAA/C,uBAAA,uBAAzBA,uBAAA,CAA2BwD,cAAc,KAAI,CAAC,EAAC,GAClD;sBAAA;wBAAAmG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNtM,OAAA;oBAAK8L,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,eAC3D/L,OAAA;sBACE8L,SAAS,EAAC,kCAAkC;sBAC5C6E,KAAK,EAAE;wBAAEC,KAAK,EAAE,GAAG,EAAAnO,uBAAA,GAAAmC,aAAa,CAACW,WAAW,cAAA9C,uBAAA,uBAAzBA,uBAAA,CAA2BuD,cAAc,KAAI,CAAC;sBAAI;oBAAE;sBAAAmG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEbtM,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;kBACTY,QAAQ,EAAE5C,IAAK;kBACfjB,SAAS,EAAC,sGAAsG;kBAAAC,QAAA,gBAEhH/L,OAAA;oBAAK8L,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrC/L,OAAA;sBAAK8L,SAAS,EAAC,wFAAwF;sBAAAC,QAAA,eACrG/L,OAAA,CAACxB,aAAa;wBAACsN,SAAS,EAAC;sBAAwB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC,eACNtM,OAAA;sBAAA+L,QAAA,gBACE/L,OAAA;wBAAI8L,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAsB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC7EtM,OAAA;wBAAG8L,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,GAC7C,EAAArJ,uBAAA,GAAAkC,aAAa,CAACW,WAAW,cAAA7C,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BkD,cAAc,cAAAjD,uBAAA,uBAAzCA,uBAAA,CAA2CkD,mBAAmB,KAAI,CAAC,EAAC,IACvE;sBAAA;wBAAAsG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNtM,OAAA;oBAAK8L,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAClBH,WAAW,CAAC,EAAAhJ,uBAAA,GAAAgC,aAAa,CAACW,WAAW,cAAA3C,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BgD,cAAc,cAAA/C,uBAAA,uBAAzCA,uBAAA,CAA2CgD,mBAAmB,KAAI,CAAC;kBAAC;oBAAAsG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEbtM,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;kBACTY,QAAQ,EAAE5C,IAAK;kBACfjB,SAAS,EAAC,sGAAsG;kBAAAC,QAAA,gBAEhH/L,OAAA;oBAAK8L,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrC/L,OAAA;sBAAK8L,SAAS,EAAC,uFAAuF;sBAAAC,QAAA,eACpG/L,OAAA,CAACxB,aAAa;wBAACsN,SAAS,EAAC;sBAAwB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC,eACNtM,OAAA;sBAAA+L,QAAA,gBACE/L,OAAA;wBAAI8L,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAwB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC/EtM,OAAA;wBAAG8L,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,GAC7C,EAAAjJ,uBAAA,GAAA8B,aAAa,CAACW,WAAW,cAAAzC,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B8C,cAAc,cAAA7C,uBAAA,uBAAzCA,uBAAA,CAA2C+C,qBAAqB,KAAI,CAAC,EAAC,IACzE;sBAAA;wBAAAqG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNtM,OAAA;oBAAK8L,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAClBH,WAAW,CAAC,EAAA5I,uBAAA,GAAA4B,aAAa,CAACW,WAAW,cAAAvC,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B4C,cAAc,cAAA3C,uBAAA,uBAAzCA,uBAAA,CAA2C6C,qBAAqB,KAAI,CAAC;kBAAC;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGbtM,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;gBACTY,QAAQ,EAAEtB,SAAU;gBACpBW,OAAO,EAAC,QAAQ;gBAChBY,WAAW,EAAC,MAAM;gBAClBC,QAAQ,EAAE;kBAAEC,IAAI,EAAE;gBAAK,CAAE;gBACzBhE,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBAGjD/L,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;kBACTY,QAAQ,EAAE5C,IAAK;kBACfjB,SAAS,EAAC,sGAAsG;kBAAAC,QAAA,gBAEhH/L,OAAA;oBAAK8L,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD/L,OAAA;sBAAI8L,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAA0B;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChFtM,OAAA;sBACE0P,OAAO,EAAEA,CAAA,KAAMrE,eAAe,CAAC,cAAc,CAAE;sBAC/CS,SAAS,EAAC,iIAAiI;sBAAAC,QAAA,EAC5I;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNtM,OAAA;oBAAK8L,SAAS,EAAC,MAAM;oBAAC+E,GAAG,EAAErM,cAAe;oBAAAuH,QAAA,GACvCnI,SAAS,CAACQ,YAAY,KAAK,UAAU,iBACpCpE,OAAA,CAACL,QAAQ;sBACP2H,IAAI,EAAEmG,gBAAiB;sBACvBwC,OAAO,EAAE;wBACPC,mBAAmB,EAAE,KAAK;wBAC1BC,OAAO,EAAE;0BACPC,MAAM,EAAE;4BAAEC,QAAQ,EAAE;0BAAS;wBAC/B;sBACF;oBAAE;sBAAAlE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACF,EACA1I,SAAS,CAACQ,YAAY,KAAK,KAAK,iBAC/BpE,OAAA,CAACN,GAAG;sBACF4H,IAAI,EAAEmG,gBAAiB;sBACvBwC,OAAO,EAAE;wBACPC,mBAAmB,EAAE,KAAK;wBAC1BC,OAAO,EAAE;0BACPC,MAAM,EAAE;4BAAEC,QAAQ,EAAE;0BAAS;wBAC/B;sBACF;oBAAE;sBAAAlE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACF,EACA1I,SAAS,CAACQ,YAAY,KAAK,KAAK,iBAC/BpE,OAAA,CAACP,GAAG;sBACF6H,IAAI,EAAEmG,gBAAiB;sBACvBwC,OAAO,EAAE;wBACPC,mBAAmB,EAAE,KAAK;wBAC1BI,MAAM,EAAE;0BACN3B,CAAC,EAAE;4BAAE4B,WAAW,EAAE;0BAAK;wBACzB;sBACF;oBAAE;sBAAApE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACF;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGbtM,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;kBACTY,QAAQ,EAAE5C,IAAK;kBACfjB,SAAS,EAAC,sGAAsG;kBAAAC,QAAA,gBAEhH/L,OAAA;oBAAK8L,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD/L,OAAA;sBAAI8L,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAAyB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/EtM,OAAA;sBACE0P,OAAO,EAAEA,CAAA,KAAMrE,eAAe,CAAC,kBAAkB,CAAE;sBACnDS,SAAS,EAAC,iIAAiI;sBAAAC,QAAA,EAC5I;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNtM,OAAA;oBAAK8L,SAAS,EAAC,MAAM;oBAAC+E,GAAG,EAAEpM,iBAAkB;oBAAAsH,QAAA,GAC1CnI,SAAS,CAACS,gBAAgB,KAAK,KAAK,iBACnCrE,OAAA,CAACP,GAAG;sBACF6H,IAAI,EAAEoG,mBAAoB;sBAC1BuC,OAAO,EAAE;wBACPC,mBAAmB,EAAE,KAAK;wBAC1BI,MAAM,EAAE;0BACNQ,CAAC,EAAE;4BAAEC,OAAO,EAAE;0BAAK,CAAC;0BACpBpC,CAAC,EAAE;4BAAEoC,OAAO,EAAE,IAAI;4BAAER,WAAW,EAAE;0BAAK;wBACxC;sBACF;oBAAE;sBAAApE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACF,EACA1I,SAAS,CAACS,gBAAgB,KAAK,MAAM,iBACpCrE,OAAA,CAACJ,IAAI;sBACH0H,IAAI,EAAEoG,mBAAoB;sBAC1BuC,OAAO,EAAE;wBACPC,mBAAmB,EAAE,KAAK;wBAC1BI,MAAM,EAAE;0BACN3B,CAAC,EAAE;4BAAE4B,WAAW,EAAE;0BAAK;wBACzB;sBACF;oBAAE;sBAAApE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACF;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGbtM,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;kBACTY,QAAQ,EAAE5C,IAAK;kBACfjB,SAAS,EAAC,sGAAsG;kBAAAC,QAAA,gBAEhH/L,OAAA;oBAAK8L,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD/L,OAAA;sBAAI8L,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAAsB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC5EtM,OAAA;sBACE0P,OAAO,EAAEA,CAAA,KAAMrE,eAAe,CAAC,eAAe,CAAE;sBAChDS,SAAS,EAAC,iIAAiI;sBAAAC,QAAA,EAC5I;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNtM,OAAA;oBAAK8L,SAAS,EAAC,MAAM;oBAAC+E,GAAG,EAAElM,sBAAuB;oBAAAoH,QAAA,GAC/CnI,SAAS,CAACW,aAAa,KAAK,OAAO,iBAClCvE,OAAA,CAACF,KAAK;sBACJwH,IAAI,EAAEyG,iBAAkB;sBACxBkC,OAAO,EAAE;wBACPC,mBAAmB,EAAE,KAAK;wBAC1BI,MAAM,EAAE;0BACN1I,CAAC,EAAE;4BACD2I,WAAW,EAAE,IAAI;4BACjBS,GAAG,EAAE,CAAC;4BACNC,KAAK,EAAE;8BACLC,QAAQ,EAAE;4BACZ;0BACF;wBACF;sBACF;oBAAE;sBAAA/E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACF,EACA1I,SAAS,CAACW,aAAa,KAAK,KAAK,iBAChCvE,OAAA,CAACP,GAAG;sBACF6H,IAAI,EAAEyG,iBAAkB;sBACxBkC,OAAO,EAAE;wBACPC,mBAAmB,EAAE,KAAK;wBAC1BI,MAAM,EAAE;0BACN3B,CAAC,EAAE;4BACD4B,WAAW,EAAE,IAAI;4BACjBS,GAAG,EAAE,CAAC;4BACNC,KAAK,EAAE;8BACLC,QAAQ,EAAE;4BACZ;0BACF;wBACF;sBACF;oBAAE;sBAAA/E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACF,EACA1I,SAAS,CAACW,aAAa,KAAK,WAAW,iBACtCvE,OAAA,CAACH,SAAS;sBACRyH,IAAI,EAAEyG,iBAAkB;sBACxBkC,OAAO,EAAE;wBACPC,mBAAmB,EAAE,KAAK;wBAC1BI,MAAM,EAAE;0BACN1I,CAAC,EAAE;4BACD2I,WAAW,EAAE,IAAI;4BACjBS,GAAG,EAAE,CAAC;4BACNC,KAAK,EAAE;8BACLC,QAAQ,EAAE;4BACZ;0BACF;wBACF;sBACF;oBAAE;sBAAA/E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACF;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGbtM,OAAA,CAAC/B,MAAM,CAAC8Q,GAAG;kBACTY,QAAQ,EAAE5C,IAAK;kBACfjB,SAAS,EAAC,sGAAsG;kBAAAC,QAAA,gBAEhH/L,OAAA;oBAAK8L,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD/L,OAAA;sBAAI8L,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAAsB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC5EtM,OAAA;sBACE0P,OAAO,EAAEA,CAAA,KAAMrE,eAAe,CAAC,aAAa,CAAE;sBAC9CS,SAAS,EAAC,iIAAiI;sBAAAC,QAAA,EAC5I;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNtM,OAAA;oBAAK8L,SAAS,EAAC,MAAM;oBAAC+E,GAAG,EAAEnM,mBAAoB;oBAAAqH,QAAA,GAC5CnI,SAAS,CAACU,WAAW,KAAK,MAAM,iBAC/BtE,OAAA,CAACJ,IAAI;sBACH0H,IAAI,EAAEwG,eAAgB;sBACtBmC,OAAO,EAAE;wBACPC,mBAAmB,EAAE,KAAK;wBAC1BI,MAAM,EAAE;0BACN3B,CAAC,EAAE;4BAAE4B,WAAW,EAAE;0BAAK;wBACzB;sBACF;oBAAE;sBAAApE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACF,EACA1I,SAAS,CAACU,WAAW,KAAK,KAAK,iBAC9BtE,OAAA,CAACP,GAAG;sBACF6H,IAAI,EAAEwG,eAAgB;sBACtBmC,OAAO,EAAE;wBACPC,mBAAmB,EAAE,KAAK;wBAC1BI,MAAM,EAAE;0BACN3B,CAAC,EAAE;4BAAE4B,WAAW,EAAE;0BAAK;wBACzB;sBACF;oBAAE;sBAAApE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACF;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,eACb,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjM,EAAA,CA14CID,SAAS;EAAA,QA6CI1C,WAAW,EACJG,OAAO;AAAA;AAAAsT,EAAA,GA9C3B/Q,SAAS;AA44Cf,eAAeA,SAAS;AAAC,IAAA+Q,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}