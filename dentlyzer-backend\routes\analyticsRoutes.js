const express = require('express');
const router = express.Router();
const Patient = require('../models/Patient');
const Appointment = require('../models/Appointment');
const Review = require('../models/Review');
const verifyToken = require('../middleware/auth');

// Analytics endpoint for students
router.get('/', verifyToken, async (req, res) => {
  try {
    const studentId = req.user.studentId || req.user.id;

    // Appointment statistics
    const appointments = await Appointment.find({ doctor: studentId, doctorModel: 'Student' });
    
    const appointmentStats = {
      pending: appointments.filter(a => a.status === 'pending').length,
      completed: appointments.filter(a => a.status === 'completed').length,
      cancelled: appointments.filter(a => a.status === 'cancelled').length,
    };

    // Appointment types
    const appointmentTypes = appointments.reduce((acc, appt) => {
      acc[appt.type] = (acc[appt.type] || 0) + 1;
      return acc;
    }, {});

    // Patient demographics
    const patients = await Patient.find({ drId: studentId });

    const ageDistribution = patients.reduce((acc, patient) => {
      const ageRange = Math.floor(patient.age / 10) * 10;
      acc[`${ageRange}-${ageRange + 9}`] = (acc[`${ageRange}-${ageRange + 9}`] || 0) + 1;
      return acc;
    }, {});

    const genderDistribution = patients.reduce((acc, patient) => {
      acc[patient.gender] = (acc[patient.gender] || 0) + 1;
      return acc;
    }, {});

    // Procedure frequency from treatment sheets
    const procedureFrequency = {};
    patients.forEach(patient => {
      if (patient.treatmentSheets && patient.treatmentSheets.length > 0) {
        patient.treatmentSheets.forEach(sheet => {
          procedureFrequency[sheet.type] = (procedureFrequency[sheet.type] || 0) + 1;
        });
      }
    });

    // Chronic disease prevalence
    const chronicDiseasePrevalence = patients.reduce((acc, patient) => {
      patient.medicalInfo.chronicDiseases.forEach(disease => {
        acc[disease] = (acc[disease] || 0) + 1;
      });
      return acc;
    }, {});

    res.json({
      appointmentStats,
      appointmentTypes,
      ageDistribution,
      genderDistribution,
      procedureFrequency,
      chronicDiseasePrevalence,
    });
  } catch (error) {
    console.error('Analytics error:', error);
    res.status(500).json({ message: 'Server error while fetching analytics' });
  }
});

module.exports = router;